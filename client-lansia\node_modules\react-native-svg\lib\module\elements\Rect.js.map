{"version": 3, "names": ["React", "withoutXY", "<PERSON><PERSON><PERSON>", "RNSVGRect", "Rect", "displayName", "defaultProps", "x", "y", "width", "height", "render", "props", "rx", "ry", "rectProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Rect.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA6B;AAEvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,+BAA+B;AAarD,eAAe,MAAMC,IAAI,SAASF,KAAK,CAAY;EACjD,OAAOG,WAAW,GAAG,MAAM;EAE3B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEL,CAAC;MAAEC,CAAC;MAAEC,KAAK;MAAEC,MAAM;MAAEG,EAAE;MAAEC;IAAG,CAAC,GAAGF,KAAK;IAC7C,MAAMG,SAAS,GAAG;MAAER,CAAC;MAAEC,CAAC;MAAEC,KAAK;MAAEC,MAAM;MAAEG,EAAE;MAAEC;IAAG,CAAC;IACjD,oBACEd,KAAA,CAAAgB,aAAA,CAACb,SAAS,EAAAc,QAAA;MACRC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DjB,SAAS,CAAC,IAAI,EAAEW,KAAK,CAAC,EACtBG,SAAS,CACd,CAAC;EAEN;AACF", "ignoreList": []}