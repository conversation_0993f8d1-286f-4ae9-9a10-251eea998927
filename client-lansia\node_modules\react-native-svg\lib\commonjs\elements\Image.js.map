{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_extractViewBox", "_extractProps", "_Shape", "_interopRequireDefault", "_ImageNativeComponent", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "spacesRegExp", "SvgImage", "<PERSON><PERSON><PERSON>", "displayName", "defaultProps", "x", "y", "width", "height", "preserveAspectRatio", "render", "props", "xlinkHref", "href", "onLoad", "modes", "trim", "split", "align", "meetOrSlice", "imageProps", "meetOrSliceTypes", "alignEnum", "src", "Image", "resolveAssetSource", "uri", "createElement", "ref", "refMethod", "withoutXY", "exports"], "sourceRoot": "../../../src", "sources": ["elements/Image.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,qBAAA,GAAAD,sBAAA,CAAAL,OAAA;AAAwD,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAExD,MAAMG,YAAY,GAAG,KAAK;AAcX,MAAMC,QAAQ,SAASC,cAAK,CAAa;EACtD,OAAOC,WAAW,GAAG,OAAO;EAE5B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,mBAAmB,EAAE;EACvB,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MACJF,mBAAmB;MACnBJ,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNI,SAAS;MACTC,IAAI,GAAGD,SAAS;MAChBE;IACF,CAAC,GAAGH,KAAK;IACT,MAAMI,KAAK,GAAGN,mBAAmB,GAC7BA,mBAAmB,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAACjB,YAAY,CAAC,GAC9C,EAAE;IACN,MAAMkB,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMI,WAA2D,GAC/DJ,KAAK,CAAC,CAAC,CAAC;IACV,MAAMK,UAAU,GAAG;MACjBf,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNM,MAAM;MACNK,WAAW,EAAEE,gCAAgB,CAACF,WAAW,CAAC,IAAI,CAAC;MAC/CD,KAAK,EAAEI,yBAAS,CAACJ,KAAK,CAAC,IAAI,UAAU;MACrCK,GAAG,EAAE,CAACV,IAAI,GACN,IAAI,GACJW,kBAAK,CAACC,kBAAkB,CACtB,OAAOZ,IAAI,KAAK,QAAQ,GAAG;QAAEa,GAAG,EAAEb;MAAK,CAAC,GAAGA,IAC7C;IACN,CAAC;IACD,oBACEhD,KAAA,CAAA8D,aAAA,CAACtD,qBAAA,CAAAG,OAAU,EAAAkB,QAAA;MACTkC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAwC;IAAE,GACnE,IAAAE,uBAAS,EAAC,IAAI,EAAEnB,KAAK,CAAC,EACtBS,UAAU,CACf,CAAC;EAEN;AACF;AAACW,OAAA,CAAAvD,OAAA,GAAAyB,QAAA", "ignoreList": []}