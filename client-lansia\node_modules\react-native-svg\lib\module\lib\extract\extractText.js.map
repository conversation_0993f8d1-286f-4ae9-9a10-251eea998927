{"version": 3, "names": ["React", "Children", "extractLengthList", "pickNotNil", "fontRegExp", "fontFamilyPrefix", "fontFamilySuffix", "commaReg", "cachedFontObjectsFromString", "extractSingleFontFamily", "fontFamilyString", "split", "replace", "parseFontString", "font", "Object", "prototype", "hasOwnProperty", "call", "match", "exec", "isBold", "isItalic", "fontSize", "fontWeight", "fontStyle", "fontFamily", "extractFont", "props", "fontData", "fontVariant", "fontStretch", "textAnchor", "textDecoration", "letterSpacing", "wordSpacing", "kerning", "fontFeatureSettings", "fontVariantLigatures", "fontVariationSettings", "ownedFont", "baseFont", "TSpan", "setTSpan", "TSpanImplementation", "<PERSON><PERSON><PERSON><PERSON>", "child", "createElement", "String", "extractText", "container", "x", "y", "dx", "dy", "rotate", "children", "inlineSize", "baselineShift", "verticalAlign", "alignmentBaseline", "textChil<PERSON>n", "count", "Array", "isArray", "map", "content"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractText.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,UAAU,QAAQ,SAAS;AAGpC,MAAMC,UAAU,GACd,mHAAmH;AACrH,MAAMC,gBAAgB,GAAG,UAAU;AACnC,MAAMC,gBAAgB,GAAG,UAAU;AACnC,MAAMC,QAAQ,GAAG,UAAU;AAE3B,MAAMC,2BAOL,GAAG,CAAC,CAAC;AAEN,SAASC,uBAAuBA,CAACC,gBAAyB,EAAE;EAC1D;EACA;EACA;EACA,OAAOA,gBAAgB,GACnBA,gBAAgB,CACbC,KAAK,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClBK,OAAO,CAACP,gBAAgB,EAAE,EAAE,CAAC,CAC7BO,OAAO,CAACN,gBAAgB,EAAE,EAAE,CAAC,GAChC,IAAI;AACV;AAEA,SAASO,eAAeA,CAACC,IAAY,EAAE;EACrC,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,2BAA2B,EAAEM,IAAI,CAAC,EAAE;IAC3E,OAAON,2BAA2B,CAACM,IAAI,CAAC;EAC1C;EACA,MAAMK,KAAK,GAAGf,UAAU,CAACgB,IAAI,CAACN,IAAI,CAAC;EACnC,IAAI,CAACK,KAAK,EAAE;IACVX,2BAA2B,CAACM,IAAI,CAAC,GAAG,IAAI;IACxC,OAAO,IAAI;EACb;EACA,MAAMO,MAAM,GAAG,MAAM,CAACD,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMG,QAAQ,GAAG,QAAQ,CAACF,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;EACxCX,2BAA2B,CAACM,IAAI,CAAC,GAAG;IAClCS,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IACxBK,UAAU,EAAEH,MAAM,GAAG,MAAM,GAAG,QAAQ;IACtCI,SAAS,EAAEH,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IACzCI,UAAU,EAAEjB,uBAAuB,CAACU,KAAK,CAAC,CAAC,CAAC;EAC9C,CAAC;EACD,OAAOX,2BAA2B,CAACM,IAAI,CAAC;AAC1C;AAqBA,OAAO,SAASa,WAAWA,CAACC,KAAgB,EAAE;EAC5C,MAAM;IACJC,QAAQ;IACRJ,SAAS;IACTK,WAAW;IACXN,UAAU;IACVO,WAAW;IACXR,QAAQ;IACRG,UAAU;IACVM,UAAU;IACVC,cAAc;IACdC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,mBAAmB;IACnBC,oBAAoB;IACpBC,qBAAqB;IACrBzB;EACF,CAAC,GAAGc,KAAK;EAET,MAAMY,SAAS,GAAGrC,UAAU,CAAC;IAC3B0B,QAAQ;IACRJ,SAAS;IACTK,WAAW;IACXN,UAAU;IACVO,WAAW;IACXR,QAAQ;IACRG,UAAU,EAAEjB,uBAAuB,CAACiB,UAAU,CAAC;IAC/CM,UAAU;IACVC,cAAc;IACdC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,mBAAmB;IACnBC,oBAAoB;IACpBC;EACF,CAAC,CAAC;EAEF,MAAME,QAAQ,GAAG,OAAO3B,IAAI,KAAK,QAAQ,GAAGD,eAAe,CAACC,IAAI,CAAC,GAAGA,IAAI;EAExE,OAAO;IAAE,GAAG2B,QAAQ;IAAE,GAAGD;EAAU,CAAC;AACtC;AAEA,IAAIE,KAA6C;AAEjD,OAAO,SAASC,QAAQA,CAACC,mBAAkC,EAAE;EAC3DF,KAAK,GAAGE,mBAAmB;AAC7B;AAMA,SAASC,QAAQA,CAACC,KAAgB,EAAE;EAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1D,oBAAO9C,KAAA,CAAA+C,aAAA,CAACL,KAAK,QAAEM,MAAM,CAACF,KAAK,CAAS,CAAC;EACvC,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;AAeA,eAAe,SAASG,WAAWA,CAACrB,KAAgB,EAAEsB,SAAkB,EAAE;EACxE,MAAM;IACJC,CAAC;IACDC,CAAC;IACDC,EAAE;IACFC,EAAE;IACFC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGhC,KAAK;EAET,MAAMiC,YAAY,GAChB,OAAOL,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,GAC1DN,SAAS,gBACPlD,KAAA,CAAA+C,aAAA,CAACL,KAAK,QAAEM,MAAM,CAACQ,QAAQ,CAAS,CAAC,GAC/B,IAAI,GACNvD,QAAQ,CAAC6D,KAAK,CAACN,QAAQ,CAAC,GAAG,CAAC,IAAIO,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,GACzDvD,QAAQ,CAACgE,GAAG,CAACT,QAAQ,EAAEX,QAAQ,CAAC,GAEhCW,QACD;EAEH,OAAO;IACLU,OAAO,EAAEL,YAAY,KAAK,IAAI,GAAGb,MAAM,CAACQ,QAAQ,CAAC,GAAG,IAAI;IACxDA,QAAQ,EAAEK,YAAY;IACtBJ,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,iBAAiB;IACjB9C,IAAI,EAAEa,WAAW,CAACC,KAAK,CAAC;IACxBuB,CAAC,EAAEjD,iBAAiB,CAACiD,CAAC,CAAC;IACvBC,CAAC,EAAElD,iBAAiB,CAACkD,CAAC,CAAC;IACvBC,EAAE,EAAEnD,iBAAiB,CAACmD,EAAE,CAAC;IACzBC,EAAE,EAAEpD,iBAAiB,CAACoD,EAAE,CAAC;IACzBC,MAAM,EAAErD,iBAAiB,CAACqD,MAAM;EAClC,CAAC;AACH", "ignoreList": []}