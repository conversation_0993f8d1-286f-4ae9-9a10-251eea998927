{"version": 3, "names": ["append", "appendTransform", "identity", "reset", "toArray", "parse", "parseTransformSvgToRnStyle", "appendTransformProps", "props", "x", "y", "originX", "originY", "scaleX", "scaleY", "rotation", "skewX", "skewY", "universal2axis", "universal", "axisX", "axisY", "defaultValue", "coords", "split", "length", "Array", "isArray", "isNaN", "transformsArrayToProps", "transformObjectsArray", "for<PERSON>ach", "transformObject", "keys", "Object", "console", "error", "key", "value", "props2transform", "translate", "translateX", "translateY", "origin", "scale", "skew", "warn", "tr", "or", "sc", "sk", "transformToMatrix", "transform", "columnMatrix", "transformProps", "t", "e", "extractTransform", "extractTransformSvgView"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractTransform.ts"], "mappings": "AACA,SAASA,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,QAAQ,aAAa;AAC/E,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASA,KAAK,IAAIC,0BAA0B,QAAQ,iBAAiB;AAarE,SAASC,oBAAoBA,CAACC,KAAuB,EAAE;EACrD,MAAM;IAAEC,CAAC;IAAEC,CAAC;IAAEC,OAAO;IAAEC,OAAO;IAAEC,MAAM;IAAEC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,GACtET,KAAK;EACPP,eAAe,CACbQ,CAAC,GAAGE,OAAO,EACXD,CAAC,GAAGE,OAAO,EACXC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLN,OAAO,EACPC,OACF,CAAC;AACH;AAEA,SAASM,cAAcA,CACrBC,SAAgD,EAChDC,KAAwB,EACxBC,KAAwB,EACxBC,YAAqB,EACH;EAClB,IAAIb,CAAC;EACL,IAAIC,CAAC;EACL,IAAI,OAAOS,SAAS,KAAK,QAAQ,EAAE;IACjCV,CAAC,GAAGC,CAAC,GAAGS,SAAS;EACnB,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxC,MAAMI,MAAM,GAAGJ,SAAS,CAACK,KAAK,CAAC,SAAS,CAAC;IACzC,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACvBhB,CAAC,GAAG,CAACc,MAAM,CAAC,CAAC,CAAC;MACdb,CAAC,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIA,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MAC9BhB,CAAC,GAAGC,CAAC,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACR,SAAS,CAAC,EAAE;IACnC,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MAC1BhB,CAAC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;MACjBT,CAAC,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MACjChB,CAAC,GAAGC,CAAC,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC;IACvB;EACF;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACQ,KAAK,CAACR,KAAK,CAAC,EAAE;IACjBX,CAAC,GAAGW,KAAK;EACX;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACO,KAAK,CAACP,KAAK,CAAC,EAAE;IACjBX,CAAC,GAAGW,KAAK;EACX;EAEA,OAAO,CAACZ,CAAC,IAAIa,YAAY,IAAI,CAAC,EAAEZ,CAAC,IAAIY,YAAY,IAAI,CAAC,CAAC;AACzD;AAEA,OAAO,SAASO,sBAAsBA,CACpCC,qBAA2C,EAC3C;EACA,MAAMtB,KAAqB,GAAG,CAAC,CAAC;EAChCsB,qBAAqB,aAArBA,qBAAqB,eAArBA,qBAAqB,CAAEC,OAAO,CAAEC,eAAe,IAAK;IAClD,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,eAAe,CAAC;IACzC,IAAIC,IAAI,CAACR,MAAM,KAAK,CAAC,EAAE;MACrBU,OAAO,CAACC,KAAK,CACX,6DACF,CAAC;IACH;IACA,MAAMC,GAAG,GAAGJ,IAAI,CAAC,CAAC,CAAyB;IAC3C,MAAMK,KAAK,GAAGN,eAAe,CAACK,GAAG,CAAiC;IAClE;IACA7B,KAAK,CAAC6B,GAAG,CAAC,GAAGC,KAAK;EACpB,CAAC,CAAC;EACF,OAAO9B,KAAK;AACd;AAEA,OAAO,SAAS+B,eAAeA,CAC7B/B,KAAiC,EACR;EACzB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAM;IACJO,QAAQ;IACRyB,SAAS;IACTC,UAAU;IACVC,UAAU;IACVC,MAAM;IACNhC,OAAO;IACPC,OAAO;IACPgC,KAAK;IACL/B,MAAM;IACNC,MAAM;IACN+B,IAAI;IACJ7B,KAAK;IACLC,KAAK;IACLR,CAAC;IACDC;EACF,CAAC,GAAGF,KAAK;EACT,IACEO,QAAQ,IAAI,IAAI,IAChByB,SAAS,IAAI,IAAI,IACjBC,UAAU,IAAI,IAAI,IAClBC,UAAU,IAAI,IAAI,IAClBC,MAAM,IAAI,IAAI,IACdhC,OAAO,IAAI,IAAI,IACfC,OAAO,IAAI,IAAI,IACfgC,KAAK,IAAI,IAAI,IACb/B,MAAM,IAAI,IAAI,IACdC,MAAM,IAAI,IAAI,IACd+B,IAAI,IAAI,IAAI,IACZ7B,KAAK,IAAI,IAAI,IACbC,KAAK,IAAI,IAAI,IACbR,CAAC,IAAI,IAAI,IACTC,CAAC,IAAI,IAAI,EACT;IACA,OAAO,IAAI;EACb;EAEA,IAAIgB,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,IAAIiB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,EAAE;IACxCyB,OAAO,CAACW,IAAI,CACV,oEACF,CAAC;EACH;EACA,MAAMC,EAAE,GAAG7B,cAAc,CACvBsB,SAAS,EACTC,UAAU,KAAKf,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,EAC3CiC,UAAU,KAAKhB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAC5C,CAAC;EACD,MAAMsC,EAAE,GAAG9B,cAAc,CAACyB,MAAM,EAAEhC,OAAO,EAAEC,OAAO,CAAC;EACnD,MAAMqC,EAAE,GAAG/B,cAAc,CAAC0B,KAAK,EAAE/B,MAAM,EAAEC,MAAM,EAAE,CAAC,CAAC;EACnD,MAAMoC,EAAE,GAAGhC,cAAc,CAAC2B,IAAI,EAAE7B,KAAK,EAAEC,KAAK,CAAC;EAE7C,OAAO;IACLF,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,QAAQ,IAAI,CAAC;IAC/CJ,OAAO,EAAEqC,EAAE,CAAC,CAAC,CAAC;IACdpC,OAAO,EAAEoC,EAAE,CAAC,CAAC,CAAC;IACdnC,MAAM,EAAEoC,EAAE,CAAC,CAAC,CAAC;IACbnC,MAAM,EAAEmC,EAAE,CAAC,CAAC,CAAC;IACbjC,KAAK,EAAEkC,EAAE,CAAC,CAAC,CAAC;IACZjC,KAAK,EAAEiC,EAAE,CAAC,CAAC,CAAC;IACZzC,CAAC,EAAEsC,EAAE,CAAC,CAAC,CAAC;IACRrC,CAAC,EAAEqC,EAAE,CAAC,CAAC;EACT,CAAC;AACH;AAEA,OAAO,SAASI,iBAAiBA,CAC/B3C,KAA8B,EAC9B4C,SAAsC,EACH;EACnC,IAAI,CAAC5C,KAAK,IAAI,CAAC4C,SAAS,EAAE;IACxB,OAAO,IAAI;EACb;EACAjD,KAAK,CAAC,CAAC;EACPK,KAAK,IAAID,oBAAoB,CAACC,KAAK,CAAC;EAEpC,IAAI4C,SAAS,EAAE;IACb,IAAI1B,KAAK,CAACC,OAAO,CAACyB,SAAS,CAAC,EAAE;MAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,MAAMC,YAAY,GAAGD,SAAuC;QAC5DpD,MAAM,CACJqD,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAChB,CAAC;MACH,CAAC,MAAM;QACL,MAAMC,cAAc,GAAGf,eAAe;QACpC;QACAV,sBAAsB,CAACuB,SAAiC,CAC1D,CAAC;QACDE,cAAc,IAAI/C,oBAAoB,CAAC+C,cAAc,CAAC;MACxD;IACF,CAAC,MAAM,IAAI,OAAOF,SAAS,KAAK,QAAQ,EAAE;MACxC,IAAI;QACF,MAAMG,CAAC,GAAGlD,KAAK,CAAC+C,SAAS,CAAC;QAC1BpD,MAAM,CAACuD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVrB,OAAO,CAACC,KAAK,CAACoB,CAAC,CAAC;MAClB;IACF,CAAC,MAAM;MACL;MACA,MAAMF,cAAc,GAAGf,eAAe,CAACa,SAAS,CAAC;MACjDE,cAAc,IAAI/C,oBAAoB,CAAC+C,cAAc,CAAC;IACxD;EACF;EAEA,OAAOlD,OAAO,CAAC,CAAC;AAClB;AAEA,eAAe,SAASqD,gBAAgBA,CACtCjD,KAAmD,EAChB;EACnC,IAAIkB,KAAK,CAACC,OAAO,CAACnB,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACxD,OAAOA,KAAK;EACd;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI;MACF,MAAM+C,CAAC,GAAGlD,KAAK,CAACG,KAAK,CAAC;MACtB,OAAO,CAAC+C,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVrB,OAAO,CAACC,KAAK,CAACoB,CAAC,CAAC;MAChB,OAAOtD,QAAQ;IACjB;EACF;EACA;EACA;EACA,MAAMoD,cAAc,GAAG9C,KAAuB;EAC9C,OAAO2C,iBAAiB,CACtBZ,eAAe,CAACe,cAAc,CAAC,EAC/BA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEF,SAClB,CAAC;AACH;AAEA,OAAO,SAASM,uBAAuBA,CACrClD,KAAsB,EACQ;EAC9B,IAAI,OAAOA,KAAK,CAAC4C,SAAS,KAAK,QAAQ,EAAE;IACvC,OAAO9C,0BAA0B,CAACE,KAAK,CAAC4C,SAAS,CAAC;EACpD;EACA,OAAO5C,KAAK,CAAC4C,SAAS;AACxB", "ignoreList": []}