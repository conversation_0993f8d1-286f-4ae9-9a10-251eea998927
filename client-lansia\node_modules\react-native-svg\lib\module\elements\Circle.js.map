{"version": 3, "names": ["React", "extract", "<PERSON><PERSON><PERSON>", "RNSVGCircle", "Circle", "displayName", "defaultProps", "cx", "cy", "r", "render", "props", "circleProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Circle.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AAErD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,WAAW,MAAM,iCAAiC;AAUzD,eAAe,MAAMC,MAAM,SAASF,KAAK,CAAc;EACrD,OAAOG,WAAW,GAAG,QAAQ;EAE7B,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,CAAC,EAAE;EACL,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEJ,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAC,GAAGE,KAAK;IAC3B,MAAMC,WAAW,GAAG;MAAE,GAAGX,OAAO,CAAC,IAAI,EAAEU,KAAK,CAAC;MAAEJ,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAC;IAE1D,oBACET,KAAA,CAAAa,aAAA,CAACV,WAAW,EAAAW,QAAA;MACVC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAsC;IAAE,GACjEH,WAAW,CAChB,CAAC;EAEN;AACF", "ignoreList": []}