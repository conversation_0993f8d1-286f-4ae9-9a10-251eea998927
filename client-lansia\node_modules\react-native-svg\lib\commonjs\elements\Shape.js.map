{"version": 3, "names": ["_react", "require", "_SvgTouchableMixin", "_interopRequireDefault", "_extractBrush", "_reactNative", "_colors", "e", "__esModule", "default", "multiplyMatrices", "l", "r", "a", "al", "b", "bl", "c", "cl", "d", "dl", "el", "f", "fl", "ar", "br", "cr", "dr", "er", "fr", "invert", "n", "deg2rad", "Math", "PI", "SVGMatrix", "constructor", "matrix", "multiply", "secondMatrix", "inverse", "translate", "x", "y", "scale", "scaleFactor", "scaleNonUniform", "scaleFactorX", "scaleFactorY", "rotate", "angle", "cos", "sin", "rotateFromVector", "atan2", "flipX", "flipY", "skewX", "tan", "skewY", "exports", "matrixTransform", "point", "SVGPoint", "ownerSVGElement", "createSVGPoint", "createSVGMatrix", "<PERSON><PERSON><PERSON>", "Component", "root", "props", "SvgTouchableMixin", "refMethod", "instance", "getNativeScrollRef", "setNativeProps", "_this$root", "key", "BrushProperties", "includes", "extractBrush", "getBBox", "options", "fill", "stroke", "markers", "clipped", "handle", "findNodeHandle", "RNSVGRenderableModule", "getCTM", "getScreenCTM", "isPointInFill", "isPointInStroke", "getTotalLength", "getPointAtLength", "length", "prototype"], "sourceRoot": "../../../src", "sources": ["elements/Shape.tsx"], "mappings": ";;;;;;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,YAAA,GAAAJ,OAAA;AAMA,IAAAK,OAAA,GAAAL,OAAA;AAAwD,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAXxD;;AAmEO,SAASG,gBAAgBA,CAACC,CAAS,EAAEC,CAAS,EAAU;EAC7D,MAAM;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEb,CAAC,EAAEc,EAAE;IAAEC,CAAC,EAAEC;EAAG,CAAC,GAAGZ,CAAC;EACtD,MAAM;IAAEE,CAAC,EAAEW,EAAE;IAAET,CAAC,EAAEU,EAAE;IAAER,CAAC,EAAES,EAAE;IAAEP,CAAC,EAAEQ,EAAE;IAAEpB,CAAC,EAAEqB,EAAE;IAAEN,CAAC,EAAEO;EAAG,CAAC,GAAGjB,CAAC;EAEtD,MAAMC,CAAC,GAAGC,EAAE,GAAGU,EAAE,GAAGN,EAAE,GAAGO,EAAE;EAC3B,MAAMR,CAAC,GAAGH,EAAE,GAAGY,EAAE,GAAGR,EAAE,GAAGS,EAAE;EAC3B,MAAMpB,CAAC,GAAGO,EAAE,GAAGc,EAAE,GAAGV,EAAE,GAAGW,EAAE,GAAGR,EAAE;EAChC,MAAMN,CAAC,GAAGC,EAAE,GAAGQ,EAAE,GAAGJ,EAAE,GAAGK,EAAE;EAC3B,MAAMN,CAAC,GAAGH,EAAE,GAAGU,EAAE,GAAGN,EAAE,GAAGO,EAAE;EAC3B,MAAML,CAAC,GAAGN,EAAE,GAAGY,EAAE,GAAGR,EAAE,GAAGS,EAAE,GAAGN,EAAE;EAEhC,OAAO;IAAEV,CAAC;IAAEI,CAAC;IAAEV,CAAC;IAAEQ,CAAC;IAAEI,CAAC;IAAEG;EAAE,CAAC;AAC7B;AAEO,SAASQ,MAAMA,CAAC;EAAEjB,CAAC;EAAEE,CAAC;EAAEE,CAAC;EAAEE,CAAC;EAAEZ,CAAC;EAAEe;AAAU,CAAC,EAAU;EAC3D,MAAMS,CAAC,GAAGlB,CAAC,GAAGM,CAAC,GAAGJ,CAAC,GAAGE,CAAC;EACvB,OAAO;IACLJ,CAAC,EAAEM,CAAC,GAAGY,CAAC;IACRhB,CAAC,EAAE,CAACA,CAAC,GAAGgB,CAAC;IACTd,CAAC,EAAE,CAACA,CAAC,GAAGc,CAAC;IACTZ,CAAC,EAAEN,CAAC,GAAGkB,CAAC;IACRxB,CAAC,EAAE,CAACU,CAAC,GAAGK,CAAC,GAAGH,CAAC,GAAGZ,CAAC,IAAIwB,CAAC;IACtBT,CAAC,EAAE,EAAET,CAAC,GAAGS,CAAC,GAAGP,CAAC,GAAGR,CAAC,CAAC,GAAGwB;EACxB,CAAC;AACH;AAEA,MAAMC,OAAO,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAEtB,MAAMC,SAAS,CAAsB;EAC1CC,WAAWA,CAACC,MAAe,EAAE;IAC3B,IAAIA,MAAM,EAAE;MACV,MAAM;QAAExB,CAAC;QAAEE,CAAC;QAAEE,CAAC;QAAEE,CAAC;QAAEZ,CAAC;QAAEe;MAAE,CAAC,GAAGe,MAAM;MACnC,IAAI,CAACxB,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACZ,CAAC,GAAGA,CAAC;MACV,IAAI,CAACe,CAAC,GAAGA,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACT,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACZ,CAAC,GAAG,CAAC;MACV,IAAI,CAACe,CAAC,GAAG,CAAC;IACZ;EACF;EAEAgB,QAAQA,CAACC,YAAoB,EAAa;IACxC,OAAO,IAAIJ,SAAS,CAACzB,gBAAgB,CAAC,IAAI,EAAE6B,YAAY,CAAC,CAAC;EAC5D;EAEAC,OAAOA,CAAA,EAAc;IACnB,OAAO,IAAIL,SAAS,CAACL,MAAM,CAAC,IAAI,CAAC,CAAC;EACpC;EAEAW,SAASA,CAACC,CAAS,EAAEC,CAAS,EAAa;IACzC,OAAO,IAAIR,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEZ,CAAC,EAAEmC,CAAC;MAAEpB,CAAC,EAAEqB;IAAE,CAAC,CAC/D,CAAC;EACH;EAEAC,KAAKA,CAACC,WAAmB,EAAa;IACpC,OAAO,IAAIV,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAEgC,WAAW;MACd9B,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE0B,WAAW;MACdtC,CAAC,EAAE,CAAC;MACJe,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEAwB,eAAeA,CAACC,YAAoB,EAAEC,YAAoB,EAAa;IACrE,OAAO,IAAIb,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAEkC,YAAY;MACfhC,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE6B,YAAY;MACfzC,CAAC,EAAE,CAAC;MACJe,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEA2B,MAAMA,CAACC,KAAa,EAAa;IAC/B,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACnB,OAAO,GAAGkB,KAAK,CAAC;IACrC,MAAME,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACpB,OAAO,GAAGkB,KAAK,CAAC;IACrC,OAAO,IAAIf,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAEsC,GAAG;MAAEpC,CAAC,EAAEqC,GAAG;MAAEnC,CAAC,EAAE,CAACmC,GAAG;MAAEjC,CAAC,EAAEgC,GAAG;MAAE5C,CAAC,EAAE,CAAC;MAAEe,CAAC,EAAE;IAAE,CAAC,CACxE,CAAC;EACH;EAEA+B,gBAAgBA,CAACX,CAAS,EAAEC,CAAS,EAAa;IAChD,MAAMO,KAAK,GAAGjB,IAAI,CAACqB,KAAK,CAACX,CAAC,EAAED,CAAC,CAAC;IAC9B,MAAMS,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACnB,OAAO,GAAGkB,KAAK,CAAC;IACrC,MAAME,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACpB,OAAO,GAAGkB,KAAK,CAAC;IACrC,OAAO,IAAIf,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAEsC,GAAG;MAAEpC,CAAC,EAAEqC,GAAG;MAAEnC,CAAC,EAAE,CAACmC,GAAG;MAAEjC,CAAC,EAAEgC,GAAG;MAAE5C,CAAC,EAAE,CAAC;MAAEe,CAAC,EAAE;IAAE,CAAC,CACxE,CAAC;EACH;EAEAiC,KAAKA,CAAA,EAAc;IACjB,OAAO,IAAIpB,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEZ,CAAC,EAAE,CAAC;MAAEe,CAAC,EAAE;IAAE,CAAC,CAChE,CAAC;EACH;EAEAkC,KAAKA,CAAA,EAAc;IACjB,OAAO,IAAIrB,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC,CAAC;MAAEZ,CAAC,EAAE,CAAC;MAAEe,CAAC,EAAE;IAAE,CAAC,CAChE,CAAC;EACH;EAEAmC,KAAKA,CAACP,KAAa,EAAa;IAC9B,OAAO,IAAIf,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAEgB,IAAI,CAACyB,GAAG,CAAC1B,OAAO,GAAGkB,KAAK,CAAC;MAC5B/B,CAAC,EAAE,CAAC;MACJZ,CAAC,EAAE,CAAC;MACJe,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEAqC,KAAKA,CAACT,KAAa,EAAa;IAC9B,OAAO,IAAIf,SAAS,CAClBzB,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAE,CAAC;MACJE,CAAC,EAAEkB,IAAI,CAACyB,GAAG,CAAC1B,OAAO,GAAGkB,KAAK,CAAC;MAC5BjC,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJZ,CAAC,EAAE,CAAC;MACJe,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;AACF;AAACsC,OAAA,CAAAzB,SAAA,GAAAA,SAAA;AAEM,SAAS0B,eAAeA,CAACxB,MAAc,EAAEyB,KAAY,EAAS;EACnE,MAAM;IAAEjD,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEZ,CAAC;IAAEe;EAAE,CAAC,GAAGe,MAAM;EACnC,MAAM;IAAEK,CAAC;IAAEC;EAAE,CAAC,GAAGmB,KAAK;EACtB,OAAO;IACLpB,CAAC,EAAE7B,CAAC,GAAG6B,CAAC,GAAGzB,CAAC,GAAG0B,CAAC,GAAGpC,CAAC;IACpBoC,CAAC,EAAE5B,CAAC,GAAG2B,CAAC,GAAGvB,CAAC,GAAGwB,CAAC,GAAGrB;EACrB,CAAC;AACH;AAEO,MAAMyC,QAAQ,CAAqB;EACxC3B,WAAWA,CAAC0B,KAAa,EAAE;IACzB,IAAIA,KAAK,EAAE;MACT,MAAM;QAAEpB,CAAC;QAAEC;MAAE,CAAC,GAAGmB,KAAK;MACtB,IAAI,CAACpB,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACD,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,CAAC,GAAG,CAAC;IACZ;EACF;EAEAkB,eAAeA,CAACxB,MAAc,EAAY;IACxC,OAAO,IAAI0B,QAAQ,CAACF,eAAe,CAACxB,MAAM,EAAE,IAAI,CAAC,CAAC;EACpD;AACF;AAACuB,OAAA,CAAAG,QAAA,GAAAA,QAAA;AAEM,MAAMC,eAAe,GAAAJ,OAAA,CAAAI,eAAA,GAAG;EAC7BC,cAAcA,CAAA,EAAa;IACzB,OAAO,IAAIF,QAAQ,CAAC,CAAC;EACvB,CAAC;EACDG,eAAeA,CAAA,EAAc;IAC3B,OAAO,IAAI/B,SAAS,CAAC,CAAC;EACxB;AACF,CAAC;AAEc,MAAMgC,KAAK,SAAYC,gBAAS,CAAI;EAEjDC,IAAI,GAAsC,IAAI;EAC9CjC,WAAWA,CAACkC,KAAsB,EAAE;IAClC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAAC,0BAAiB,EAAC,IAAI,CAAC;EACzB;EAEAC,SAAS,GACPC,QAA2C,IACxC;IACH,IAAI,CAACJ,IAAI,GAAGI,QAAQ;EACtB,CAAC;;EAED;EACAC,kBAAkBA,CAAA,EAAsC;IACtD,OAAO,IAAI,CAACL,IAAI;EAClB;EAEAM,cAAc,GACZL,KAGkB,IACf;IAAA,IAAAM,UAAA;IACH,KAAK,MAAMC,GAAG,IAAIP,KAAK,EAAE;MACvB,IAAIQ,uBAAe,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;QACjC;QACAP,KAAK,CAACO,GAAG,CAAC,GAAG,IAAAG,qBAAY,EAACV,KAAK,CAACO,GAAG,CAAC,CAAC;MACvC;IACF;IACA,CAAAD,UAAA,OAAI,CAACP,IAAI,cAAAO,UAAA,eAATA,UAAA,CAAWD,cAAc,CAACL,KAAK,CAAC;EAClC,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEW,OAAO,GAAIC,OAA+B,IAA0B;IAClE,MAAM;MACJC,IAAI,GAAG,IAAI;MACXC,MAAM,GAAG,IAAI;MACbC,OAAO,GAAG,IAAI;MACdC,OAAO,GAAG;IACZ,CAAC,GAAGJ,OAAO,IAAI,CAAC,CAAC;IACjB,MAAMK,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAAqB,GACzBxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAOgF,qBAAqB,CAACR,OAAO,CAACM,MAAM,EAAE;MAC3CJ,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,CAAC;EACJ,CAAC;EAEDI,MAAM,GAAGA,CAAA,KAAiB;IACxB,MAAMH,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAO,IAAI0B,SAAS,CAACsD,qBAAqB,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC;EAC5D,CAAC;EAEDI,YAAY,GAAGA,CAAA,KAAiB;IAC9B,MAAMJ,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAO,IAAI0B,SAAS,CAACsD,qBAAqB,CAACE,YAAY,CAACJ,MAAM,CAAC,CAAC;EAClE,CAAC;EAEDK,aAAa,GAAIV,OAAqB,IAA0B;IAC9D,MAAMK,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAOgF,qBAAqB,CAACG,aAAa,CAACL,MAAM,EAAEL,OAAO,CAAC;EAC7D,CAAC;EAEDW,eAAe,GAAIX,OAAqB,IAA0B;IAChE,MAAMK,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAOgF,qBAAqB,CAACI,eAAe,CAACN,MAAM,EAAEL,OAAO,CAAC;EAC/D,CAAC;EAEDY,cAAc,GAAGA,CAAA,KAA0B;IACzC,MAAMP,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAOgF,qBAAqB,CAACK,cAAc,CAACP,MAAM,CAAC;EACrD,CAAC;EAEDQ,gBAAgB,GAAIC,MAAc,IAAe;IAC/C,MAAMT,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACnB,IAAI,CAAC;IACxC,MAAMoB,qBAA2B,GAC/BxF,OAAO,CAAC,qCAAqC,CAAC,CAACQ,OAAO;IACxD,OAAO,IAAIsD,QAAQ,CACjB0B,qBAAqB,CAACM,gBAAgB,CAACR,MAAM,EAAE;MAAES;IAAO,CAAC,CAC3D,CAAC;EACH,CAAC;AACH;AAACpC,OAAA,CAAAnD,OAAA,GAAA0D,KAAA;AACDA,KAAK,CAAC8B,SAAS,CAACjC,eAAe,GAAGA,eAAe", "ignoreList": []}