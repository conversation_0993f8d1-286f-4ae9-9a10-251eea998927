import*as e from"../../../core/sdk/sdk.js";import*as t from"../types/types.js";import*as a from"../../../core/platform/platform.js";import*as n from"../helpers/helpers.js";const r=new Map,o=new Map;async function s(t,a){const n=r.get(t)?.get(a);if(void 0!==n)return n;const o=e.TargetManager.TargetManager.instance().primaryPageTarget(),s=o?.model(e.DOMModel.DOMModel);if(!s)return null;const i=await s.pushNodesByBackendIdsToFrontend(new Set([a])),c=i?.get(a)||null,d=r.get(t)||new Map;return d.set(a,c),r.set(t,d),c}const i=new WeakMap;function c(e,a){const n=i.get(a);if(n)return n;const r=new Set;if(t.TraceEvents.isTraceEventLayout(a))a.args.endData?.layoutRoots.forEach((e=>r.add(e.nodeId)));else if(t.TraceEvents.isSyntheticLayoutShift(a)&&a.args.data?.impacted_nodes)a.args.data.impacted_nodes.forEach((e=>r.add(e.node_id)));else if(t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(a)&&void 0!==a.args.data?.nodeId)r.add(a.args.data.nodeId);else if(t.TraceEvents.isTraceEventPaint(a)&&void 0!==a.args.data.nodeId)r.add(a.args.data.nodeId);else if(t.TraceEvents.isTraceEventPaintImage(a)&&void 0!==a.args.data.nodeId)r.add(a.args.data.nodeId);else if(t.TraceEvents.isTraceEventScrollLayer(a)&&void 0!==a.args.data.nodeId)r.add(a.args.data.nodeId);else if(t.TraceEvents.isTraceEventDecodeImage(a)){const t=e.ImagePainting.paintImageForEvent.get(a);t&&void 0!==t.args.data.nodeId&&r.add(t.args.data.nodeId)}else if(t.TraceEvents.isTraceEventDrawLazyPixelRef(a)&&a.args?.LazyPixelRef){const t=e.ImagePainting.paintImageByDrawLazyPixelRef.get(a.args.LazyPixelRef);t&&void 0!==t.args.data.nodeId&&r.add(t.args.data.nodeId)}return i.set(a,r),r}async function d(t,a){const n=o.get(t)?.get(a);if(n)return n;const r=e.TargetManager.TargetManager.instance().primaryPageTarget(),s=r?.model(e.DOMModel.DOMModel);if(!s)return new Map;const i=await s.pushNodesByBackendIdsToFrontend(new Set(a))||new Map,c=o.get(t)||new Map;return c.set(a,i),o.set(t,c),i}const l=new Map,g=new Map;var u=Object.freeze({__proto__:null,clearCacheForTesting:function(){r.clear(),o.clear(),l.clear(),g.clear()},domNodeForBackendNodeID:s,nodeIdsForEvent:c,extractRelatedDOMNodesFromEvent:async function(e,t){const a=c(e,t);return a.size?d(e,Array.from(a)):null},domNodesForMultipleBackendNodeIds:d,sourcesForLayoutShift:async function(e,t){const a=l.get(e)?.get(t);if(a)return a;const n=t.args.data?.impacted_nodes;if(!n)return[];const r=[];await Promise.all(n.map((async t=>{const a=await s(e,t.node_id);a&&r.push({previousRect:new DOMRect(t.old_rect[0],t.old_rect[1],t.old_rect[2],t.old_rect[3]),currentRect:new DOMRect(t.new_rect[0],t.new_rect[1],t.new_rect[2],t.new_rect[3]),node:a})})));const o=l.get(e)||new Map;return o.set(t,r),l.set(e,o),r},normalizedImpactedNodesForLayoutShift:async function(t,a){const n=g.get(t)?.get(a);if(n)return n;const r=a.args?.data?.impacted_nodes;if(!r)return[];let o=null;const s=e.TargetManager.TargetManager.instance().primaryPageTarget(),i=await(s?.runtimeAgent().invoke_evaluate({expression:"window.devicePixelRatio"}));if("number"===i?.result.type&&(o=i?.result.value??null),!o)return r;const c=[];for(const e of r){const t={...e};for(let a=0;a<e.old_rect.length;a++)t.old_rect[a]/=o;for(let a=0;a<e.new_rect.length;a++)t.new_rect[a]/=o;c.push(t)}const d=g.get(t)||new Map;return d.set(a,c),g.set(t,d),c}});const f=new Map;var m=Object.freeze({__proto__:null,fromTraceData:function(e,t){const n=[],r=void 0!==t?t:e.Meta.traceBounds.min,o=e.Meta.traceBounds.range,s=f.get(e)?.get(r);if(s)return s;for(const t of e.Screenshots){if(t.ts<r)continue;const e={index:n.length,screenshotEvent:t};n.push(e)}const i={zeroTime:r,spanTime:o,frames:Array.from(n)};return a.MapUtilities.getWithDefault(f,e,(()=>new Map)).set(r,i),i},frameClosestToTimestamp:function(e,t){const n=a.ArrayUtilities.nearestIndexFromEnd(e.frames,(e=>e.screenshotEvent.ts<t));return null===n?null:e.frames[n]}});const T=new Set(["(program)","(idle)","(root)"]);var p=Object.freeze({__proto__:null,calculateWindow:function(e,a){if(!a.length)return e;const r=a.filter((e=>!(t.TraceEvents.isProfileCall(e)&&(T.has(e.callFrame.functionName)||!e.callFrame.functionName))));if(0===r.length)return e;function o(e,t){let a=e;const o=r[a],s=n.Timing.eventTimingsMicroSeconds(o);let i=(s.startTime+s.endTime)/2,c=0;const d=Math.sign(t-e);for(let o=e;o!==t;o+=d){const e=r[o],t=n.Timing.eventTimingsMicroSeconds(e),s=(t.startTime+t.endTime)/2;c<.1*Math.abs(i-s)&&(a=o,i=s,c=0),c+=t.duration}return a}const s=o(r.length-1,0),i=o(0,s),c=n.Timing.eventTimingsMicroSeconds(r[i]),d=n.Timing.eventTimingsMicroSeconds(r[s]);let l=c.startTime,g=d.endTime;const u=g-l;return u<.1*e.range?e:(l=t.Timing.MicroSeconds(Math.max(l-.05*u,e.min)),g=t.Timing.MicroSeconds(Math.min(g+.05*u,e.max)),{min:l,max:g,range:t.Timing.MicroSeconds(g-l)})}});var v=Object.freeze({__proto__:null,forNewRecording:async function(t,a){try{if(t)return{dataOrigin:"CPUProfile"};const n=e.CPUThrottlingManager.CPUThrottlingManager.instance().hasPrimaryPageTargetSet()?await Promise.race([e.CPUThrottlingManager.CPUThrottlingManager.instance().getHardwareConcurrency(),new Promise((e=>{setTimeout((()=>e(void 0)),1e3)}))]):void 0,r=e.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate(),o=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),s="function"==typeof o.title?o.title():o.title;return{source:"DevTools",startTime:a?new Date(a).toJSON():void 0,cpuThrottling:r,networkThrottling:s,hardwareConcurrency:n,dataOrigin:"TraceEvents"}}catch{return{}}}});var M=Object.freeze({__proto__:null,get:function e(a,n){if(t.TraceEvents.isProfileCall(n))return n.callFrame.url;if(n.args?.data?.stackTrace&&n.args.data.stackTrace.length>0)return n.args.data.stackTrace[0].url;if(t.TraceEvents.isSyntheticNetworkRequestEvent(n))return n.args.data.url;if(t.TraceEvents.isTraceEventDecodeImage(n)){const t=a.ImagePainting.paintImageForEvent.get(n);return t?e(a,t):null}if(t.TraceEvents.isTraceEventDrawLazyPixelRef(n)&&n.args?.LazyPixelRef){const t=a.ImagePainting.paintImageByDrawLazyPixelRef.get(n.args.LazyPixelRef);return t?e(a,t):null}return t.TraceEvents.isTraceEventParseHTML(n)?n.args.beginData.url:n.args?.data?.url?n.args.data.url:null}});export{u as FetchNodes,m as FilmStrip,p as MainThreadActivity,v as Metadata,M as URLForEntry};
