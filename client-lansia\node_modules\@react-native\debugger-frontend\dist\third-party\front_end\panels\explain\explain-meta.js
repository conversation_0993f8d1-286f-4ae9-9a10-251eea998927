import*as e from"../../core/common/common.js";import*as o from"../../core/i18n/i18n.js";import*as n from"../console/console.js";import*as t from"../../ui/legacy/legacy.js";const s={explainThisError:"Understand this error",explainThisWarning:"Understand this warning",explainThisMessage:"Understand this message",enableConsoleInsights:"Understand console messages with AI",wrongLocale:"To use this feature, update your Language preference in DevTools Settings to English.",ageRestricted:"This feature is only available to users who are 18 years of age or older.",geoRestricted:"This feature is unavailable in your region.",policyRestricted:"Your organization turned off this feature. Contact your administrators for more information.",rolloutRestricted:"This feature is currently being rolled out. Stay tuned."},i=o.i18n.registerUIStrings("panels/explain/explain-meta.ts",s),a=o.i18n.getLazilyComputedLocalizedString.bind(void 0,i),r=o.i18n.getLocalizedString.bind(void 0,i),l="console-insights-enabled",c=[{actionId:"explain.console-message.hover",title:a(s.explainThisMessage),contextTypes:()=>[n.ConsoleViewMessage.ConsoleViewMessage]},{actionId:"explain.console-message.context.error",title:a(s.explainThisError),contextTypes:()=>[]},{actionId:"explain.console-message.context.warning",title:a(s.explainThisWarning),contextTypes:()=>[]},{actionId:"explain.console-message.context.other",title:a(s.explainThisMessage),contextTypes:()=>[]}];function d(){return!o.DevToolsLocale.DevToolsLocale.instance().locale.startsWith("en-")}function g(e){return!0===e?.devToolsConsoleInsights?.blockedByAge}function u(e){return!0===e?.devToolsConsoleInsights?.blockedByRollout}function p(e){return!0===e?.devToolsConsoleInsights?.blockedByGeo}function h(e){return!0===e?.devToolsConsoleInsights?.blockedByEnterprisePolicy}function y(e){return!1===e?.devToolsConsoleInsights?.blockedByFeatureFlag}e.Settings.registerSettingExtension({category:"CONSOLE",settingName:l,settingType:"boolean",title:a(s.enableConsoleInsights),defaultValue:e=>!function(e){return!0===e?.devToolsConsoleInsights?.optIn}(e),reloadRequired:!0,condition:e=>y(e),disabledCondition:e=>d()?{disabled:!0,reason:r(s.wrongLocale)}:g(e)?{disabled:!0,reason:r(s.ageRestricted)}:p(e)?{disabled:!0,reason:r(s.geoRestricted)}:h(e)?{disabled:!0,reason:r(s.policyRestricted)}:u(e)?{disabled:!0,reason:r(s.rolloutRestricted)}:{disabled:!1}});for(const e of c)t.ActionRegistration.registerActionExtension({...e,setting:l,category:"CONSOLE",loadActionDelegate:async()=>new((await import("./explain.js")).ActionDelegate),condition:e=>y(e)&&!g(e)&&!p(e)&&!d()&&!h(e)&&!u(e)});
