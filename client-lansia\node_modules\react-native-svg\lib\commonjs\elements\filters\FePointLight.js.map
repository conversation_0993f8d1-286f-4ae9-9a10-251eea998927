{"version": 3, "names": ["_react", "require", "_util", "FePointLight", "Component", "displayName", "defaultProps", "render", "warnUnimplementedFilter", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FePointLight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAQe,MAAME,YAAY,SAASC,gBAAS,CAAoB;EACrE,OAAOC,WAAW,GAAG,cAAc;EAEnC,OAAOC,YAAY,GAAG,CAAC,CAAC;EAExBC,MAAMA,CAAA,EAAG;IACP,IAAAC,6BAAuB,EAAC,CAAC;IACzB,OAAO,IAAI;EACb;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAP,YAAA", "ignoreList": []}