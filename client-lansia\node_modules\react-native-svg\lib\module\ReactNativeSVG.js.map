{"version": 3, "names": ["<PERSON><PERSON><PERSON>", "camelCase", "parse", "SvgAst", "SvgFromUri", "SvgFromXml", "SvgUri", "SvgXml", "fetchText", "RNSVGCircle", "RNSVGClipPath", "RNSVGDefs", "RNSVGEllipse", "RNSVGFeColorMatrix", "RNSVGFeComposite", "RNSVGFeGaussianBlur", "RNSVGFeMerge", "RNSVGFeOffset", "RNSVGFilter", "RNSVGForeignObject", "RNSVGGroup", "RNSVGImage", "RNSVGLine", "RNSVGLinearGradient", "RNSVGMarker", "RNSVGMask", "RNSVGPath", "RNSVGPattern", "RNSVGRadialGradient", "RNSVGRect", "RNSVGSvgAndroid", "RNSVGSvgIOS", "RNSVGSymbol", "RNSVGText", "RNSVGTextPath", "RNSVGTSpan", "RNSVGUse", "inlineStyles", "loadLocalRawResource", "LocalSvg", "SvgCss", "SvgCssUri", "SvgWithCss", "SvgWithCssUri", "WithLocalSvg", "default"], "sourceRoot": "../../src", "sources": ["ReactNativeSVG.ts"], "mappings": "AAAA,OAAOA,KAAK,MAAM,kBAAkB;AACpC,SAEEC,SAAS,EAGTC,KAAK,EAELC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,MAAM,QAMD,OAAO;AAEd,SAASC,SAAS,QAAQ,mBAAmB;AAE7C,SACEC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbC,WAAW,EACXC,kBAAkB,EAClBC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,mBAAmB,EACnBC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,mBAAmB,EACnBC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,QAAQ,QACH,UAAU;AAEjB,SACEC,YAAY,EACZC,oBAAoB,EACpBC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,cAAc;AAuDrB,cAAc,qBAAqB;AAEnC,SACE3C,SAAS,EACTO,SAAS,EACTN,KAAK,EACLO,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbC,WAAW,EACXC,kBAAkB,EAClBC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,mBAAmB,EACnBC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,mBAAmB,EACnBC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,QAAQ,EACRpC,KAAK,EACLG,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,MAAM;AAeR,cAAc,YAAY;AAC1B,SAASsC,OAAO,QAAQ,YAAY", "ignoreList": []}