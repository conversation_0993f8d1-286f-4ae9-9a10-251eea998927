import*as e from"../../core/common/common.js";import*as t from"../../core/root/root.js";import*as n from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";import*as s from"../../ui/visual_logging/visual_logging.js";import*as o from"../../core/host/host.js";import*as r from"../../core/i18n/i18n.js";import*as a from"../../core/platform/platform.js";import{assertNotNullOrUndefined as l}from"../../core/platform/platform.js";import*as d from"../../models/extensions/extensions.js";import*as c from"../../ui/components/buttons/buttons.js";import*as h from"../../ui/components/tree_outline/tree_outline.js";import*as p from"./components/components.js";import*as u from"../../ui/lit-html/lit-html.js";import*as m from"../../models/bindings/bindings.js";import*as g from"../../ui/legacy/components/color_picker/color_picker.js";import*as y from"../../ui/legacy/components/inline_editor/inline_editor.js";import*as f from"../../ui/legacy/components/utils/utils.js";import*as S from"../../models/text_utils/text_utils.js";import*as C from"../../models/workspace/workspace.js";import*as E from"../../models/workspace_diff/workspace_diff.js";import{PanelUtils as b}from"../utils/utils.js";import*as v from"../../ui/components/diff_view/diff_view.js";import*as w from"../../ui/components/icon_button/icon_button.js";import*as x from"../../third_party/codemirror.next/codemirror.next.js";import*as T from"../../ui/components/adorners/adorners.js";import*as M from"../../ui/components/code_highlighter/code_highlighter.js";import*as N from"../../ui/components/highlighting/highlighting.js";import*as I from"../../ui/components/text_editor/text_editor.js";import*as P from"../emulation/emulation.js";import*as O from"../../models/issues_manager/issues_manager.js";import*as L from"../../ui/components/issue_counter/issue_counter.js";import*as A from"../event_listeners/event_listeners.js";import*as k from"../../ui/legacy/components/object_ui/object_ui.js";function R(e){const t=n.FrameManager.FrameManager.instance().getFrame(e),i=t?.resourceTreeModel().target().model(n.AccessibilityModel.AccessibilityModel);if(!i)throw Error("Could not instantiate model for frameId");return i}async function D(e){const t=R(e),n=await t.requestRootNode(e);if(!n)throw Error("No accessibility root for frame");return n}function F(e){let t;if(t=e instanceof n.DOMModel.DOMDocument?e.body?.frameId():e.frameId(),!t)throw Error("No frameId for DOM node");return t}async function U(e){let t=F(e);const i=R(t),s=await i.requestAndLoadSubTreeToNode(e);if(!s)throw Error("Could not retrieve accessibility node for inspected DOM node");const o=n.FrameManager.FrameManager.instance().getOutermostFrame()?.id;if(!o)return s;for(;t!==o;){const e=await(n.FrameManager.FrameManager.instance().getFrame(t)?.getOwnerDOMNodeOrDocument());if(!e)break;t=F(e);const i=R(t),o=await i.requestAndLoadSubTreeToNode(e);s.push(...o||[])}return s}async function B(e){const t=e;return 0===(n=e).numChildren()&&"Iframe"!==n.role()?.value?[{treeNodeData:t,id:V(e)}]:[{treeNodeData:t,children:async()=>{const t=await async function(e){if("Iframe"===e.role()?.value){const t=await(e.deferredDOMNode()?.resolvePromise());if(!t)throw new Error("Could not find corresponding DOMNode");const n=t.frameOwnerFrameId();if(!n)throw Error("No owner frameId on iframe node");return[await D(n)]}return e.accessibilityModel().requestAXChildren(e.id(),e.getFrameId()||void 0)}(e);return(await Promise.all(t.map((e=>B(e))))).flat(1)},id:V(e)}];var n}function _(e){const t=p.AccessibilityTreeNode.AccessibilityTreeNode.litTagName,n=e.treeNodeData,i=n.name()?.value||"",s=n.role()?.value||"",o=n.properties()||[],r=n.ignored(),a=V(n);return u.html`<${t} .data=${{name:i,role:s,ignored:r,properties:o,id:a}}></${t}>`}function V(e){return e.getFrameId()+"#"+e.id()}var H=Object.freeze({__proto__:null,getRootNode:D,getNodeAndAncestorsFromDOMNode:U,sdkNodeToAXTreeNodes:B,accessibilityNodeRenderer:_,getNodeId:V});const W=new CSSStyleSheet;W.replaceSync(".accessibility-tree-view-container{height:100%;overflow:auto}\n/*# sourceURL=accessibilityTreeView.css */\n");class j extends i.Widget.VBox{accessibilityTreeComponent;toggleButton;inspectedDOMNode=null;root=null;constructor(e,t){super(),this.toggleButton=e,this.accessibilityTreeComponent=t;const i=this.contentElement.createChild("div");i.classList.add("accessibility-tree-view-container"),i.setAttribute("jslog",`${s.tree("full-accessibility")}`),i.appendChild(this.toggleButton),i.appendChild(this.accessibilityTreeComponent),n.TargetManager.TargetManager.instance().observeModels(n.AccessibilityModel.AccessibilityModel,this,{scoped:!0}),this.accessibilityTreeComponent.addEventListener("itemselected",(e=>{const t=e.data.node.treeNodeData;if(!t.isDOMNode())return;const n=t.deferredDOMNode();n&&n.resolve((e=>{e&&(this.inspectedDOMNode=e,Ls.instance().revealAndSelectNode(e,!0,!1))}))})),this.accessibilityTreeComponent.addEventListener("itemmouseover",(e=>{e.data.node.treeNodeData.highlightDOMNode()})),this.accessibilityTreeComponent.addEventListener("itemmouseout",(()=>{n.OverlayModel.OverlayModel.hideDOMNodeHighlight()}))}async wasShown(){await this.refreshAccessibilityTree(),this.inspectedDOMNode&&await this.loadSubTreeIntoAccessibilityModel(this.inspectedDOMNode),this.registerCSSFiles([W])}async refreshAccessibilityTree(){if(!this.root){const e=n.FrameManager.FrameManager.instance().getOutermostFrame()?.id;if(!e)throw Error("No top frame");if(this.root=await D(e),!this.root)throw Error("No root")}await this.renderTree(),await this.accessibilityTreeComponent.expandRecursively(1)}async renderTree(){if(!this.root)return;const e=await B(this.root);this.accessibilityTreeComponent.data={defaultRenderer:_,tree:e,filter:e=>e.ignored()||"generic"===e.role()?.value&&!e.name()?.value?"FLATTEN":"SHOW"}}async loadSubTreeIntoAccessibilityModel(e){const t=await U(e),n=t.find((t=>t.backendDOMNodeId()===e.backendNodeId()));n&&(await this.accessibilityTreeComponent.expandNodeIds(t.map((e=>e.getFrameId()+"#"+e.id()))),await this.accessibilityTreeComponent.focusNodeId(V(n)))}async revealAndSelectNode(e){e!==this.inspectedDOMNode&&(this.inspectedDOMNode=e,this.isShowing()&&await this.loadSubTreeIntoAccessibilityModel(e))}async selectedNodeChanged(e){this.isShowing()||e===this.inspectedDOMNode||(!e.ownerDocument||"HTML"!==e.nodeName()&&"BODY"!==e.nodeName()?this.inspectedDOMNode=e:this.inspectedDOMNode=e.ownerDocument)}treeUpdated({data:e}){if(!e.root)return void this.renderTree();const t=n.FrameManager.FrameManager.instance().getOutermostFrame()?.id;e.root?.getFrameId()===t?(this.root=e.root,this.accessibilityTreeComponent.collapseAllNodes(),this.refreshAccessibilityTree()):this.renderTree()}modelAdded(e){e.addEventListener("TreeUpdated",this.treeUpdated,this)}modelRemoved(e){e.removeEventListener("TreeUpdated",this.treeUpdated,this)}}var z=Object.freeze({__proto__:null,AccessibilityTreeView:j});const $={openCubicBezierEditor:"Open cubic bezier editor",openShadowEditor:"Open shadow editor"},K=r.i18n.registerUIStrings("panels/elements/ColorSwatchPopoverIcon.ts",$),G=r.i18n.getLocalizedString.bind(void 0,K);class Y{treeElement;swatchPopoverHelper;swatch;boundBezierChanged;boundOnScroll;bezierEditor;scrollerElement;originalPropertyText;constructor({treeElement:e,swatchPopoverHelper:t,swatch:n}){this.treeElement=e,this.swatchPopoverHelper=t,this.swatch=n,i.Tooltip.Tooltip.install(this.swatch.iconElement(),G($.openCubicBezierEditor)),this.swatch.iconElement().addEventListener("click",this.iconClick.bind(this),!1),this.swatch.iconElement().addEventListener("mousedown",(e=>e.consume()),!1),this.boundBezierChanged=this.bezierChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}iconClick(t){if(t.consume(!0),this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);const n=y.AnimationTimingModel.AnimationTimingModel.parse(this.swatch.bezierText())||y.AnimationTimingModel.LINEAR_BEZIER;this.bezierEditor=new y.BezierEditor.BezierEditor(n),this.bezierEditor.addEventListener("BezierChanged",this.boundBezierChanged),this.swatchPopoverHelper.show(this.bezierEditor,this.swatch.iconElement(),this.onPopoverHidden.bind(this)),this.scrollerElement=this.swatch.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const i=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);i&&e.Revealer.reveal(i,!0)}bezierChanged(e){this.swatch.setBezierText(e.data),this.treeElement.applyStyleText(this.treeElement.renderedPropertyText(),!1)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.bezierEditor&&this.bezierEditor.removeEventListener("BezierChanged",this.boundBezierChanged),this.bezierEditor=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText}}class q extends e.ObjectWrapper.ObjectWrapper{treeElement;swatchPopoverHelper;swatch;contrastInfo;boundSpectrumChanged;boundOnScroll;spectrum;scrollerElement;originalPropertyText;constructor(e,t,n){super(),this.treeElement=e,this.swatchPopoverHelper=t,this.swatch=n,this.swatch.addEventListener(y.ColorSwatch.ClickEvent.eventName,this.iconClick.bind(this)),this.contrastInfo=null,this.boundSpectrumChanged=this.spectrumChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}generateCSSVariablesPalette(){const t=this.treeElement.matchedStyles(),n=this.treeElement.property.ownerStyle,i=t.availableCSSVariables(n),s=[],o=[];for(const r of i){if(r===this.treeElement.property.name)continue;const i=t.computeCSSVariable(n,r);if(!i)continue;e.Color.parse(i.value)&&(s.push(i.value),o.push(r))}return{title:"CSS Variables",mutable:!1,matchUserFormat:!0,colors:s,colorNames:o}}setContrastInfo(e){this.contrastInfo=e}iconClick(e){e.consume(!0),this.showPopover()}async toggleEyeDropper(){await(this.spectrum?.toggleColorPicker())}showPopover(){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);const t=this.swatch.getColor();if(!t)return;this.spectrum=new g.Spectrum.Spectrum(this.contrastInfo),this.spectrum.setColor(t),this.spectrum.addPalette(this.generateCSSVariablesPalette()),this.spectrum.addEventListener("SizeChanged",this.spectrumResized,this),this.spectrum.addEventListener("ColorChanged",this.boundSpectrumChanged),this.swatchPopoverHelper.show(this.spectrum,this.swatch,this.onPopoverHidden.bind(this)),this.scrollerElement=this.swatch.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const n=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);n&&e.Revealer.reveal(n,!0),i.Context.Context.instance().setFlavor(q,this),o.userMetrics.colorPickerOpenedFrom(1)}spectrumResized(){this.swatchPopoverHelper.reposition()}async spectrumChanged(t){const n=e.Color.parse(t.data);if(!n)return;const i=this.spectrum?this.spectrum.colorName():void 0,s=i&&i.startsWith("--")?`var(${i})`:n.getAuthoredText()??n.asString();this.swatch.renderColor(n);const o=this.swatch.firstElementChild;o&&(o.remove(),this.swatch.createChild("span").textContent=s),s&&this.dispatchEventToListeners("colorchanged",s)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.spectrum&&this.spectrum.removeEventListener("ColorChanged",this.boundSpectrumChanged),this.spectrum=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText,i.Context.Context.instance().setFlavor(q,null)}}class X extends e.ObjectWrapper.ObjectWrapper{treeElement;swatchPopoverHelper;shadowSwatch;iconElement;boundShadowChanged;boundOnScroll;cssShadowEditor;scrollerElement;originalPropertyText;constructor(e,t,n){super(),this.treeElement=e,this.swatchPopoverHelper=t,this.shadowSwatch=n,this.iconElement=n.iconElement(),i.Tooltip.Tooltip.install(this.iconElement,G($.openShadowEditor)),this.iconElement.addEventListener("click",this.iconClick.bind(this),!1),this.iconElement.addEventListener("mousedown",(e=>e.consume()),!1),this.boundShadowChanged=this.shadowChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}iconClick(e){e.consume(!0),this.showPopover()}showPopover(){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);this.cssShadowEditor=new y.CSSShadowEditor.CSSShadowEditor,this.cssShadowEditor.setModel(this.shadowSwatch.model()),this.cssShadowEditor.addEventListener("ShadowChanged",this.boundShadowChanged),this.swatchPopoverHelper.show(this.cssShadowEditor,this.iconElement,this.onPopoverHidden.bind(this)),this.scrollerElement=this.iconElement.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const t=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);t&&e.Revealer.reveal(t,!0)}shadowChanged(e){this.dispatchEventToListeners("shadowChanged",e.data)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.cssShadowEditor&&this.cssShadowEditor.removeEventListener("ShadowChanged",this.boundShadowChanged),this.cssShadowEditor=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText}}class Q{treeElementMap;swatchPopoverHelper;section;parentPane;fontEditor;scrollerElement;boundFontChanged;boundOnScroll;boundResized;constructor(e,t){this.treeElementMap=new Map,this.swatchPopoverHelper=e,this.section=t,this.parentPane=null,this.fontEditor=null,this.scrollerElement=null,this.boundFontChanged=this.fontChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this),this.boundResized=this.fontEditorResized.bind(this)}fontChanged(e){const{propertyName:t,value:n}=e.data,i=this.treeElementMap.get(t);this.updateFontProperty(t,n,i)}async updateFontProperty(e,t,n){if(n&&n.treeOutline&&n.valueElement&&n.property.parsedOk&&n.property.range){let e,i=!1;n.valueElement.textContent=t,n.property.value=t;const s=n.property.name;t.length?e=n.renderedPropertyText():(e="",i=!0,this.fixIndex(n.property.index)),this.treeElementMap.set(s,n),await n.applyStyleText(e,!0),i&&this.treeElementMap.delete(s)}else if(t.length){const n=this.section.addNewBlankProperty();n&&(n.property.name=e,n.property.value=t,n.updateTitle(),await n.applyStyleText(n.renderedPropertyText(),!0),this.treeElementMap.set(n.property.name,n))}this.section.onpopulate(),this.swatchPopoverHelper.reposition()}fontEditorResized(){this.swatchPopoverHelper.reposition()}fixIndex(e){for(const t of this.treeElementMap.values())t.property.index>e&&(t.property.index-=1)}createPropertyValueMap(){const e=new Map;for(const t of this.treeElementMap){const n=t[0],i=t[1];i.property.value.length?e.set(n,i.property.value):this.treeElementMap.delete(n)}return e}registerFontProperty(e){const t=e.property.name;if(this.treeElementMap.has(t)){const n=this.treeElementMap.get(t);(!e.overloaded()||n&&n.overloaded())&&this.treeElementMap.set(t,e)}else this.treeElementMap.set(t,e)}async showPopover(e,t){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);this.parentPane=t;const n=this.createPropertyValueMap();this.fontEditor=new y.FontEditor.FontEditor(n),this.fontEditor.addEventListener("FontChanged",this.boundFontChanged),this.fontEditor.addEventListener("FontEditorResized",this.boundResized),this.swatchPopoverHelper.show(this.fontEditor,e,this.onPopoverHidden.bind(this)),this.scrollerElement=e.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.parentPane.setEditingStyle(!0)}onScroll(){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.section.onpopulate(),this.fontEditor&&this.fontEditor.removeEventListener("FontChanged",this.boundFontChanged),this.fontEditor=null,this.parentPane&&this.parentPane.setEditingStyle(!1),this.section.resetToolbars(),this.section.onpopulate()}}var J=Object.freeze({__proto__:null,BezierPopoverIcon:Y,ColorSwatchPopoverIcon:q,ShadowSwatchPopoverHelper:X,FontEditorSectionManager:Q});class Z extends e.ObjectWrapper.ObjectWrapper{nodeInternal;cssModelInternal;eventListeners;frameResizedTimer;computedStylePromise;constructor(){super(),this.nodeInternal=i.Context.Context.instance().flavor(n.DOMModel.DOMNode),this.cssModelInternal=null,this.eventListeners=[],i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.onNodeChanged,this)}node(){return this.nodeInternal}cssModel(){return this.cssModelInternal&&this.cssModelInternal.isEnabled()?this.cssModelInternal:null}onNodeChanged(e){this.nodeInternal=e.data,this.updateModel(this.nodeInternal?this.nodeInternal.domModel().cssModel():null),this.onComputedStyleChanged(null)}updateModel(t){if(this.cssModelInternal===t)return;e.EventTarget.removeEventListeners(this.eventListeners),this.cssModelInternal=t;const i=t?t.domModel():null,s=t?t.target().model(n.ResourceTreeModel.ResourceTreeModel):null;t&&i&&s&&(this.eventListeners=[t.addEventListener(n.CSSModel.Events.StyleSheetAdded,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.StyleSheetRemoved,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.StyleSheetChanged,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.FontsUpdated,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.MediaQueryResultChanged,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.PseudoStateForced,this.onComputedStyleChanged,this),t.addEventListener(n.CSSModel.Events.ModelWasEnabled,this.onComputedStyleChanged,this),i.addEventListener(n.DOMModel.Events.DOMMutated,this.onDOMModelChanged,this),s.addEventListener(n.ResourceTreeModel.Events.FrameResized,this.onFrameResized,this)])}onComputedStyleChanged(e){delete this.computedStylePromise,this.dispatchEventToListeners("ComputedStyleChanged",e?.data??null)}onDOMModelChanged(e){const t=e.data;this.nodeInternal&&(this.nodeInternal===t||t.parentNode===this.nodeInternal.parentNode||t.isAncestor(this.nodeInternal))&&this.onComputedStyleChanged(null)}onFrameResized(){this.frameResizedTimer&&clearTimeout(this.frameResizedTimer),this.frameResizedTimer=window.setTimeout(function(){this.onComputedStyleChanged(null),delete this.frameResizedTimer}.bind(this),100)}elementNode(){const e=this.node();return e?e.enclosingElementOrSelf():null}async fetchComputedStyle(){const e=this.elementNode(),t=this.cssModel();if(!e||!t)return null;const n=e.id;return n?(this.computedStylePromise||(this.computedStylePromise=t.getComputedStyle(n).then(function(e,t){return e===this.elementNode()&&t?new ee(e,t):null}.bind(this,e))),this.computedStylePromise):null}}class ee{node;computedStyle;constructor(e,t){this.node=e,this.computedStyle=t}}var te=Object.freeze({__proto__:null,ComputedStyleModel:Z,ComputedStyle:ee});const ne=new CSSStyleSheet;ne.replaceSync(".styles-sidebar-pane-toolbar{border-bottom:1px solid var(--sys-color-divider);flex-shrink:0}.styles-sidebar-computed-style-widget{min-height:auto}.styles-pane-toolbar{width:100%}\n/*# sourceURL=computedStyleSidebarPane.css */\n");class ie{getLinkElement;getDOMNode;popover;constructor(e,t,n){this.getLinkElement=t,this.getDOMNode=n,this.popover=new i.PopoverHelper.PopoverHelper(e,this.handleRequest.bind(this),"elements.image-preview"),this.popover.setHasPadding(!0),this.popover.setTimeout(0,100)}handleRequest(e){const t=this.getLinkElement(e);if(!t)return null;const n=se.get(t);return n?{box:t.boxInWindow(),hide:void 0,show:async e=>{const i=this.getDOMNode(t);if(!i)return!1;const s=await f.ImagePreview.ImagePreview.loadDimensionsForNode(i),o=await f.ImagePreview.ImagePreview.build(i.domModel().target(),n,!0,{imageAltText:void 0,precomputedFeatures:s});return o&&e.contentElement.appendChild(o),Boolean(o)}}:null}hide(){this.popover.hidePopover()}static setImageUrl(e,t){return se.set(e,t),e}static getImageURL(e){return se.get(e)}}const se=new WeakMap,oe=new CSSStyleSheet;oe.replaceSync(":host{user-select:text}.platform-fonts{flex-shrink:0}.font-usage{color:var(--sys-color-token-subtle);padding-left:3px}.title{padding:0 5px;border-top:1px solid;border-bottom:1px solid;border-color:var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden;height:24px;background-color:var(--sys-color-surface2);display:flex;align-items:center}.font-stats-item{padding:5px 1em;div{margin-bottom:2px}&:not(:last-child){border-bottom:1px solid var(--sys-color-divider)}}\n/*# sourceURL=platformFontsWidget.css */\n");const re={renderedFonts:"Rendered Fonts",familyName:"Family name",postScriptName:"PostScript name",fontOrigin:"Font origin",networkResource:"Network resource",localFile:"Local file",dGlyphs:"{n, plural, =1 {(# glyph)} other {(# glyphs)}}"},ae=r.i18n.registerUIStrings("panels/elements/PlatformFontsWidget.ts",re),le=r.i18n.getLocalizedString.bind(void 0,ae);class de extends i.ThrottledWidget.ThrottledWidget{sharedModel;sectionTitle;fontStatsSection;constructor(e){super(!0),this.sharedModel=e,this.sharedModel.addEventListener("ComputedStyleChanged",this.update,this),this.sectionTitle=document.createElement("div"),this.sectionTitle.classList.add("title"),this.contentElement.classList.add("platform-fonts"),this.contentElement.appendChild(this.sectionTitle),this.sectionTitle.textContent=le(re.renderedFonts),this.fontStatsSection=this.contentElement.createChild("div","stats-section")}doUpdate(){const e=this.sharedModel.cssModel(),t=this.sharedModel.node();return t&&e?e.getPlatformFonts(t.id).then(this.refreshUI.bind(this,t)):Promise.resolve()}refreshUI(e,t){if(this.sharedModel.node()!==e)return;this.fontStatsSection.removeChildren();const n=!t||!t.length;if(this.sectionTitle.classList.toggle("hidden",n),!n&&t){t.sort((function(e,t){return t.glyphCount-e.glyphCount}));for(const e of t){const t=this.fontStatsSection.createChild("div","font-stats-item");t.createChild("div").textContent=`${re.familyName}: ${e.familyName}`;t.createChild("div").textContent=`${re.postScriptName}: ${e.postScriptName}`;const n=t.createChild("div"),i=e.isCustomFont?le(re.networkResource):le(re.localFile);n.textContent=`${re.fontOrigin}: ${i}`;const s=n.createChild("span","font-usage"),o=e.glyphCount;s.textContent=le(re.dGlyphs,{n:o})}}}wasShown(){super.wasShown(),this.registerCSSFiles([oe])}}var ce=Object.freeze({__proto__:null,PlatformFontsWidget:de});const he=n.CSSPropertyParser.ASTUtils,pe=n.CSSPropertyParser.matcherBase,ue=n.CSSPropertyParser.tokenizeDeclaration;class me{text;node;constructor(e,t){this.text=e,this.node=t}computedText(){return this.text}}class ge extends(pe(me)){accepts(e){return n.CSSMetadata.cssMetadata().isAngleAwareProperty(e)}matches(e,t){if("NumberLiteral"!==e.name)return null;const n=e.getChild("Unit");return n&&["deg","grad","rad","turn"].includes(t.ast.text(n))?new me(t.ast.text(e),e):null}}function ye(e,t){if("NumberLiteral"!==e.type.name)return null;const n=t.text(e);return Number(n.substring(0,n.length-t.text(e.getChild("Unit")).length))}class fe{text;node;space;color1;color2;constructor(e,t,n,i,s){this.text=e,this.node=t,this.space=n,this.color1=i,this.color2=s}}class Se extends(pe(fe)){accepts(e){return n.CSSMetadata.cssMetadata().isColorAwareProperty(e)}matches(e,t){if("CallExpression"!==e.name||"color-mix"!==t.ast.text(e.getChild("Callee")))return null;const n=ue("--property",t.getComputedText(e));if(!n)return null;const i=he.declValue(n.tree);if(!i)return null;const s=he.callArgs(i);if(3!==s.length)return null;const[o,r,a]=s;if(o.length<2||"in"!==n.text(he.stripComments(o).next().value)||r.length<1||a.length<1)return null;const l=r.filter((e=>"NumberLiteral"===e.type.name&&"%"===n.text(e.getChild("Unit")))),d=a.filter((e=>"NumberLiteral"===e.type.name&&"%"===n.text(e.getChild("Unit"))));if(l.length>1||d.length>1)return null;if(l[0]&&d[0]&&0===(ye(l[0],n)??0)&&0===(ye(d[0],n)??0))return null;const c=he.callArgs(e);return 3!==c.length?null:new fe(t.ast.text(e),e,c[0],c[1],c[2])}}class Ce{url;text;node;constructor(e,t,n){this.url=e,this.text=t,this.node=n}}class Ee extends(pe(Ce)){matches(e,t){if("CallLiteral"!==e.name)return null;const n=e.getChild("CallTag");if(!n||"url"!==t.ast.text(n))return null;const[,i,s,o]=he.siblings(n);if("("!==t.ast.text(i)||"ParenthesizedContent"!==s.name&&"StringLiteral"!==s.name||")"!==t.ast.text(o))return null;const r=t.ast.text(s),a="StringLiteral"===s.name?r.substr(1,r.length-2):r.trim();return new Ce(a,t.ast.text(e),e)}}class be{text;node;constructor(e,t){this.text=e,this.node=t}}class ve extends(pe(be)){matches(e,t){const n=t.ast.text(e);return"CallExpression"===e.name&&"linear-gradient"===t.ast.text(e.getChild("Callee"))?new be(n,e):null}accepts(e){return["background","background-image","-webkit-mask-image"].includes(e)}}class we{text;node;constructor(e,t){this.text=e,this.node=t}}class xe extends(pe(we)){accepts(e){return n.CSSMetadata.cssMetadata().isColorAwareProperty(e)}matches(t,n){const i=n.ast.text(t);if("ColorLiteral"===t.name)return new we(i,t);if("ValueName"===t.name&&e.Color.Nicknames.has(i))return new we(i,t);if("CallExpression"===t.name){const e=t.getChild("Callee");if(e&&n.ast.text(e).match(/^(rgba?|hsla?|hwba?|lab|lch|oklab|oklch|color)$/))return new we(i,t)}return null}}class Te{text;node;light;dark;constructor(e,t,n,i){this.text=e,this.node=t,this.light=n,this.dark=i}}class Me extends(pe(Te)){accepts(e){return n.CSSMetadata.cssMetadata().isColorAwareProperty(e)}matches(e,t){if("CallExpression"!==e.name||"light-dark"!==t.ast.text(e.getChild("Callee")))return null;const n=he.callArgs(e);return 2!==n.length||0===n[0].length||0===n[1].length?null:new Te(t.ast.text(e),e,n[0],n[1])}}class Ne{text;node;properyName;constructor(e,t,n){this.text=e,this.node=t,this.properyName=n}}class Ie extends(pe(Ne)){static isLinkableNameProperty(e){return["animation","animation-name","font-palette","position-try-options","position-try"].includes(e)}static identifierAnimationLonghandMap=new Map(Object.entries({normal:"direction",alternate:"direction",reverse:"direction","alternate-reverse":"direction",none:"fill-mode",forwards:"fill-mode",backwards:"fill-mode",both:"fill-mode",running:"play-state",paused:"play-state",infinite:"iteration-count",linear:"easing-function",ease:"easing-function","ease-in":"easing-function","ease-out":"easing-function","ease-in-out":"easing-function"}));matchAnimationNameInShorthand(e,t){const n=t.ast.text(e);if(!Ie.identifierAnimationLonghandMap.has(n))return new Ne(n,e,"animation");const i=he.split(he.siblings(he.declValue(t.ast.tree))).find((t=>t[0].from<=e.from&&t[t.length-1].to>=e.to));if(!i)return null;const s=t.getComputedTextRange(i[0],e),o=ue("--p",s);if(!o)return null;const r=Ie.identifierAnimationLonghandMap.get(n);for(let t=he.declValue(o.tree);t?.nextSibling;t=t.nextSibling)if("ValueName"===t.name){const i=Ie.identifierAnimationLonghandMap.get(o.text(t));if(i&&i===r)return new Ne(n,e,"animation")}return null}accepts(e){return Ie.isLinkableNameProperty(e)}matches(e,t){const{propertyName:n}=t.ast,i=t.ast.text(e),s=e.parent;if(!s)return null;const o="Declaration"===s.name,r="ArgList"===s.name&&"Callee"===s.prevSibling?.name&&"var"===t.ast.text(s.prevSibling),a=o||r,l="position-try"===n||"position-try-options"===n;return!n||"ValueName"!==e.name&&"VariableName"!==e.name||!a||"ValueName"===e.name&&l?null:"animation"===n?this.matchAnimationNameInShorthand(e,t):new Ne(i,e,n)}}class Pe{text;node;constructor(e,t){this.text=e,this.node=t}}class Oe extends(pe(Pe)){accepts(e){return n.CSSMetadata.cssMetadata().isBezierAwareProperty(e)}matches(e,t){const n=t.ast.text(e),s="ValueName"===e.name&&i.Geometry.CubicBezier.KeywordValues.has(n),o="CallExpression"===e.name&&["cubic-bezier","linear"].includes(t.ast.text(e.getChild("Callee")));return(s||o)&&y.AnimationTimingModel.AnimationTimingModel.parse(n)?new Pe(n,e):null}}class Le{text;node;constructor(e,t){this.text=e,this.node=t}}class Ae extends(pe(Le)){matches(e,t){return"StringLiteral"===e.name?new Le(t.ast.text(e),e):null}}class ke{text;node;shadowType;constructor(e,t,n){this.text=e,this.node=t,this.shadowType=n}}class Re extends(pe(ke)){accepts(e){return n.CSSMetadata.cssMetadata().isShadowProperty(e)}matches(e,t){if("Declaration"!==e.name)return null;const n=he.siblings(he.declValue(e)),i=t.ast.textRange(n[0],n[n.length-1]);return new ke(i,e,"text-shadow"===t.ast.propertyName?"textShadow":"boxShadow")}}class De{text;node;constructor(e,t){this.text=e,this.node=t}}class Fe extends(pe(De)){accepts(e){return n.CSSMetadata.cssMetadata().isFontAwareProperty(e)}matches(e,t){if("Declaration"===e.name)return null;const n="font-family"===t.ast.propertyName?y.FontEditorUtils.FontFamilyRegex:y.FontEditorUtils.FontPropertiesRegex,i=t.ast.text(e);return n.test(i)?new De(i,e):null}}class Ue{text;node;constructor(e,t){this.text=e,this.node=t}}class Be extends(pe(Ue)){matches(e,t){const n=t.ast.text(e),i=new RegExp(`^${y.CSSLengthUtils.CSSLengthRegex.source}$`).exec(n);return i&&0===i.index?new Ue(i[0],e):null}}class _e{text;node;lines;constructor(e,t,n){this.text=e,this.node=t,this.lines=n}}class Ve extends(pe(_e)){accepts(e){return n.CSSMetadata.cssMetadata().isGridAreaDefiningProperty(e)}matches(e,t){if("Declaration"!==e.name||t.hasUnresolvedVars(e))return null;const i=[];let s=[],o=!1,r=!1;const a=he.siblings(he.declValue(e));!function e(a,l=!1){for(const d of a)if(t.getMatch(d)instanceof n.CSSPropertyParser.VariableMatch){const n=ue("--property",t.getComputedText(d));if(!n)continue;const a=he.siblings(he.declValue(n.tree));if(0===a.length)continue;"StringLiteral"===a[0].name&&!o||"LineNames"===a[0].name&&!r?(i.push(s),s=[d]):s.push(d),e(a,!0)}else"BinaryExpression"===d.name?e(he.siblings(d.firstChild)):"StringLiteral"===d.name?(l||(o?s.push(d):(i.push(s),s=[d])),r=!0,o=!1):"LineNames"===d.name?(l||(r?s.push(d):(i.push(s),s=[d])),o=!r,r=!r):l||s.push(d)}(a),i.push(s);const l=t.ast.textRange(a[0],a[a.length-1]);return new _e(l,e,i.filter((e=>e.length>0)))}}class He{text;matching;node;functionName;args;constructor(e,t,n,i,s){this.text=e,this.matching=t,this.node=n,this.functionName=i,this.args=s}}class We extends(pe(He)){matches(e,t){if("CallExpression"!==e.name)return null;const n=t.ast.text(e.getChild("Callee"));if("anchor"!==n&&"anchor-size"!==n)return null;const[i]=he.callArgs(e);return i&&0!==i.length?new He(t.ast.text(e),t,e,n,i):null}}class je{text;matching;node;constructor(e,t,n){this.text=e,this.matching=t,this.node=n}}class ze extends(pe(je)){accepts(e){return"position-anchor"===e}matches(e,t){if("VariableName"!==e.name)return null;const n=t.ast.text(e);return new je(n,t,e)}}var $e=Object.freeze({__proto__:null,AngleMatch:me,AngleMatcher:ge,ColorMixMatch:fe,ColorMixMatcher:Se,URLMatch:Ce,URLMatcher:Ee,LinearGradientMatch:be,LinearGradientMatcher:ve,ColorMatch:we,ColorMatcher:xe,LightDarkColorMatch:Te,LightDarkColorMatcher:Me,LinkableNameMatch:Ne,LinkableNameMatcher:Ie,BezierMatch:Pe,BezierMatcher:Oe,StringMatch:Le,StringMatcher:Ae,ShadowMatch:ke,ShadowMatcher:Re,FontMatch:De,FontMatcher:Fe,LengthMatch:Ue,LengthMatcher:Be,GridTemplateMatch:_e,GridTemplateMatcher:Ve,AnchorFunctionMatch:He,AnchorFunctionMatcher:We,PositionAnchorMatch:je,PositionAnchorMatcher:ze});const Ke=["Layout","Text","Appearance","Animation","CSS Variables","Grid","Flex","Table","Generated Content","Other"],Ge=new Map([["Layout",["display","margin","padding","height","width","position","top","right","bottom","left","z-index","float","clear","overflow","resize","clip","visibility","box-sizing","align-content","align-items","align-self","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","justify-content","order","inline-size","block-size","min-inline-size","min-block-size","max-inline-size","max-block-size","min-width","max-width","min-height","max-height"]],["Text",["font","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","font-smoothing","direction","tab-size","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-indent","text-justify","text-overflow","text-shadow","text-transform","text-size-adjust","line-height","vertical-align","letter-spacing","word-spacing","white-space","word-break","word-wrap"]],["Appearance",["color","outline","outline-color","outline-offset","outline-style","Outline-width","border","border-image","background","cursor","box-shadow","≈","tap-highlight-color"]],["Animation",["animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","transition","transition-delay","transition-duration","transition-property","transition-timing-function"]],["Grid",["grid","grid-column","grid-row","order","place-items","place-content","place-self"]],["Flex",["flex","order","place-items","place-content","place-self"]],["Table",["border-collapse","border-spacing","caption-side","empty-cells","table-layout"]],["Generated Content",["content","quotes","counter-reset","counter-increment"]]]),Ye=new Map;for(const[e,t]of Ge)for(const n of t){Ye.has(n)||Ye.set(n,[]);Ye.get(n).push(e)}const qe=e=>Ye.has(e)?Ye.get(e):e.startsWith("--")?["CSS Variables"]:[],Xe=e=>{const t=n.CSSMetadata.cssMetadata(),i=t.canonicalPropertyName(e),s=qe(i);if(s.length>0)return s;const o=t.getShorthands(i);if(o)for(const e of o){const t=qe(e);if(t.length>0)return t}return["Other"]};class Qe extends i.Widget.VBox{computedStyleModelInternal;updateThrottler;updateWhenVisible;constructor(t){super(!0,t),this.element.classList.add("flex-none"),this.computedStyleModelInternal=new Z,this.computedStyleModelInternal.addEventListener("ComputedStyleChanged",this.onCSSModelChanged,this),this.updateThrottler=new e.Throttler.Throttler(100),this.updateWhenVisible=!1}node(){return this.computedStyleModelInternal.node()}cssModel(){return this.computedStyleModelInternal.cssModel()}computedStyleModel(){return this.computedStyleModelInternal}async doUpdate(){}update(){this.updateWhenVisible=!this.isShowing(),this.updateWhenVisible||this.updateThrottler.schedule(function(){return this.isShowing()?this.doUpdate():Promise.resolve()}.bind(this))}wasShown(){super.wasShown(),this.updateWhenVisible&&this.update()}onCSSModelChanged(e){}}var Je=Object.freeze({__proto__:null,ElementsSidebarPane:Qe});const Ze=new CSSStyleSheet;Ze.replaceSync(".styles-layers-pane{overflow:hidden;padding-left:2px;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);margin-top:0;padding-bottom:2px}.styles-layers-pane > div{font-weight:bold;margin:8px 4px 6px}.styles-layers-pane > table{width:100%;border-spacing:0}.styles-layers-pane td{padding:0}\n/*# sourceURL=layersWidget.css */\n");const et={cssLayersTitle:"CSS layers",toggleCSSLayers:"Toggle CSS Layers view"},tt=r.i18n.registerUIStrings("panels/elements/LayersWidget.ts",et),nt=r.i18n.getLocalizedString.bind(void 0,tt);let it,st;class ot extends i.Widget.Widget{cssModel;layerTreeComponent=new h.TreeOutline.TreeOutline;constructor(){super(!0),this.contentElement.className="styles-layers-pane",this.contentElement.setAttribute("jslog",`${s.pane("css-layers")}`),i.UIUtils.createTextChild(this.contentElement.createChild("div"),nt(et.cssLayersTitle)),this.contentElement.appendChild(this.layerTreeComponent),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.update,this)}updateModel(e){this.cssModel!==e&&(this.cssModel&&this.cssModel.removeEventListener(n.CSSModel.Events.StyleSheetChanged,this.update,this),this.cssModel=e,this.cssModel&&this.cssModel.addEventListener(n.CSSModel.Events.StyleSheetChanged,this.update,this))}async wasShown(){return super.wasShown(),this.registerCSSFiles([Ze]),this.update()}async update(){if(!this.isShowing())return;let e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),!e)return;if(this.updateModel(e.domModel().cssModel()),!this.cssModel)return;const t=e=>i=>{const s=i.subLayers,o=n.CSSModel.CSSModel.readableLayerName(i.name),r=i.order+": "+o,a=e?e+"."+o:o;return s?{treeNodeData:r,id:a,children:()=>Promise.resolve(s.sort(((e,t)=>e.order-t.order)).map(t(a)))}:{treeNodeData:r,id:a}},s=await this.cssModel.getRootLayer(e.id);this.layerTreeComponent.data={defaultRenderer:h.TreeOutline.defaultRenderer,tree:[t("")(s)]},await this.layerTreeComponent.expandRecursively(5)}async revealLayer(e){return this.isShowing()||Ls.instance().showToolbarPane(this,rt.instance().item()),await this.update(),this.layerTreeComponent.expandToAndSelectTreeNodeId("implicit outer layer."+e)}static instance(e={forceNew:null}){const{forceNew:t}=e;return it&&!t||(it=new ot),it}}class rt{button;constructor(){this.button=new i.Toolbar.ToolbarToggle(nt(et.toggleCSSLayers),"layers","layers-filled"),this.button.setVisible(!1),this.button.addEventListener("Click",this.clicked,this),this.button.element.classList.add("monospace"),this.button.element.setAttribute("jslog",`${s.toggleSubpane("css-layers").track({click:!0})}`)}static instance(e={forceNew:null}){const{forceNew:t}=e;return st&&!t||(st=new rt),st}clicked(){const e=ot.instance();Ls.instance().showToolbarPane(e.isShowing()?null:e,this.button)}item(){return this.button}}var at=Object.freeze({__proto__:null,LayersWidget:ot,ButtonProvider:rt});const lt=(e,t)=>void 0===t?dt(e):'<code class="unbreakable-text"><span class="property">'+e+"</span>: "+t+"</code>",dt=e=>'<code class="unbreakable-text"><span class="property">'+e+"</span></code>",ct=e=>'<code class="unbreakable-text">'+e+"</code>",ht=e=>{if(!e)return!1;const t=e.get("display");return"flex"===t||"inline-flex"===t},pt=new Set(["block","flow-root","inline-block","list-item","table-caption","table-cell"]),ut=new Set(["audio","canvas","embed","iframe","img","input","object","video"]),mt=e=>{if(!e)return!1;const t=e.get("display");return"grid"===t||"inline-grid"===t},gt={ruleViolatedBySameElementRuleReason:"The {REASON_PROPERTY_DECLARATION_CODE} property prevents {AFFECTED_PROPERTY_DECLARATION_CODE} from having an effect.",ruleViolatedBySameElementRuleFix:"Try setting {PROPERTY_NAME} to something other than {PROPERTY_VALUE}.",ruleViolatedBySameElementRuleChangeFlexOrGrid:"Try adding {DISPLAY_GRID_RULE} or {DISPLAY_FLEX_RULE} to make this element into a container.",ruleViolatedBySameElementRuleChangeSuggestion:"Try setting the {EXISTING_PROPERTY_DECLARATION} property to {TARGET_PROPERTY_DECLARATION}.",ruleViolatedByParentElementRuleReason:"The {REASON_PROPERTY_DECLARATION_CODE} property on the parent element prevents {AFFECTED_PROPERTY_DECLARATION_CODE} from having an effect.",ruleViolatedByParentElementRuleFix:"Try setting the {EXISTING_PARENT_ELEMENT_RULE} property on the parent to {TARGET_PARENT_ELEMENT_RULE}.",fontVariationSettingsWarning:"Value for setting “{PH1}” {PH2} is outside the supported range [{PH3}, {PH4}] for font-family “{PH5}”.",flexGridContainerPropertyRuleReason:"This element is a {CONTAINER_DISPLAY_NAME} item, i.e. a child of a {CONTAINER_DISPLAY_NAME} container, but {PROPERTY_NAME} only applies to containers.",flexGridContainerPropertyRuleFix:"Try setting the {PROPERTY_NAME} on the container element or use {ALTERNATIVE_PROPERTY_NAME} instead."},yt=r.i18n.registerUIStrings("panels/elements/CSSRuleValidator.ts",gt),ft=r.i18n.getLocalizedString.bind(void 0,yt);class St{#e;#t;#n;constructor(e,t,n){this.#e=e,this.#t=t,this.#n=n}getMessage(){return this.#e}getPossibleFixMessage(){return this.#t}getLearnMoreLink(){return this.#n}}class Ct{getMetricType(){return 0}#i;constructor(e){this.#i=e}getApplicableProperties(){return this.#i}}class Et extends Ct{constructor(){super(["align-content"])}getMetricType(){return 1}getHint(e,t){if(!t)return;const n=ht(t);if(!n&&!(e=>{if(!e)return!1;const t=e.get("display");if(!t)return!1;const n=t.split(" ");return!(n.length>3)&&(3===n.length?"list-item"===n[2]:2===n.length?"list-item"===n[1]&&"inline"!==n[0]:pt.has(n[0]))})(t)&&!mt(t)){const e=lt("display",t?.get("display")),n=dt("align-content");return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:e,AFFECTED_PROPERTY_DECLARATION_CODE:n}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("display"),PROPERTY_VALUE:ct(t?.get("display"))}))}if(!n)return;if("nowrap"!==t.get("flex-wrap"))return;const i=lt("flex-wrap","nowrap"),s=dt("align-content");return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("flex-wrap"),PROPERTY_VALUE:ct("nowrap")}))}}class bt extends Ct{constructor(){super(["flex","flex-basis","flex-grow","flex-shrink"])}getMetricType(){return 2}getHint(e,t,n){if(!n)return;if(ht(n))return;const i=lt("display",n?.get("display")),s=dt(e),o=lt("display","flex");return new St(ft(gt.ruleViolatedByParentElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedByParentElementRuleFix,{EXISTING_PARENT_ELEMENT_RULE:i,TARGET_PARENT_ELEMENT_RULE:o}))}}class vt extends Ct{constructor(){super(["flex-direction","flex-flow","flex-wrap"])}getMetricType(){return 3}getHint(e,t){if(!t)return;if(ht(t))return;const n=lt("display",t?.get("display")),i=lt("display","flex"),s=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:n,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleChangeSuggestion,{EXISTING_PROPERTY_DECLARATION:n,TARGET_PROPERTY_DECLARATION:i}))}}class wt extends Ct{constructor(){super(["grid","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template","grid-template-areas","grid-template-columns","grid-template-rows"])}getMetricType(){return 4}getHint(e,t){if(mt(t))return;const n=lt("display",t?.get("display")),i=lt("display","grid"),s=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:n,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleChangeSuggestion,{EXISTING_PROPERTY_DECLARATION:n,TARGET_PROPERTY_DECLARATION:i}))}}class xt extends Ct{constructor(){super(["grid-area","grid-column","grid-row","grid-row-end","grid-row-start","justify-self"])}getMetricType(){return 5}getHint(e,t,n){if(!n)return;if(mt(n))return;const i=lt("display",n?.get("display")),s=lt("display","grid"),o=dt(e);return new St(ft(gt.ruleViolatedByParentElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:o}),ft(gt.ruleViolatedByParentElementRuleFix,{EXISTING_PARENT_ELEMENT_RULE:i,TARGET_PARENT_ELEMENT_RULE:s}))}}class Tt extends Ct{constructor(){super(["place-self","align-self","order"])}getMetricType(){return 12}getHint(e,t,n){if(!n)return;if(ht(n)||mt(n))return;const i=lt("display",n?.get("display")),s=`${lt("display","flex")} or ${lt("display","grid")}`,o=dt(e);return new St(ft(gt.ruleViolatedByParentElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:o}),ft(gt.ruleViolatedByParentElementRuleFix,{EXISTING_PARENT_ELEMENT_RULE:i,TARGET_PARENT_ELEMENT_RULE:s}))}}class Mt extends Ct{constructor(){super(["justify-content","place-content","align-items"])}getMetricType(){return 6}getHint(e,t,n){if(!t)return;if(ht(t)||mt(t))return;if(n&&(ht(n)||mt(n))){const t=ct(n.get("display")),i=dt(e),s=dt("justify-content"===e?"justify-self":"align-self");return new St(ft(gt.flexGridContainerPropertyRuleReason,{CONTAINER_DISPLAY_NAME:t,PROPERTY_NAME:i}),ft(gt.flexGridContainerPropertyRuleFix,{PROPERTY_NAME:i,ALTERNATIVE_PROPERTY_NAME:s}))}const i=lt("display",t.get("display")),s=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleChangeFlexOrGrid,{DISPLAY_GRID_RULE:lt("display","grid"),DISPLAY_FLEX_RULE:lt("display","flex")}))}}class Nt extends Ct{constructor(){super(["gap","column-gap","row-gap","grid-gap","grid-column-gap","grid-column-end","grid-row-gap"])}getMetricType(){return 7}getHint(e,t){if(!t)return;if((e=>{if(!e)return!1;const t=e.get("column-width"),n=e.get("column-count");return"auto"!==t||"auto"!==n})(t)||ht(t)||mt(t))return;const n=lt("display",t?.get("display")),i=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:n,AFFECTED_PROPERTY_DECLARATION_CODE:i}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("display"),PROPERTY_VALUE:ct(t?.get("display"))}))}}class It extends Ct{constructor(){super(["padding","padding-top","padding-right","padding-bottom","padding-left"])}getMetricType(){return 8}getHint(e,t){const n=t?.get("display");if(!n)return;if(!["table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column"].includes(n))return;const i=lt("display",t?.get("display")),s=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("display"),PROPERTY_VALUE:ct(t?.get("display"))}))}}class Pt extends Ct{constructor(){super(["top","right","bottom","left"])}getMetricType(){return 9}getHint(e,t){const n=t?.get("position");if(!n)return;if("static"!==n)return;const i=lt("position",t?.get("position")),s=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:i,AFFECTED_PROPERTY_DECLARATION_CODE:s}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("position"),PROPERTY_VALUE:ct(t?.get("position"))}))}}class Ot extends Ct{constructor(){super(["z-index"])}getMetricType(){return 10}getHint(e,t,n){const i=t?.get("position");if(!i)return;if(["absolute","relative","fixed","sticky"].includes(i)||ht(n)||mt(n))return;const s=lt("position",t?.get("position")),o=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:s,AFFECTED_PROPERTY_DECLARATION_CODE:o}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("position"),PROPERTY_VALUE:ct(t?.get("position"))}))}}class Lt extends Ct{constructor(){super(["width","height"])}getMetricType(){return 11}getHint(e,t,n,i){if(!t||!i)return;if(!(e=>!!e&&"inline"===e.get("display"))(t))return;if((e=>!!e&&ut.has(e))(i))return;const s=lt("display",t?.get("display")),o=dt(e);return new St(ft(gt.ruleViolatedBySameElementRuleReason,{REASON_PROPERTY_DECLARATION_CODE:s,AFFECTED_PROPERTY_DECLARATION_CODE:o}),ft(gt.ruleViolatedBySameElementRuleFix,{PROPERTY_NAME:dt("display"),PROPERTY_VALUE:ct(t?.get("display"))}))}}class At extends Ct{constructor(){super(["font-variation-settings"])}getMetricType(){return 13}getHint(e,t,i,s,o){if(!t)return;const r=t.get("font-variation-settings");if(!r)return;const a=t.get("font-family");if(!a)return;const l=new Set(n.CSSPropertyParser.parseFontFamily(a)),d=(o||[]).filter((e=>l.has(e.getFontFamily()))),c=n.CSSPropertyParser.parseFontVariationSettings(r),h=[];for(const e of c)for(const t of d){const n=t.getVariationAxisByTag(e.tag);n&&((e.value<n.minValue||e.value>n.maxValue)&&h.push(ft(gt.fontVariationSettingsWarning,{PH1:e.tag,PH2:e.value,PH3:n.minValue,PH4:n.maxValue,PH5:t.getFontFamily()})))}return h.length?new St(h.join(" "),""):void 0}}const kt=[Et,vt,Mt,bt,Tt,At,wt,xt,Nt,It,Pt,Lt,Ot],Rt=(()=>{const e=new Map;for(const t of kt){const n=new t,i=n.getApplicableProperties();for(const t of i){let i=e.get(t);void 0===i&&(i=[]),i.push(n),e.set(t,i)}}return e})();var Dt=Object.freeze({__proto__:null,Hint:St,CSSRuleValidator:Ct,AlignContentValidator:Et,FlexItemValidator:bt,FlexContainerValidator:vt,GridContainerValidator:wt,GridItemValidator:xt,FlexOrGridItemValidator:Tt,FlexGridValidator:Mt,MulticolFlexGridValidator:Nt,PaddingValidator:It,PositionValidator:Pt,ZIndexValidator:Ot,SizingValidator:Lt,FontVariationSettingsValidator:At,cssRuleValidatorsMap:Rt});function Ft(e){const{name:t,value:n}=e;return`${t.startsWith("--")?`'${t}'`:t.replace(/-([a-z])/gi,((e,t)=>t.toUpperCase()))}: ${`'${n.replaceAll("'","\\'")}'`}`}var Ut=Object.freeze({__proto__:null,getCssDeclarationAsJavascriptProperty:Ft});const Bt=n.CSSPropertyParser.ASTUtils,_t=p.StylePropertyEditor.FlexboxEditor,Vt=p.StylePropertyEditor.GridEditor,Ht=new WeakMap,Wt={shiftClickToChangeColorFormat:"Shift + Click to change color format.",openColorPickerS:"Open color picker. {PH1}",togglePropertyAndContinueEditing:"Toggle property and continue editing",revealInSourcesPanel:"Reveal in Sources panel",copyDeclaration:"Copy declaration",copyProperty:"Copy property",copyValue:"Copy value",copyRule:"Copy rule",copyAllDeclarations:"Copy all declarations",copyAllCSSChanges:"Copy all CSS changes",viewComputedValue:"View computed value",flexboxEditorButton:"Open `flexbox` editor",gridEditorButton:"Open `grid` editor",copyCssDeclarationAsJs:"Copy declaration as JS",copyAllCssDeclarationsAsJs:"Copy all declarations as JS"},jt=r.i18n.registerUIStrings("panels/elements/StylePropertyTreeElement.ts",Wt),zt=r.i18n.getLocalizedString.bind(void 0,jt),$t=new WeakMap;class Kt{#s;#o;constructor(e,t){this.#s=e,this.#o=t}matcher(){return new n.CSSPropertyParser.VariableMatcher(this.computedText.bind(this))}resolveVariable(e){return this.#r.computeCSSVariable(this.#o,e.name)}fallbackValue(e){return 0===e.fallback.length||e.matching.hasUnresolvedVarsRange(e.fallback[0],e.fallback[e.fallback.length-1])?null:e.matching.getComputedTextRange(e.fallback[0],e.fallback[e.fallback.length-1])}computedText(e){return this.resolveVariable(e)?.value??this.fallbackValue(e)}render(t,n){const s=t.fallback.length>0?ui.render(t.fallback,n):void 0,{declaration:o,value:r}=this.resolveVariable(t)??{},a=!r,l=r??this.fallbackValue(t),d=new y.LinkSwatch.CSSVarSwatch;if(d.data={computedValue:l,variableName:t.name,fromFallback:a,fallbackText:t.fallback.map((e=>n.ast.text(e))).join(" "),onLinkActivate:e=>this.#a(o??e)},s?.nodes.length){d.appendChild(document.createTextNode(`var(${t.name}`));const e=d.appendChild(document.createElement("span"));e.appendChild(document.createTextNode(", ")),e.slot="fallback",s.nodes.forEach((t=>e.appendChild(t))),d.appendChild(document.createTextNode(")"))}else i.UIUtils.createTextChild(d,t.text);d.link&&this.#l.addPopover(d.link,{contents:()=>this.#s.getVariablePopoverContents(t.name,r??null),jslogContext:"elements.css-var"});const c=l&&e.Color.parse(l);if(!c)return[d];const h=new Yt(this.#s).renderColorSwatch(c,d);return n.addControl("color",h),a&&s?.cssControls.get("color")?.forEach((e=>e.addEventListener(y.ColorSwatch.ColorChangedEvent.eventName,(e=>{h.setColor(e.data.color)})))),[h]}get#l(){return this.#s.parentPane()}get#r(){return this.#s.matchedStyles()}#a(e){o.userMetrics.actionTaken(o.UserMetrics.Action.CustomPropertyLinkClicked),o.userMetrics.swatchActivated(0),e instanceof n.CSSProperty.CSSProperty?this.#l.revealProperty(e):e instanceof n.CSSMatchedStyles.CSSRegisteredProperty?this.#l.jumpToProperty("initial-value",e.propertyName(),Yn):this.#l.jumpToProperty(e)||this.#l.jumpToProperty("initial-value",e,Yn)}}class Gt{matcher(){return new ve}render(e,t){const n=Bt.children(e.node),{nodes:i,cssControls:s}=ui.render(n,t),o=s.get("angle"),r=1===o?.length?o[0]:null;if(r instanceof y.CSSAngle.CSSAngle){r.updateProperty(t.matchedResult.getComputedText(e.node));const n=Bt.callArgs(e.node),i=n[0]?.find((e=>t.matchedResult.getMatch(e)instanceof me)),s=i&&t.matchedResult.getMatch(i);s&&r.addEventListener(y.InlineEditorUtils.ValueChangedEvent.eventName,(n=>{r.updateProperty(t.matchedResult.getComputedText(e.node,new Map([[s,n.data.value]])))}))}return i}}class Yt{treeElement;constructor(e){this.treeElement=e}matcher(){return new xe}#d(t,n){const i=document.createElement("span");if("ColorLiteral"===t.node.name||"ValueName"===t.node.name&&e.Color.Nicknames.has(t.text))return i.appendChild(document.createTextNode(t.text)),{valueChild:i};const{cssControls:s}=ui.renderInto(Bt.children(t.node),n,i);return{valueChild:i,cssControls:s}}render(t,n){const{valueChild:i,cssControls:s}=this.#d(t,n);let o=n.matchedResult.getComputedText(t.node);if("CallExpression"===t.node.name&&o.match(/^[^)]*\(\W*from\W+/)&&!n.matchedResult.hasUnresolvedVars(t.node)&&CSS.supports("color",o)){const e=document.body.appendChild(document.createElement("span"));e.style.backgroundColor=o,o=window.getComputedStyle(e).backgroundColor?.toString()||o,e.remove()}const r=e.Color.parse(o);if(!r)return[document.createTextNode(o)];const a=this.renderColorSwatch(r,i);if(n.addControl("color",a),s&&"CallExpression"===t.node.name&&n.ast.text(t.node.getChild("Callee")).match(/^(hsla?|hwba?)/)){const[t]=s.get("angle")??[];t instanceof y.CSSAngle.CSSAngle&&(t.updateProperty(a.getColor()?.asString()??""),t.addEventListener(y.InlineEditorUtils.ValueChangedEvent.eventName,(n=>{const i=e.Color.parseHueNumeric(n.data.value),s=a.getColor();i&&s&&(s.is("hsl")||s.is("hsla")?a.renderColor(new e.Color.HSL(i,s.s,s.l,s.alpha)):(s.is("hwb")||s.is("hwba"))&&a.renderColor(new e.Color.HWB(i,s.w,s.b,s.alpha)),t.updateProperty(a.getColor()?.asString()??""))})))}return[a]}renderColorSwatch(t,n){const i=this.treeElement.editable(),s=zt(Wt.shiftClickToChangeColorFormat),r=i?zt(Wt.openColorPickerS,{PH1:s}):"",a=new y.ColorSwatch.ColorSwatch(r);a.setReadonly(!i),t&&a.renderColor(t),n||(n=a.createChild("span"),t&&(n.textContent=t.getAuthoredText()??t.asString())),a.appendChild(n);if(a.addEventListener(y.ColorSwatch.ClickEvent.eventName,(()=>{o.userMetrics.swatchActivated(2)})),a.addEventListener(y.ColorSwatch.ColorChangedEvent.eventName,(()=>{this.treeElement.applyStyleText(this.treeElement.renderedPropertyText(),!1)})),i){const t=new q(this.treeElement,this.treeElement.parentPane().swatchPopoverHelper(),a);t.addEventListener("colorchanged",(t=>{const n=e.Color.parse(t.data);n&&a.setColorText(n)})),this.#c(t)}return a}async#c(e){const t=this.treeElement.parentPane().cssModel(),n=this.treeElement.node();if("color"!==this.treeElement.property.name||!t||!n||void 0===n.id)return;const i=new g.ContrastInfo.ContrastInfo(await t.getBackgroundColors(n.id));e.setContrastInfo(i)}}class qt{#s;constructor(e){this.#s=e}matcher(){return new Me}render(t,n){const i=document.createElement("span");i.appendChild(document.createTextNode("light-dark("));const s=i.appendChild(document.createElement("span"));i.appendChild(document.createTextNode(", "));const o=i.appendChild(document.createElement("span"));i.appendChild(document.createTextNode(")"));const{cssControls:r}=ui.renderInto(t.light,n,s),{cssControls:a}=ui.renderInto(t.dark,n,o);if(n.matchedResult.hasUnresolvedVars(t.node))return[i];if(!e.Color.parse(n.matchedResult.getComputedTextRange(t.light[0],t.light[t.light.length-1])))return[i];const l=new Yt(this.#s).renderColorSwatch(void 0,i);return n.addControl("color",l),this.applyColorScheme(t,n,l,s,o,r,a),[l]}async applyColorScheme(t,n,i,s,o,r,a){const l=await this.#h(t);if(!l)return;const d=(l===t.light?r:a).get("color");d?.forEach((e=>e.addEventListener(y.ColorSwatch.ColorChangedEvent.eventName,(e=>i.setColor(e.data.color)))));const c=l===t.light?o:s,h=n.matchedResult.getComputedTextRange(l[0],l[l.length-1]),p=h&&e.Color.parse(h);c.style.textDecoration="line-through",p&&i.renderColor(p)}async#h(e){const t=this.#s.getComputedStyle("color-scheme")?.split(" ")??[],n=t.includes("light"),i=t.includes("dark");if(!i&&!n)return e.light;if(!n)return e.dark;if(!i)return e.light;switch(await(this.#s.parentPane().cssModel()?.colorScheme())){case"dark":return e.dark;case"light":return e.light;default:return}}}class Xt{#l;constructor(e){this.#l=e}render(e,t){const n=(e,t)=>{if(e instanceof y.ColorMixSwatch.ColorMixSwatch||e instanceof y.ColorSwatch.ColorSwatch){e instanceof y.ColorSwatch.ColorSwatch?e.addEventListener(y.ColorSwatch.ColorChangedEvent.eventName,(e=>t(e.data.color.getAuthoredText()??e.data.color.asString()))):e.addEventListener("colorChanged",(e=>t(e.data.text)));const n=e.getText();if(n)return t(n),!0}return!1},i=document.createElement("span");i.appendChild(document.createTextNode("color-mix(")),ui.renderInto(e.space,t,i),i.appendChild(document.createTextNode(", "));const s=ui.renderInto(e.color1,t,i).cssControls.get("color")??[];i.appendChild(document.createTextNode(", "));const o=ui.renderInto(e.color2,t,i).cssControls.get("color")??[];if(i.appendChild(document.createTextNode(")")),t.matchedResult.hasUnresolvedVars(e.node)||1!==s.length||1!==o.length)return[i];const r=new y.ColorMixSwatch.ColorMixSwatch;if(!n(s[0],(e=>r.setFirstColor(e)))||!n(o[0],(e=>r.setSecondColor(e))))return[i];const a=e.space.map((e=>t.matchedResult.getComputedText(e))).join(" "),l=e.color1.map((e=>t.matchedResult.getComputedText(e))).join(" "),d=e.color2.map((e=>t.matchedResult.getComputedText(e))).join(" ");return r.appendChild(i),r.setColorMixText(`color-mix(${a}, ${l}, ${d})`),r.setRegisterPopoverCallback((e=>{e.icon&&this.#l.addPopover(e.icon,{contents:()=>{const t=e.mixedColor();if(!t)return;const n=document.createElement("span");n.style.padding="11px 7px";const i=t.as("hex"),s=i.isGamutClipped()?t.asString():i.asString();return s?(n.appendChild(document.createTextNode(s)),n):void 0},jslogContext:"elements.css-color-mix"})})),t.addControl("color",r),[r]}matcher(){return new Se}}class Qt{#s;constructor(e){this.#s=e}render(e,t){const n=e.text;if(!this.#s.editable())return[document.createTextNode(n)];const i=new y.CSSAngle.CSSAngle;i.setAttribute("jslog",`${s.showStyleEditor().track({click:!0}).context("css-angle")}`);const r=document.createElement("span");return r.textContent=n,i.data={angleText:n,containingPane:this.#s.parentPane().element.enclosingNodeOrSelfWithClass("style-panes-wrapper")},i.append(r),i.addEventListener("popovertoggled",(({data:e})=>{const t=this.#s.section();t&&(e.open&&(this.#s.parentPane().hideAllPopovers(),this.#s.parentPane().activeCSSAngle=i,o.userMetrics.swatchActivated(7)),t.element.classList.toggle("has-open-popover",e.open),this.#s.parentPane().setEditingStyle(e.open),e.open||this.#s.applyStyleText(this.#s.renderedPropertyText(),!0))})),i.addEventListener("valuechanged",(async({data:e})=>{r.textContent=e.value,await this.#s.applyStyleText(this.#s.renderedPropertyText(),!1)})),i.addEventListener("unitchanged",(({data:e})=>{r.textContent=e.value})),t.addControl("angle",i),[i]}matcher(){return new ge}}class Jt{#s;constructor(e){this.#s=e}#p(e){switch(e.properyName){case"animation":case"animation-name":return{jslogContext:"css-animation-name",metric:1,ruleBlock:"@keyframes",isDefined:Boolean(this.#s.matchedStyles().keyframes().find((t=>t.name().text===e.text)))};case"font-palette":return{jslogContext:"css-font-palette",metric:null,ruleBlock:"@font-palette-values",isDefined:this.#s.matchedStyles().fontPaletteValuesRule()?.name().text===e.text};case"position-try":case"position-try-options":return{jslogContext:"css-position-try",metric:10,ruleBlock:"@position-try",isDefined:Boolean(this.#s.matchedStyles().positionTryRules().find((t=>t.name().text===e.text)))}}}render(e){const t=new y.LinkSwatch.LinkSwatch;i.UIUtils.createTextChild(t,e.text);const{metric:n,jslogContext:s,ruleBlock:r,isDefined:a}=this.#p(e);return t.data={text:e.text,isDefined:a,onLinkActivate:()=>{n&&o.userMetrics.swatchActivated(n),this.#s.parentPane().jumpToSectionBlock(`${r} ${e.text}`)},jslogContext:s},[t]}matcher(){return new Ie}}class Zt{#s;constructor(e){this.#s=e}render(e){return[this.renderSwatch(e)]}renderSwatch(e){if(!this.#s.editable())return document.createTextNode(e.text);const t=this.#s.parentPane().swatchPopoverHelper(),n=y.Swatches.BezierSwatch.create();return n.iconElement().addEventListener("click",(()=>{o.userMetrics.swatchActivated(3)})),n.setBezierText(e.text),new Y({treeElement:this.#s,swatchPopoverHelper:t,swatch:n}),n}matcher(){return new Oe}}class en{#u;#m;#g;constructor(e,t,n){this.#m=e,this.#u=t,this.#g=n}isBoxShadow(){return"boxShadow"===this.#m}inset(){return Boolean(this.#u.find((e=>"inset"===e.propertyType)))}#y(e){return this.#u.find((t=>t.propertyType===e))?.length??y.CSSShadowEditor.CSSLength.zero()}offsetX(){return this.#y("x")}offsetY(){return this.#y("y")}blurRadius(){return this.#y("blur")}spreadRadius(){return this.#y("spread")}#f(e){return Boolean(e.expansionContext&&e.source)}#S(e){if(this.#f(e)){const t=e.source;this.#u.filter((e=>e.source===t)).forEach((e=>{e.source=null}))}}#C(e){const t=this.#u.findIndex((t=>t.propertyType===e)),n=t>=0?this.#u[t]:void 0;return n&&this.#S(n),{property:n,index:t}}setInset(e){if(!this.isBoxShadow())return;const{property:t,index:n}=this.#C("inset");t?e||this.#u.splice(n,1):this.#u.unshift({value:"inset",source:null,expansionContext:null,propertyType:"inset"})}#E(e,t){const{property:n}=this.#C(t);if(n)n.value=e.asCSSText(),n.length=e,n.source=null;else{const n=1+this.#u.findLastIndex((e=>"y"===e.propertyType||"spread"===t&&"blur"===e.propertyType));n>0&&n<this.#u.length&&this.#f(this.#u[n])&&this.#u[n-1].source===this.#u[n].source&&this.#S(this.#u[n]),this.#u.splice(n,0,{value:e.asCSSText(),length:e,source:null,expansionContext:null,propertyType:t})}}setOffsetX(e){this.#E(e,"x")}setOffsetY(e){this.#E(e,"y")}setBlurRadius(e){this.#E(e,"blur")}setSpreadRadius(e){this.isBoxShadow()&&this.#E(e,"spread")}renderContents(e){e.removeChildren();const t=e.createChild("span");let n=null;for(const e of this.#u)e.source&&e.source===n||(e!==this.#u[0]&&t.append(" "),e.source?t.append(...ui.render(e.source,this.#g).nodes):"string"==typeof e.value?t.append(e.value):t.append(...ui.render(e.value,e.expansionContext??this.#g).nodes)),n=e.source}}class tn{#s;constructor(e){this.#s=e}shadowModel(e,t,i){const s=[],o=["spread","blur","y","x"];let r=!0;const a=e.map((e=>({value:e,source:e,match:i.matchedResult.getMatch(e),expansionContext:null})));for(let e=a.shift();e;e=a.shift()){const{value:l,source:d,match:c,expansionContext:h}=e,p=(h??i).ast.text(l);if("NumberLiteral"===l.name){if(!r)return null;const e=o.pop();if(void 0===e||"spread"===e&&"textShadow"===t)return null;const n=y.CSSShadowEditor.CSSLength.parse(p);if(!n)return null;s.push({value:l,source:d,length:n,propertyType:e,expansionContext:h})}else if(c instanceof n.CSSPropertyParser.VariableMatch){const e=i.matchedResult.getComputedText(l),t=n.CSSPropertyParser.tokenizeDeclaration("--property",e);if(!t)return null;const s=n.CSSPropertyParser.BottomUpTreeMatching.walkExcludingSuccessors(t,[new xe]);if(s.hasUnresolvedVars(s.ast.tree))return null;a.unshift(...Bt.siblings(Bt.declValue(s.ast.tree)).map((e=>({value:e,source:l,match:s.getMatch(e),expansionContext:new pi(t,i.renderers,s)}))))}else if(r=4===o.length,"ValueName"===l.name&&"inset"===p.toLowerCase()){if("textShadow"===t||s.find((({propertyType:e})=>"inset"===e)))return null;s.push({value:l,source:d,propertyType:"inset",expansionContext:h})}else if(c instanceof we||c instanceof fe){if(s.find((({propertyType:e})=>"color"===e)))return null;s.push({value:l,source:d,propertyType:"color",expansionContext:h})}else if("Comment"!==l.name&&"Important"!==l.name)return null}return o.length>2?null:new en(t,s,i)}render(e,t){const n=Bt.split(Bt.siblings(Bt.declValue(e.node))),i=[];for(const r of n){const a=this.shadowModel(r,e.shadowType,t),l=r.find((e=>"Important"===e.name));if(r!==n[0]&&i.push(document.createTextNode(", ")),!a){const{nodes:e}=ui.render(r,t);i.push(...e);continue}const d=new y.Swatches.CSSShadowSwatch(a);d.setAttribute("jslog",`${s.showStyleEditor("css-shadow").track({click:!0})}`),d.iconElement().addEventListener("click",(()=>{o.userMetrics.swatchActivated(4)})),a.renderContents(d);new X(this.#s,this.#s.parentPane().swatchPopoverHelper(),d).addEventListener("shadowChanged",(()=>{a.renderContents(d),this.#s.applyStyleText(this.#s.renderedPropertyText(),!1)})),i.push(d),l&&i.push(document.createTextNode(" "),...ui.render(l,t).nodes)}return i}matcher(){return new Re}}class nn{treeElement;constructor(e){this.treeElement=e}render(e){return this.treeElement.section().registerFontProperty(this.treeElement),[document.createTextNode(e.text)]}matcher(){return new Fe}}class sn{render(t,n){if(t.lines.length<=1)return ui.render(Bt.siblings(Bt.declValue(t.node)),n).nodes;const s=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),o=document.createDocumentFragment();for(const e of t.lines){const t=ui.render(e,n),r=i.Fragment.html`<br /><span class='styles-clipboard-only'>${s.repeat(2)}</span>`;o.append(r,...t.nodes)}return[o]}matcher(){return new Ve}}class on{#s;constructor(e){this.#s=e}render(e,t){const n=e.text;if(!this.#s.editable())return[document.createTextNode(n)];const i=new y.CSSLength.CSSLength,s=document.createElement("span");s.textContent=n,i.data={lengthText:n,overloaded:this.#s.overloaded()},i.append(s);return i.addEventListener("valuechanged",(e=>{const{data:t}=e;s.textContent=t.value,this.#s.parentPane().setEditingStyle(!0),this.#s.applyStyleText(this.#s.renderedPropertyText(),!1)})),i.addEventListener("draggingfinished",(()=>{this.#s.parentPane().setEditingStyle(!1)})),[i]}matcher(){return new Be}}async function rn(t,i,s){const o=await(i.node()?.getAnchorBySpecifier(s.identifier))??void 0,r=new p.AnchorFunctionLinkSwatch.AnchorFunctionLinkSwatch({identifier:s.identifier,anchorNode:o,needsSpace:s.needsSpace,onLinkActivate:()=>{o&&e.Revealer.reveal(o,!1)},onMouseEnter:()=>{o?.highlight()},onMouseLeave:()=>{n.OverlayModel.OverlayModel.hideDOMNodeHighlight()}});t.removeChildren(),t.appendChild(r)}class an{#s;constructor(e){this.#s=e}anchorDecoratedForTest(){}async#b(e,t){await rn(e,this.#s,{identifier:t,needsSpace:!0}),this.anchorDecoratedForTest()}render(e,t){const n=document.createElement("span");n.appendChild(document.createTextNode(`${e.functionName}(`));const i=e.matching.ast.text(e.args[0]),s=i.startsWith("--"),o=document.createElement("span");s&&(o.textContent=`${i} `),n.appendChild(o);const r=n.appendChild(document.createElement("span"));return s?ui.renderInto(e.args.slice(1),t,r):ui.renderInto(e.args,t,r),this.#b(o,s?i:void 0),n.appendChild(document.createTextNode(")")),[n]}matcher(){return new We}}class ln{#s;constructor(e){this.#s=e}anchorDecoratedForTest(){}render(e){const t=document.createElement("span");return t.appendChild(document.createTextNode(e.text)),rn(t,this.#s,{identifier:e.text,needsSpace:!1}).then((()=>this.anchorDecoratedForTest())),[t]}matcher(){return new ze}}class dn extends i.TreeOutline.TreeElement{style;matchedStylesInternal;property;inheritedInternal;overloadedInternal;parentPaneInternal;#v;isShorthand;applyStyleThrottler;newProperty;expandedDueToFilter;valueElement;nameElement;expandElement;originalPropertyText;hasBeenEditedIncrementally;prompt;lastComputedValue;computedStyles=null;parentsComputedStyles=null;contextForTest;#w;constructor({stylesPane:t,section:n,matchedStyles:i,property:s,isShorthand:o,inherited:r,overloaded:a,newProperty:l}){super("",o,s.name.startsWith("--")?"custom-property":s.name),this.style=s.ownerStyle,this.matchedStylesInternal=i,this.property=s,this.inheritedInternal=r,this.overloadedInternal=a,this.selectable=!1,this.parentPaneInternal=t,this.#v=n,this.isShorthand=o,this.applyStyleThrottler=new e.Throttler.Throttler(0),this.newProperty=l,this.newProperty&&(this.listItemElement.textContent=""),this.expandedDueToFilter=!1,this.valueElement=null,this.nameElement=null,this.expandElement=null,this.originalPropertyText="",this.hasBeenEditedIncrementally=!1,this.prompt=null,this.lastComputedValue=null,this.#w=s.propertyText||""}matchedStyles(){return this.matchedStylesInternal}editable(){const e=this.parent instanceof dn&&this.parent.isShorthand,t=Boolean(this.style.styleSheetId&&this.style.range);return!e&&t}inherited(){return this.inheritedInternal}overloaded(){return this.overloadedInternal}setOverloaded(e){e!==this.overloadedInternal&&(this.overloadedInternal=e,this.updateState())}setComputedStyles(e){this.computedStyles=e}getComputedStyle(e){return this.computedStyles?.get(e)??null}setParentsComputedStyles(e){this.parentsComputedStyles=e}get name(){return this.property.name}get value(){return this.property.value}updateFilter(){const e=this.parentPaneInternal.filterRegex(),t=null!==e&&(e.test(this.property.name)||e.test(this.property.value));this.listItemElement.classList.toggle("filter-match",t),this.onpopulate();let n=!1;for(let e=0;e<this.childCount();++e){const t=this.childAt(e);!t||t&&!t.updateFilter()||(n=!0)}return e?n&&!this.expanded?(this.expand(),this.expandedDueToFilter=!0):!n&&this.expanded&&this.expandedDueToFilter&&(this.collapse(),this.expandedDueToFilter=!1):(this.expandedDueToFilter&&this.collapse(),this.expandedDueToFilter=!1),t}renderedPropertyText(){return this.nameElement&&this.valueElement?this.nameElement.textContent+": "+this.valueElement.textContent:""}updateState(){if(!this.listItemElement)return;this.style.isPropertyImplicit(this.name)?this.listItemElement.classList.add("implicit"):this.listItemElement.classList.remove("implicit");!this.property.parsedOk&&Qn.ignoreErrorsForProperty(this.property)?this.listItemElement.classList.add("has-ignorable-error"):this.listItemElement.classList.remove("has-ignorable-error"),this.inherited()?this.listItemElement.classList.add("inherited"):this.listItemElement.classList.remove("inherited"),this.overloaded()?this.listItemElement.classList.add("overloaded"):this.listItemElement.classList.remove("overloaded"),this.property.disabled?this.listItemElement.classList.add("disabled"):this.listItemElement.classList.remove("disabled"),this.listItemElement.classList.toggle("changed",this.isPropertyChanged(this.property))}node(){return this.parentPaneInternal.node()}parentPane(){return this.parentPaneInternal}section(){return this.#v}updatePane(){this.#v.refreshUpdate(this)}async toggleDisabled(e){if(!this.style.range)return;this.parentPaneInternal.setUserOperation(!0);const t=await this.property.setDisabled(e);this.parentPaneInternal.setUserOperation(!1),t&&(this.matchedStylesInternal.resetActiveProperties(),this.updatePane(),this.styleTextAppliedForTest())}isPropertyChanged(e){return!!t.Runtime.experiments.isEnabled("styles-pane-css-changes")&&(this.#w!==e.propertyText||this.parentPane().isPropertyChanged(e))}async onpopulate(){if(this.childCount()||!this.isShorthand)return;const e=this.property.getLonghandProperties(),t=this.style.leadingProperties();for(const n of e){const e=n.name;let i=!1,s=!1;i=this.#v.isPropertyInherited(e),s="Overloaded"===this.matchedStylesInternal.propertyState(n);const o=t.find((t=>t.name===e&&t.activeInStyle()));o&&(s=!0);const r=new dn({stylesPane:this.parentPaneInternal,section:this.#v,matchedStyles:this.matchedStylesInternal,property:n,isShorthand:!1,inherited:i,overloaded:s,newProperty:!1});r.setComputedStyles(this.computedStyles),r.setParentsComputedStyles(this.parentsComputedStyles),this.appendChild(r)}}onattach(){this.updateTitle(),this.listItemElement.addEventListener("mousedown",(e=>{0===e.button&&$t.set(this.parentPaneInternal,this)}),!1),this.listItemElement.addEventListener("mouseup",this.mouseUp.bind(this)),this.listItemElement.addEventListener("click",(e=>{if(!e.target)return;e.target.hasSelection()||e.target===this.listItemElement||e.consume(!0)})),this.listItemElement.addEventListener("contextmenu",this.handleCopyContextMenuEvent.bind(this))}onexpand(){this.updateExpandElement()}oncollapse(){this.updateExpandElement()}updateExpandElement(){this.expandElement&&(this.expanded?this.expandElement.name="triangle-down":this.expandElement.name="triangle-right")}#x(e){const t=this.matchedStyles().getRegisteredProperty(e);return t?{registration:t,goToDefinition:()=>this.parentPaneInternal.jumpToSection(e,Yn)}:void 0}getVariablePopoverContents(e,t){return new p.CSSVariableValueView.CSSVariableValueView({variableName:e,value:t??void 0,details:this.#x(e)})}#T(e,t){const i=n.CSSPropertyParser.tokenizeDeclaration("--unused",t);if(!i)return null;const s=n.CSSPropertyParser.BottomUpTreeMatching.walk(i,[new n.CSSPropertyParser.VariableMatcher((t=>{const n=this.matchedStylesInternal.computeCSSVariable(e,t.name)?.value;return void 0!==n?n:0===t.fallback.length||t.matching.hasUnresolvedVarsRange(t.fallback[0],t.fallback[t.fallback.length-1])?null:t.matching.getComputedTextRange(t.fallback[0],t.fallback[t.fallback.length-1])}))]),o=n.CSSPropertyParser.ASTUtils.siblings(n.CSSPropertyParser.ASTUtils.declValue(s.ast.tree));return s.getComputedTextRange(o[0],o[o.length-1])}updateTitleIfComputedValueChanged(){const e=this.#T(this.property.ownerStyle,this.property.value);e!==this.lastComputedValue&&(this.lastComputedValue=e,this.innerUpdateTitle())}updateTitle(){this.lastComputedValue=this.#T(this.property.ownerStyle,this.property.value),this.innerUpdateTitle()}innerUpdateTitle(){this.updateState(),this.isExpandable()&&(this.expandElement=w.Icon.create("triangle-right","expand-icon"),this.expandElement.setAttribute("jslog",`${s.expand().track({click:!0})}`));const r=this.property.parsedOk?[new Kt(this,this.style),new Yt(this),new Xt(this.parentPaneInternal),new mi(this.style.parentRule,this.node()),new Qt(this),new Jt(this),new Zt(this),new gi,new tn(this),new nn(this),new qt(this),new sn,new Gt,new an(this),new ln(this)]:[];if(!t.Runtime.experiments.isEnabled("css-type-component-length-deprecate")&&this.property.parsedOk&&r.push(new on(this)),this.listItemElement.removeChildren(),this.valueElement=ui.renderValueElement(this.name,this.value,r),this.nameElement=ui.renderNameElement(this.name),this.property.name.startsWith("--")&&this.nameElement&&this.parentPaneInternal.addPopover(this.nameElement,{contents:()=>this.getVariablePopoverContents(this.property.name,this.matchedStylesInternal.computeCSSVariable(this.style,this.property.name)?.value??null),jslogContext:"elements.css-var"}),!this.treeOutline)return;const a=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get();if(i.UIUtils.createTextChild(this.listItemElement.createChild("span","styles-clipboard-only"),a.repeat(this.section().nestingLevel+1)+(this.property.disabled?"/* ":"")),this.nameElement&&this.listItemElement.appendChild(this.nameElement),this.valueElement){const e=this.valueElement.firstElementChild&&"BR"===this.valueElement.firstElementChild.tagName?":":": ";this.listItemElement.createChild("span","styles-name-value-separator").textContent=e,this.expandElement&&this.listItemElement.appendChild(this.expandElement),this.listItemElement.appendChild(this.valueElement);const t=this.listItemElement.createChild("span","styles-semicolon");t.textContent=";",t.onmouseup=this.mouseUp.bind(this),this.property.disabled&&i.UIUtils.createTextChild(this.listItemElement.createChild("span","styles-clipboard-only")," */")}if(this.valueElement&&this.#v.editable&&"display"===this.property.name){const e=this.property.trimmedValueWithoutImportant(),t="flex"===e||"inline-flex"===e;if(t||("grid"===e||"inline-grid"===e)){const e=`${this.#v.getSectionIdx()}_${this.#v.nextEditorTriggerButtonIdx}`,n=pn.createTriggerButton(this.parentPaneInternal,this.#v,t?_t:Vt,zt(t?Wt.flexboxEditorButton:Wt.gridEditorButton),e);n.setAttribute("jslog",`${s.showStyleEditor().track({click:!0}).context(t?"flex":"grid")}`),this.#v.nextEditorTriggerButtonIdx++,n.addEventListener("click",(()=>{o.userMetrics.swatchActivated(t?6:5)})),this.listItemElement.appendChild(n);const i=this.parentPaneInternal.swatchPopoverHelper();i.isShowing(pn.instance())&&pn.instance().getTriggerKey()===e&&i.setAnchorElement(n)}}if(this.property.parsedOk)this.updateAuthoringHint();else{this.listItemElement.classList.add("not-parsed-ok");const e=this.#x(this.property.name),t=e?new p.CSSVariableValueView.CSSVariableParserError(e):null;this.listItemElement.insertBefore(this.parentPaneInternal.createExclamationMark(this.property,t),this.listItemElement.firstChild);n.CSSMetadata.cssMetadata().isCSSPropertyName(this.property.name)&&this.listItemElement.classList.add("invalid-property-value")}if(this.property.activeInStyle()||this.listItemElement.classList.add("inactive"),this.updateFilter(),this.property.parsedOk&&this.parent&&this.parent.root){const e=document.createElement("input");e.classList.add("enabled-button","small"),e.type="checkbox",e.checked=!this.property.disabled,e.setAttribute("jslog",`${s.toggle().track({click:!0})}`),e.addEventListener("mousedown",(e=>e.consume()),!1),e.addEventListener("click",(e=>{this.toggleDisabled(!this.property.disabled),e.consume()}),!1),this.nameElement&&this.valueElement&&i.ARIAUtils.setLabel(e,`${this.nameElement.textContent} ${this.valueElement.textContent}`);const t=w.Icon.create("copy","copy");i.Tooltip.Tooltip.install(t,zt(Wt.copyDeclaration)),t.addEventListener("click",(()=>{const e=`${this.property.name}: ${this.property.value};`;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(1)})),this.listItemElement.append(t),this.listItemElement.insertBefore(e,this.listItemElement.firstChild)}}updateAuthoringHint(){this.listItemElement.classList.remove("inactive-property");const e=this.listItemElement.querySelector(".hint");e&&(Ht.delete(e),e?.closest(".hint-wrapper")?.remove());const t=this.property.name;if(!Rt.has(t))return;if(this.node()?.isSVGNode())return;const n=this.parentPaneInternal.cssModel(),i=n?.fontFaces()||[],s=this.node()?.localName();for(const e of Rt.get(t)||[]){const n=e.getHint(t,this.computedStyles||void 0,this.parentsComputedStyles||void 0,s?.toLowerCase(),i);if(n){o.userMetrics.cssHintShown(e.getMetricType());const t=document.createElement("span");t.classList.add("hint-wrapper");const i=new w.Icon.Icon;i.data={iconName:"info",color:"var(--icon-default)",width:"14px",height:"14px"},i.classList.add("hint"),t.append(i),Ht.set(i,n),this.listItemElement.append(t),this.listItemElement.classList.add("inactive-property");break}}}mouseUp(e){const t=$t.get(this.parentPaneInternal);if($t.delete(this.parentPaneInternal),!t)return;if(this.listItemElement.hasSelection())return;if(i.UIUtils.isBeingEdited(e.target))return;if(e.composedPath()[0]instanceof HTMLButtonElement)return;if(e.consume(!0),e.target===this.listItemElement)return;let n=e.target;i.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&this.#v.navigable?this.navigateToSource(n):this.expandElement&&n===this.expandElement||this.#v.editable&&(n=n.enclosingNodeOrSelfWithClass("webkit-css-property")||n.enclosingNodeOrSelfWithClass("value")||n.enclosingNodeOrSelfWithClass("styles-semicolon"),n&&n!==this.nameElement?(s.logClick(this.valueElement,e),this.startEditingValue()):(s.logClick(this.nameElement,e),this.startEditingName()))}handleContextMenuEvent(e,t){const n=new i.ContextMenu.ContextMenu(t);if(this.property.parsedOk&&this.parent&&this.parent.root){const i=this.parentPaneInternal.focusedSectionIndex();n.defaultSection().appendCheckboxItem(zt(Wt.togglePropertyAndContinueEditing),(async()=>{if(this.treeOutline){const n=this.treeOutline.rootElement().indexOfChild(this);this.editingCancelled(null,e),await this.toggleDisabled(!this.property.disabled),t.consume(),this.parentPaneInternal.continueEditingElement(i,n)}}),{checked:!this.property.disabled,jslogContext:"toggle-property-and-continue-editing"})}const s=this.navigateToSource.bind(this);n.defaultSection().appendItem(zt(Wt.revealInSourcesPanel),s,{jslogContext:"reveal-in-sources-panel"}),n.show()}handleCopyContextMenuEvent(e){if(!e.target)return;this.createCopyContextMenu(e).show()}createCopyContextMenu(e){const t=new i.ContextMenu.ContextMenu(e);return t.headerSection().appendItem(zt(Wt.copyDeclaration),(()=>{const e=`${this.property.name}: ${this.property.value};`;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(3)}),{jslogContext:"copy-declaration"}),t.headerSection().appendItem(zt(Wt.copyProperty),(()=>{o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.property.name),o.userMetrics.styleTextCopied(4)}),{jslogContext:"copy-property"}),t.headerSection().appendItem(zt(Wt.copyValue),(()=>{o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.property.value),o.userMetrics.styleTextCopied(5)}),{jslogContext:"copy-value"}),t.headerSection().appendItem(zt(Wt.copyRule),(()=>{const e=Qn.formatLeadingProperties(this.#v).ruleText;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(7)}),{jslogContext:"copy-rule"}),t.headerSection().appendItem(zt(Wt.copyCssDeclarationAsJs),this.copyCssDeclarationAsJs.bind(this),{jslogContext:"copy-css-declaration-as-js"}),t.clipboardSection().appendItem(zt(Wt.copyAllDeclarations),(()=>{const e=Qn.formatLeadingProperties(this.#v).allDeclarationText;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(8)}),{jslogContext:"copy-all-declarations"}),t.clipboardSection().appendItem(zt(Wt.copyAllCssDeclarationsAsJs),this.copyAllCssDeclarationAsJs.bind(this),{jslogContext:"copy-all-css-declarations-as-js"}),t.defaultSection().appendItem(zt(Wt.copyAllCSSChanges),(async()=>{const e=await this.parentPane().getFormattedChanges();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(2)}),{jslogContext:"copy-all-css-changes"}),t.footerSection().appendItem(zt(Wt.viewComputedValue),(()=>{this.viewComputedValue()}),{jslogContext:"view-computed-value"}),t}async viewComputedValue(){const e=Ls.instance().getComputedStyleWidget();e.isShowing()||await i.ViewManager.ViewManager.instance().showView("Computed");let t="";t=this.isShorthand?"^"+this.property.name+"-":"^"+this.property.name+"$";const n=new RegExp(t,"i");await e.filterComputedStyles(n),e.input.setValue(this.property.name),e.input.element.focus()}copyCssDeclarationAsJs(){const e=Ft(this.property);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(6)}copyAllCssDeclarationAsJs(){const e=this.#v.style().leadingProperties().filter((e=>!e.disabled)).map(Ft);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.join(",\n")),o.userMetrics.styleTextCopied(9)}navigateToSource(t,n){if(!this.#v.navigable)return;const i=t===this.nameElement,s=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.property,i);s&&e.Revealer.reveal(s,n)}startEditingValue(){const e={expanded:this.expanded,hasChildren:this.isExpandable(),isEditingName:!1,originalProperty:this.property,previousContent:this.value};if(n.CSSMetadata.cssMetadata().isGridAreaDefiningProperty(this.name)){const t=S.TextUtils.Utils.splitStringByRegexes(this.value,[n.CSSMetadata.GridAreaRowRegex]);e.previousContent=t.map((e=>e.value.trim())).join("\n")}this.#M(e)}startEditingName(){const e={expanded:this.expanded,hasChildren:this.isExpandable(),isEditingName:!0,originalProperty:this.property,previousContent:this.name.split("\n").map((e=>e.trim())).join("\n")};this.#M(e)}#M(e){if(this.contextForTest=e,this.parent instanceof dn&&this.parent.isShorthand)return;const t=e.isEditingName?this.nameElement:this.valueElement;if(!t)return;if(i.UIUtils.isBeingEdited(t))return;this.setExpandable(!1),t.parentElement?.classList.add("child-editing"),t.textContent=e.previousContent,this.originalPropertyText=this.property.propertyText||"",this.parentPaneInternal.setEditingStyle(!0,this),t.parentElement?.scrollIntoViewIfNeeded(!1),this.prompt=new ni(this,e.isEditingName),this.prompt.setAutocompletionTimeout(0),this.prompt.addEventListener("TextChanged",(()=>{this.applyFreeFlowStyleTextEdit(e)}));const n=this.property.getInvalidStringForInvalidProperty();n&&i.ARIAUtils.alert(n);const s=this.prompt.attachAndStartEditing(t,function(e,t){let n=t.target.textContent;e.isEditingName||(n=this.value||n),this.editingCommitted(n||"",e,"")}.bind(this,e));this.navigateToSource(t,!0),s.addEventListener("keydown",this.editingNameValueKeyDown.bind(this,e),!1),s.addEventListener("keypress",this.editingNameValueKeyPress.bind(this,e),!1),e.isEditingName&&(s.addEventListener("paste",function(e,t){const n=t.clipboardData;if(!n)return;const i=n.getData("Text");if(!i)return;const s=i.indexOf(":");if(s<0)return;const o=i.substring(0,s).trim(),r=i.substring(s+1).trim();t.preventDefault(),void 0===e.originalName&&(this.nameElement&&(e.originalName=this.nameElement.textContent||""),this.valueElement&&(e.originalValue=this.valueElement.textContent||"")),this.property.name=o,this.property.value=r,this.nameElement&&(this.nameElement.textContent=o,this.nameElement.normalize()),this.valueElement&&(this.valueElement.textContent=r,this.valueElement.normalize());const a=t.target;this.editingCommitted(a.textContent||"",e,"forward")}.bind(this,e),!1),s.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this,e),!1)),t.getComponentSelection()?.selectAllChildren(t)}editingNameValueKeyDown(e,t){if(t.handled)return;const n=t,s=n.target;let o;if("Enter"===n.key&&!n.shiftKey||e.isEditingName&&" "===n.key)o="forward";else if(n.keyCode===i.KeyboardShortcut.Keys.Esc.code||n.key===a.KeyboardUtilities.ESCAPE_KEY)o="cancel";else if(!e.isEditingName&&this.newProperty&&n.keyCode===i.KeyboardShortcut.Keys.Backspace.code){const e=s.getComponentSelection();e&&e.isCollapsed&&!e.focusOffset&&(t.preventDefault(),o="backward")}else"Tab"===n.key&&(o=n.shiftKey?"backward":"forward",t.preventDefault());if(o){switch(o){case"cancel":this.editingCancelled(null,e);break;case"forward":case"backward":this.editingCommitted(s.textContent||"",e,o)}t.consume()}else;}editingNameValueKeyPress(e,t){const n=t,i=n.target,s=String.fromCharCode(n.charCode),o=this.#N(i);if(e.isEditingName?":"===s:";"===s&&null!==o&&function(e,t){let n="";for(let i=0;i<t;++i){const t=e[i];"\\"===t&&""!==n?++i:n||'"'!==t&&"'"!==t?n===t&&(n=""):n=t}return!n}(i.textContent||"",o))return t.consume(!0),void this.editingCommitted(i.textContent||"",e,"forward")}#N(e){const t=e.getComponentSelection();if(!t?.containsNode(e,!0))return null;let n=t.anchorOffset,i=t.anchorNode;for(;i!==e;){for(;i?.previousSibling;)i=i.previousSibling,n+=i.textContent?.length??0;i=i?.parentNodeOrShadowHost()??null}return n}async applyFreeFlowStyleTextEdit(e){if(!this.prompt||!this.parentPaneInternal.node())return;const t=this.prompt.text();if(e.isEditingName&&t.includes(":"))return void this.editingCommitted(t,e,"forward");const n=this.prompt.textWithCurrentSuggestion();if(n.includes(";"))return;const i=this.parentPaneInternal.node();if(i){if(Boolean(i.pseudoType())){if("content"===this.name.toLowerCase())return;const e=n.trim().toLowerCase();if(e.startsWith("content:")||"display: none"===e)return}}e.isEditingName?n.includes(":")?await this.applyStyleText(n,!1):this.hasBeenEditedIncrementally&&await this.applyOriginalStyle(e):this.nameElement&&await this.applyStyleText(`${this.nameElement.textContent}: ${n}`,!1)}kickFreeFlowStyleEditForTest(){const e=this.contextForTest;return this.applyFreeFlowStyleTextEdit(e)}editingEnded(e){this.setExpandable(e.hasChildren),e.expanded&&this.expand();const t=e.isEditingName?this.nameElement:this.valueElement;t&&t.parentElement&&t.parentElement.classList.remove("child-editing"),this.parentPaneInternal.setEditingStyle(!1)}editingCancelled(e,t){this.removePrompt(),this.hasBeenEditedIncrementally?this.applyOriginalStyle(t):this.newProperty&&this.treeOutline&&this.treeOutline.removeChild(this),this.updateTitle(),this.editingEnded(t)}async applyOriginalStyle(e){await this.applyStyleText(this.originalPropertyText,!1,e.originalProperty)}findSibling(e){let t=this;do{const n="forward"===e?t.nextSibling:t.previousSibling;t=n instanceof dn?n:null}while(t&&t.inherited());return t}async editingCommitted(e,t,n){this.removePrompt(),this.editingEnded(t);const i=t.isEditingName;if(!this.nameElement||!this.valueElement)return;const s=this.nameElement.textContent||"",o=i&&s.includes(":")||!this.property;let r=!1,l=!1;const d=void 0!==t.originalName,c=d&&(this.nameElement.textContent!==t.originalName||this.valueElement.textContent!==t.originalValue),h=d&&i&&this.valueElement.textContent!==t.originalValue;let p=this;const u=i!==("forward"===n),m=this.newProperty&&!e&&(u||i);("forward"===n&&(!i||h)||"backward"===n&&i)&&(p=p.findSibling(n),p||("forward"!==n||this.newProperty&&!e?"backward"===n&&(l=!0):r=!0));let g=-1;null!==p&&this.treeOutline&&(g=this.treeOutline.rootElement().indexOfChild(p));const y=a.StringUtilities.isWhitespace(e),f=this.newProperty&&(h||u||!n&&!i||i&&y||o);if((e!==t.previousContent||c)&&!this.newProperty||f){let t;t=o?this.nameElement.textContent:y||this.newProperty&&a.StringUtilities.isWhitespace(this.valueElement.textContent||"")?"":i?e+": "+this.property.value:this.property.name+": "+e,await this.applyStyleText(t||"",!0),S.call(this,this.newProperty,!y,this.#v)}else i?this.property.name=e:this.property.value=e,d||this.newProperty||this.updateTitle(),S.call(this,this.newProperty,!1,this.#v);function S(e,t,s){if(n)if(p&&p.parent)i?p.startEditingValue():p.startEditingName();else{if(p&&!p.parent){const t=s.propertiesTreeOutline.rootElement();if("forward"===n&&y&&!i&&--g,g>=t.childCount()&&!this.newProperty)r=!0;else{const s=g>=0?t.childAt(g):null;if(s)return void(e&&y?"forward"===n?s.startEditingName():s.startEditingValue():!i||h?s.startEditingName():s.startEditingValue());e||(l=!0)}}if(r){if(e&&!t&&i!==("backward"===n))return;s.addNewBlankProperty().startEditingName()}else if(m){p=this.findSibling(n);const e=p||"backward"===n?s:s.nextEditableSibling();e&&(e.style().parentRule?e.startEditingSelector():e.moveEditorFromSelector(n))}else l&&(s.style().parentRule?s.startEditingSelector():s.moveEditorFromSelector(n))}else this.parentPaneInternal.resetFocus()}}removePrompt(){this.prompt&&(this.prompt.detach(),this.prompt=null)}styleTextAppliedForTest(){}applyStyleText(e,t,n){return this.applyStyleThrottler.schedule(this.innerApplyStyleText.bind(this,e,t,n))}async innerApplyStyleText(e,t,n){if(!this.treeOutline||!this.property)return;if(!this.style.range)return;const i=this.hasBeenEditedIncrementally;if(!(e=e.replace(/[\xA0\t]/g," ").trim()).length&&t&&this.newProperty&&!i)return void(this.parent&&this.parent.removeChild(this));const s=this.parentPaneInternal.node();this.parentPaneInternal.setUserOperation(!0),e+=a.StringUtilities.findUnclosedCssQuote(e),(e+=")".repeat(a.StringUtilities.countUnmatchedLeftParentheses(e))).length&&!/;\s*$/.test(e)&&(e+=";");const o=!this.newProperty||i;let r=await this.property.setText(e,t,o);i&&t&&!r&&(t=!1,r=await this.property.setText(this.originalPropertyText,t,o)),this.parentPaneInternal.setUserOperation(!1);const l=n||this.style.propertyAt(this.property.index),d=this.property.index<this.style.allProperties().length;if(!r||!l&&d)return t&&(this.newProperty?this.treeOutline.removeChild(this):this.updateTitle()),void this.styleTextAppliedForTest();l&&(this.listItemElement.classList.toggle("changed",this.isPropertyChanged(l)),this.parentPane().updateChangeStatus()),this.matchedStylesInternal.resetActiveProperties(),this.hasBeenEditedIncrementally=!0;const c=t&&!e.length;c?this.#v.resetToolbars():!c&&l&&(this.property=l),s===this.node()&&this.updatePane(),this.styleTextAppliedForTest()}ondblclick(){return!0}isEventWithinDisclosureTriangle(e){return e.target===this.expandElement}}var cn=Object.freeze({__proto__:null,activeHints:Ht,VariableRenderer:Kt,LinearGradientRenderer:Gt,ColorRenderer:Yt,LightDarkColorRenderer:qt,ColorMixRenderer:Xt,AngleRenderer:Qt,LinkableNameRenderer:Jt,BezierRenderer:Zt,ShadowModel:en,ShadowRenderer:tn,FontRenderer:nn,GridTemplateRenderer:sn,LengthRenderer:on,AnchorFunctionRenderer:an,PositionAnchorRenderer:ln,StylePropertyTreeElement:dn});let hn=null;class pn extends i.Widget.VBox{editor;pane;section;editorContainer;#I;constructor(){super(!0),this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),this.editorContainer=document.createElement("div"),this.contentElement.appendChild(this.editorContainer),this.onPropertySelected=this.onPropertySelected.bind(this),this.onPropertyDeselected=this.onPropertyDeselected.bind(this)}getSection(){return this.section}async onPropertySelected(e){if(!this.section)return;const t=un(this.section,e.data.name);t.property.value=e.data.value,t.updateTitle(),await t.applyStyleText(t.renderedPropertyText(),!1),await this.render()}async onPropertyDeselected(e){if(!this.section)return;const t=un(this.section,e.data.name);await t.applyStyleText("",!1),await this.render()}bindContext(e,t){this.pane=e,this.section=t,this.editor?.addEventListener("propertyselected",this.onPropertySelected),this.editor?.addEventListener("propertydeselected",this.onPropertyDeselected)}setTriggerKey(e){this.#I=e}getTriggerKey(){return this.#I}unbindContext(){this.pane=void 0,this.section=void 0,this.editor?.removeEventListener("propertyselected",this.onPropertySelected),this.editor?.removeEventListener("propertydeselected",this.onPropertyDeselected)}async render(){this.editor&&(this.editor.data={authoredProperties:this.section?gn(this.section,this.editor.getEditableProperties()):new Map,computedProperties:this.pane?await mn(this.pane):new Map})}static instance(){return hn||(hn=new pn),hn}setEditor(e){this.editor instanceof e||(this.contentElement.removeChildren(),this.editor=new e,this.contentElement.appendChild(this.editor))}static createTriggerButton(e,t,n,i,s){const o=function(e){const t=document.createElement("button");t.classList.add("styles-pane-button"),t.tabIndex=0,t.title=e,t.onmouseup=e=>{e.stopPropagation()};const n=new w.Icon.Icon;return n.data={iconName:"flex-wrap",color:"var(--sys-color-token-subtle)",width:"16px",height:"16px"},t.appendChild(n),t}(i);return o.onclick=async i=>{i.stopPropagation();const r=e.swatchPopoverHelper(),a=pn.instance();a.setEditor(n),a.bindContext(e,t),a.setTriggerKey(s),await a.render();const l=o.enclosingNodeOrSelfWithClass("style-panes-wrapper"),d=()=>{r.hide(!0)};r.show(a,o,(()=>{a.unbindContext(),l&&l.removeEventListener("scroll",d)})),l&&l.addEventListener("scroll",d)},o}}function un(e,t){const n=e.propertiesTreeOutline.rootElement().children().find((e=>e instanceof dn&&e.property.name===t));if(n)return n;const i=e.addNewBlankProperty();return i.property.name=t,i}async function mn(e){const t=e.computedStyleModel(),n=await t.fetchComputedStyle();return n?n.computedStyle:new Map}function gn(e,t){const n=new Map,i=new Set(t.map((e=>e.propertyName)));for(const t of e.style().leadingProperties())i.has(t.name)&&n.set(t.name,t.value);return n}var yn=Object.freeze({__proto__:null,StyleEditorWidget:pn});const fn=new CSSStyleSheet;fn.replaceSync(":host{display:inline}.node-link{cursor:pointer;display:inline;pointer-events:auto;outline-offset:2px;&:focus-visible{outline-width:unset}&.dynamic-link:hover{text-decoration:underline}}.node-label-name{color:var(--sys-color-token-property-special);.dynamic-link &{color:var(--text-link)}}.node-label-class,\n.node-label-pseudo{color:var(--sys-color-token-attribute)}\n/*# sourceURL=domLinkifier.css */\n");const Sn={node:"<node>"},Cn=r.i18n.registerUIStrings("panels/elements/DOMLinkifier.ts",Sn),En=r.i18n.getLocalizedString.bind(void 0,Cn),bn=function(e,t,n){const s=e,o=e.nodeType()===Node.ELEMENT_NODE&&e.pseudoType();if(o&&e.parentNode&&(e=e.parentNode),e.isViewTransitionPseudoNode()){const e=t.createChild("span","extra node-label-pseudo"),o=`::${s.pseudoType()}(${s.pseudoIdentifier()})`;return i.UIUtils.createTextChild(e,o),void i.Tooltip.Tooltip.install(t,n.tooltip||o)}const r=t.createChild("span","node-label-name");if(n.textContent)return r.textContent=n.textContent,void i.Tooltip.Tooltip.install(t,n.tooltip||n.textContent);let a=e.nodeNameInCorrectCase();r.textContent=a;const l=e.getAttribute("id");if(l){const e=t.createChild("span","node-label-id"),n="#"+l;a+=n,i.UIUtils.createTextChild(e,n),r.classList.add("extra")}const d=e.getAttribute("class");if(d){const e=d.split(/\s+/);if(e.length){const n=new Set,s=t.createChild("span","extra node-label-class");for(let t=0;t<e.length;++t){const o=e[t];if(o&&!n.has(o)){const e="."+o;a+=e,i.UIUtils.createTextChild(s,e),n.add(o)}}}}if(o){const e=s.pseudoIdentifier(),n=t.createChild("span","extra node-label-pseudo");let o="::"+s.pseudoType();e&&(o+=`(${e})`),i.UIUtils.createTextChild(n,o),a+=o}i.Tooltip.Tooltip.install(t,n.tooltip||a)},vn=function(t,o={tooltip:void 0,preventKeyboardFocus:void 0,textContent:void 0,isDynamicLink:!1}){if(!t)return document.createTextNode(En(Sn.node));const r=document.createElement("span");r.classList.add("monospace");const a=i.UIUtils.createShadowRootWithCoreStyles(r,{cssFile:[fn],delegatesFocus:void 0}).createChild("button","node-link text-button link-style");return a.classList.toggle("dynamic-link",o.isDynamicLink),a.setAttribute("jslog",`${s.link("node").track({click:!0,keydown:"Enter"})}`),bn(t,a,o),a.addEventListener("click",(()=>(e.Revealer.reveal(t,!1),!1)),!1),a.addEventListener("mouseover",t.highlight.bind(t,void 0),!1),a.addEventListener("mouseleave",(()=>n.OverlayModel.OverlayModel.hideDOMNodeHighlight()),!1),o.preventKeyboardFocus&&(a.tabIndex=-1),r},wn=function(t,n={tooltip:void 0,preventKeyboardFocus:void 0}){const o=document.createElement("div"),r=i.UIUtils.createShadowRootWithCoreStyles(o,{cssFile:[fn],delegatesFocus:void 0}).createChild("button","node-link text-button link-style");return r.setAttribute("jslog",`${s.link("node").track({click:!0})}`),r.createChild("slot"),r.addEventListener("click",t.resolve.bind(t,(function(t){e.Revealer.reveal(t)})),!1),r.addEventListener("mousedown",(e=>e.consume()),!1),n.preventKeyboardFocus&&(r.tabIndex=-1),o};let xn;class Tn{static instance(e={forceNew:null}){const{forceNew:t}=e;return xn&&!t||(xn=new Tn),xn}linkify(e,t){if(e instanceof n.DOMModel.DOMNode)return vn(e,t);if(e instanceof n.DOMModel.DeferredDOMNode)return wn(e,t);throw new Error("Can't linkify non-node")}}var Mn=Object.freeze({__proto__:null,decorateNodeLabel:bn,linkifyNodeReference:vn,linkifyDeferredNodeReference:wn,Linkifier:Tn});const Nn=new CSSStyleSheet;Nn.replaceSync('.has-ignorable-error .webkit-css-property{color:inherit}.tree-outline{padding:0}.tree-outline li{margin-left:12px;padding-left:22px;white-space:normal;text-overflow:ellipsis;cursor:auto;display:block;&::before{display:none}.webkit-css-property{margin-left:-22px}&.not-parsed-ok{margin-left:0;.exclamation-mark{display:inline-block;position:relative;width:11px;height:10px;margin:0 7px 0 0;top:1px;left:-36px;user-select:none;cursor:default;z-index:1;mask:var(--image-file-warning-filled) center /14px no-repeat;background-color:var(--icon-warning)}&.has-ignorable-error .exclamation-mark{background-color:unset}}&.filter-match{background-color:var(--sys-color-tonal-container)}&.editing{margin-left:10px;text-overflow:clip}&.editing-sub-part{padding:3px 6px 8px 18px;margin:-1px -6px -8px;text-overflow:clip}&.child-editing{word-wrap:break-word!important;white-space:normal!important;padding-left:0}.info{padding-top:4px;padding-bottom:3px}}.tree-outline > li{padding-left:38px;clear:both;min-height:14px;.webkit-css-property{margin-left:-38px}&.child-editing{.text-prompt{white-space:pre-wrap}.webkit-css-property{margin-left:0}}}ol:not(.tree-outline){display:none;margin:0;padding-inline-start:12px;list-style:none}ol.expanded{display:block}.enabled-button{visibility:hidden;float:left;font-size:10px;margin:0;vertical-align:top;position:relative;z-index:1;width:18px;left:-40px;top:0.5px;height:13px}input.enabled-button.small{&:hover::after,\n  &:active::before{left:3px}}.overloaded:not(.has-ignorable-error, .invalid-property-value),\n.inactive:not(.invalid-property-value),\n.disabled,\n.not-parsed-ok:not(.has-ignorable-error, .invalid-property-value),\n.not-parsed-ok.invalid-property-value .value{text-decoration:line-through}.implicit,\n.inherited,\n.inactive-property{opacity:50%}.changed{background-color:var(--sys-color-tertiary-container);&::after{content:"";position:absolute;left:-4px;top:0;width:2px;height:100%;background-color:var(--sys-color-tertiary)}}.copy{display:none;.changed:hover &{position:absolute;right:-4px;top:0;bottom:0;margin:auto;display:inline-block;cursor:pointer;transform:scale(0.9)}}.hint-wrapper{align-items:center;display:inline-block;margin-left:3px;max-height:13px;max-width:13px;vertical-align:middle}.hint{cursor:pointer;display:block;position:relative;left:-1.5px;top:-1.5px}.has-ignorable-error{color:var(--sys-color-state-disabled)}:host-context(.no-affect) .tree-outline li{opacity:50%;&.editing{opacity:100%}}:host-context(.styles-panel-hovered:not(.read-only)) .webkit-css-property:hover,\n:host-context(.styles-panel-hovered:not(.read-only)) .value:hover{text-decoration:underline;cursor:default}.styles-name-value-separator{display:inline-block;width:14px;text-decoration:inherit;white-space:pre}.styles-clipboard-only{display:inline-block;width:0;opacity:0%;pointer-events:none;white-space:pre;.tree-outline li.child-editing &{display:none}}.styles-pane-button{width:15px;height:15px;padding:0;border:0;margin:0 0 0 6px;position:absolute;top:-1px;background-color:var(--sys-color-cdt-base-container);border-radius:3px;display:inline-flex;align-items:center;justify-content:center;cursor:pointer;&:hover devtools-icon{color:var(--icon-default-hover)}}:host-context(.matched-styles) .tree-outline li{margin-left:0!important}.expand-icon{user-select:none;margin-left:-6px;margin-right:2px;margin-bottom:-4px;.tree-outline li:not(.parent) &{display:none}}:host-context(.matched-styles:not(.read-only):hover) li:not(.child-editing) .enabled-button,\n:host-context(.matched-styles:not(.read-only)) .tree-outline li.disabled:not(.child-editing) .enabled-button{visibility:visible}:host-context(.matched-styles) ol.expanded{margin-left:16px}.devtools-link-styled-trim{display:inline-block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:80%;vertical-align:bottom}devtools-css-angle,\ndevtools-css-length{display:inline-block}\n/*# sourceURL=stylePropertiesTreeOutline.css */\n');const In={insertStyleRuleBelow:"Insert Style Rule Below",constructedStylesheet:"constructed stylesheet",userAgentStylesheet:"user agent stylesheet",injectedStylesheet:"injected stylesheet",viaInspector:"via inspector",styleAttribute:"`style` attribute",sattributesStyle:"{PH1}[Attributes Style]",showAllPropertiesSMore:"Show all properties ({PH1} more)",copySelector:"Copy `selector`",copyRule:"Copy rule",copyAllDeclarations:"Copy all declarations",copyAllCSSChanges:"Copy all CSS changes",cssSelector:"`CSS` selector"},Pn=r.i18n.registerUIStrings("panels/elements/StylePropertiesSection.ts",In),On=r.i18n.getLocalizedString.bind(void 0,Pn),Ln="<style>";class An{parentPane;styleInternal;matchedStyles;computedStyles;parentsComputedStyles;editable;hoverTimer;willCauseCancelEditing;forceShowAll;originalPropertiesCount;element;#P;titleElement;propertiesTreeOutline;showAllButton;selectorElement;newStyleRuleToolbar;fontEditorToolbar;fontEditorSectionManager;fontEditorButton;selectedSinceMouseDown;elementToSelectorIndex;navigable;selectorRefElement;hoverableSelectorsMode;isHiddenInternal;nestingLevel=0;#O;#L;nextEditorTriggerButtonIdx=1;sectionIdx=0;static#A=new WeakMap;#k;constructor(o,r,l,d,c,h,p){this.#k=p,this.parentPane=o,this.sectionIdx=d,this.styleInternal=l,this.matchedStyles=r,this.computedStyles=c,this.parentsComputedStyles=h,this.editable=Boolean(l.styleSheetId&&l.range),this.hoverTimer=null,this.willCauseCancelEditing=!1,this.forceShowAll=!1,this.originalPropertiesCount=l.leadingProperties().length;const u=l.parentRule,m=this.headerText();this.element=document.createElement("div"),this.element.classList.add("styles-section"),this.element.classList.add("matched-styles"),this.element.classList.add("monospace"),this.element.setAttribute("jslog",`${s.section("style-properties").track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter|Space"})}`),i.ARIAUtils.setLabel(this.element,`${m}, css selector`),this.element.tabIndex=-1,i.ARIAUtils.markAsListitem(this.element),this.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),o.sectionByElement.set(this.element,this),this.#P=this.element.createChild("div","style-rule"),this.#O=document.createElement("div"),this.#O.classList.add("ancestor-rule-list"),this.element.prepend(this.#O),this.#L=document.createElement("div"),this.#L.classList.add("ancestor-closing-braces"),this.element.append(this.#L),this.updateAncestorRuleList(),this.titleElement=this.#P.createChild("div","styles-section-title "+(u?"styles-selector":"")),this.propertiesTreeOutline=new i.TreeOutline.TreeOutlineInShadow,this.propertiesTreeOutline.setFocusable(!1),this.propertiesTreeOutline.registerCSSFiles([Nn]),this.propertiesTreeOutline.element.classList.add("style-properties","matched-styles","monospace"),this.#P.appendChild(this.propertiesTreeOutline.element),this.showAllButton=i.UIUtils.createTextButton("",this.showAllItems.bind(this),{className:"styles-show-all",jslogContext:"elements.show-all-style-properties"}),this.#P.appendChild(this.showAllButton);const g=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),y=document.createElement("div");if(y.createChild("span","styles-clipboard-only").textContent=g.repeat(this.nestingLevel),y.classList.add("selector-container"),this.selectorElement=document.createElement("span"),i.ARIAUtils.setLabel(this.selectorElement,On(In.cssSelector)),this.selectorElement.classList.add("selector"),this.selectorElement.textContent=m,y.appendChild(this.selectorElement),this.selectorElement.addEventListener("mouseenter",this.onMouseEnterSelector.bind(this),!1),this.selectorElement.addEventListener("mouseleave",this.onMouseOutSelector.bind(this),!1),m.length>0||!(u instanceof n.CSSRule.CSSStyleRule)){y.createChild("span","sidebar-pane-open-brace").textContent=m.length>0?" {":"{";const e=this.#P.createChild("div","sidebar-pane-closing-brace");e.createChild("span","styles-clipboard-only").textContent=g.repeat(this.nestingLevel),e.createChild("span").textContent="}"}else this.titleElement.classList.add("hidden");if(u){const e=new i.Toolbar.ToolbarButton(On(In.insertStyleRuleBelow),"plus",void 0,"elements.new-style-rule");e.addEventListener("Click",this.onNewRuleClick,this),e.element.tabIndex=-1,this.newStyleRuleToolbar||(this.newStyleRuleToolbar=new i.Toolbar.Toolbar("sidebar-pane-section-toolbar new-rule-toolbar",this.element)),this.newStyleRuleToolbar.appendToolbarItem(e),i.ARIAUtils.markAsHidden(this.newStyleRuleToolbar.element)}if(t.Runtime.experiments.isEnabled("font-editor")&&this.editable&&(this.fontEditorToolbar=new i.Toolbar.Toolbar("sidebar-pane-section-toolbar",this.#P),this.fontEditorSectionManager=new Q(this.parentPane.swatchPopoverHelper(),this),this.fontEditorButton=new i.Toolbar.ToolbarButton("Font Editor","custom-typography"),this.fontEditorButton.addEventListener("Click",(()=>{this.onFontEditorButtonClicked()}),this),this.fontEditorButton.element.addEventListener("keydown",(e=>{a.KeyboardUtilities.isEnterOrSpaceKey(e)&&(e.consume(!0),this.onFontEditorButtonClicked())}),!1),this.fontEditorToolbar.appendToolbarItem(this.fontEditorButton),this.styleInternal.type===n.CSSStyleDeclaration.Type.Inline?this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.add("shifted-toolbar"):this.fontEditorToolbar.element.classList.add("font-toolbar-hidden")),this.selectorElement.addEventListener("click",this.handleSelectorClick.bind(this),!1),this.selectorElement.setAttribute("jslog",`${s.cssRuleHeader("selector").track({click:!0,change:!0})}`),this.element.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1),this.element.addEventListener("mousedown",this.handleEmptySpaceMouseDown.bind(this),!1),this.element.addEventListener("click",this.handleEmptySpaceClick.bind(this),!1),this.element.addEventListener("mousemove",this.onMouseMove.bind(this),!1),this.element.addEventListener("mouseleave",this.onMouseLeave.bind(this),!1),this.selectedSinceMouseDown=!1,this.elementToSelectorIndex=new WeakMap,u)if(u.isUserAgent()||u.isInjected())this.editable=!1;else if(u.styleSheetId){const e=u.cssModel().styleSheetHeaderForId(u.styleSheetId);this.navigable=e&&!e.isAnonymousInlineStyleSheet()}this.selectorRefElement=document.createElement("div"),this.selectorRefElement.classList.add("styles-section-subtitle"),this.element.prepend(this.selectorRefElement),this.updateRuleOrigin(),this.titleElement.appendChild(y),this.navigable&&this.element.classList.add("navigable"),this.editable||(this.element.classList.add("read-only"),this.propertiesTreeOutline.element.classList.add("read-only")),this.hoverableSelectorsMode=!1,this.isHiddenInternal=!1,this.markSelectorMatches(),this.onpopulate()}setComputedStyles(e){this.computedStyles=e}setParentsComputedStyles(e){this.parentsComputedStyles=e}updateAuthoringHint(){let e=this.propertiesTreeOutline.firstChild();for(;e;)e instanceof dn&&(e.setComputedStyles(this.computedStyles),e.setParentsComputedStyles(this.parentsComputedStyles),e.updateAuthoringHint()),e=e.nextSibling}setSectionIdx(e){this.sectionIdx=e,this.onpopulate()}getSectionIdx(){return this.sectionIdx}registerFontProperty(e){this.fontEditorSectionManager&&this.fontEditorSectionManager.registerFontProperty(e),this.fontEditorToolbar&&(this.fontEditorToolbar.element.classList.remove("font-toolbar-hidden"),this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.add("shifted-toolbar"))}resetToolbars(){this.parentPane.swatchPopoverHelper().isShowing()||this.styleInternal.type===n.CSSStyleDeclaration.Type.Inline||(this.fontEditorToolbar&&this.fontEditorToolbar.element.classList.add("font-toolbar-hidden"),this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.remove("shifted-toolbar"))}static createRuleOriginNode(e,t,n){if(!n)return document.createTextNode("");const i=An.getRuleLocationFromCSSRule(n),s=n.styleSheetId?e.cssModel().styleSheetHeaderForId(n.styleSheetId):null;function o(){return n&&i&&n.styleSheetId&&s&&(!s.isAnonymousInlineStyleSheet()||e.cssModel().sourceMapManager().sourceMapForClient(s))?An.linkifyRuleLocation(e.cssModel(),t,n.styleSheetId,i):null}function r(e){if(s?.ownerNode){const t=wn(s.ownerNode,{preventKeyboardFocus:!1,tooltip:void 0});return t.textContent=e,t}return null}if(s?.isMutable&&!s.isViaInspector()){const e=s.isConstructedByNew()&&!s.sourceMapURL?null:o();if(e)return e;const t=s.isConstructedByNew()?On(In.constructedStylesheet):Ln,n=r(t);return n||document.createTextNode(t)}const a=o();if(a)return a;if(n.isUserAgent())return document.createTextNode(On(In.userAgentStylesheet));if(n.isInjected())return document.createTextNode(On(In.injectedStylesheet));if(n.isViaInspector())return document.createTextNode(On(In.viaInspector));const l=r(Ln);return l||document.createTextNode("")}createRuleOriginNode(e,t,n){return An.createRuleOriginNode(e,t,n)}static getRuleLocationFromCSSRule(e){let t;return e instanceof n.CSSRule.CSSStyleRule?t=e.style.range:e instanceof n.CSSRule.CSSKeyframeRule&&(t=e.key().range),t}static tryNavigateToRuleLocation(e,t){if(!t)return;const n=this.getRuleLocationFromCSSRule(t),i=t.styleSheetId?e.cssModel().styleSheetHeaderForId(t.styleSheetId):null;if(n&&t.styleSheetId&&i&&!i.isAnonymousInlineStyleSheet()){const i=this.getCSSSelectorLocation(e.cssModel(),t.styleSheetId,n);this.revealSelectorSource(i,!0)}}static linkifyRuleLocation(e,t,n,i){const s=this.getCSSSelectorLocation(e,n,i);return t.linkifyCSSLocation(s)}static getCSSSelectorLocation(e,t,i){const s=e.styleSheetHeaderForId(t),o=s.lineNumberInSource(i.startLine),r=s.columnNumberInSource(i.startLine,i.startColumn);return new n.CSSModel.CSSLocation(s,o,r)}getFocused(){return this.propertiesTreeOutline.shadowRoot.activeElement||null}focusNext(e){const t=this.getFocused();t&&(t.tabIndex=-1),e.focus(),this.propertiesTreeOutline.shadowRoot.contains(e)&&(e.tabIndex=0)}ruleNavigation(e){if(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)return;const t=this.getFocused();let n=null;const i=Array.from(this.propertiesTreeOutline.shadowRoot.querySelectorAll("[tabindex]"));if(0===i.length)return;const s=t?i.indexOf(t):-1;if("ArrowLeft"===e.key)n=i[s-1]||this.element;else if("ArrowRight"===e.key)n=i[s+1]||this.element;else if("ArrowUp"===e.key||"ArrowDown"===e.key)return void this.focusNext(this.element);n&&(this.focusNext(n),e.consume(!0))}onKeyDown(e){const t=e;if(!(i.UIUtils.isEditing()||!this.editable||t.altKey||t.ctrlKey||t.metaKey))switch(t.key){case"Enter":case" ":this.startEditingAtFirstPosition(),t.consume(!0);break;case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":this.ruleNavigation(t);break;default:1===t.key.length&&this.addNewBlankProperty(0).startEditingName()}}setSectionHovered(e){this.element.classList.toggle("styles-panel-hovered",e),this.propertiesTreeOutline.element.classList.toggle("styles-panel-hovered",e),this.hoverableSelectorsMode!==e&&(this.hoverableSelectorsMode=e,this.markSelectorMatches())}onMouseLeave(e){this.setSectionHovered(!1),this.parentPane.setActiveProperty(null)}onMouseMove(e){const t=i.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e);this.setSectionHovered(t);const n=this.propertiesTreeOutline.treeElementFromEvent(e);n instanceof dn?this.parentPane.setActiveProperty(n):this.parentPane.setActiveProperty(null);const s=this.element.getComponentSelection();!this.selectedSinceMouseDown&&s&&s.toString()&&(this.selectedSinceMouseDown=!0)}onFontEditorButtonClicked(){this.fontEditorSectionManager&&this.fontEditorButton&&this.fontEditorSectionManager.showPopover(this.fontEditorButton.element,this.parentPane)}style(){return this.styleInternal}headerText(){if(this.#k)return this.#k;const e=this.matchedStyles.nodeForStyle(this.styleInternal);return this.styleInternal.type===n.CSSStyleDeclaration.Type.Inline?this.matchedStyles.isInherited(this.styleInternal)?On(In.styleAttribute):"element.style":e&&this.styleInternal.type===n.CSSStyleDeclaration.Type.Attributes?On(In.sattributesStyle,{PH1:e.nodeNameInCorrectCase()}):this.styleInternal.parentRule instanceof n.CSSRule.CSSStyleRule?this.styleInternal.parentRule.selectorText():""}onMouseOutSelector(){this.hoverTimer&&clearTimeout(this.hoverTimer),n.OverlayModel.OverlayModel.hideDOMNodeHighlight()}onMouseEnterSelector(){this.hoverTimer&&clearTimeout(this.hoverTimer),this.hoverTimer=window.setTimeout(this.highlight.bind(this),300)}highlight(e="all"){n.OverlayModel.OverlayModel.hideDOMNodeHighlight();const t=this.parentPane.node();if(!t)return;const i=this.styleInternal.parentRule&&this.styleInternal.parentRule instanceof n.CSSRule.CSSStyleRule?this.styleInternal.parentRule.selectorText():void 0;t.domModel().overlayModel().highlightInOverlay({node:t,selectorList:i},e)}firstSibling(){const e=this.element.parentElement;if(!e)return null;let t=e.firstChild;for(;t;){const e=this.parentPane.sectionByElement.get(t);if(e)return e;t=t.nextSibling}return null}findCurrentOrNextVisible(e,t){if(!this.isHidden())return this;if(this===t)return null;t||(t=this);let n=null;const i=e?this.nextSibling():this.previousSibling();if(i)n=i.findCurrentOrNextVisible(e,t);else{const i=e?this.firstSibling():this.lastSibling();i&&(n=i.findCurrentOrNextVisible(e,t))}return n}lastSibling(){const e=this.element.parentElement;if(!e)return null;let t=e.lastChild;for(;t;){const e=this.parentPane.sectionByElement.get(t);if(e)return e;t=t.previousSibling}return null}nextSibling(){let e=this.element;do{e=e.nextSibling}while(e&&!this.parentPane.sectionByElement.has(e));if(e)return this.parentPane.sectionByElement.get(e)}previousSibling(){let e=this.element;do{e=e.previousSibling}while(e&&!this.parentPane.sectionByElement.has(e));if(e)return this.parentPane.sectionByElement.get(e)}onNewRuleClick(e){e.data.consume();const t=this.styleInternal.parentRule;if(!t||!t.style.range||void 0===t.styleSheetId)return;const n=S.TextRange.TextRange.createFromLocation(t.style.range.endLine,t.style.range.endColumn+1);this.parentPane.addBlankSection(this,t.styleSheetId,n)}styleSheetEdited(e){const t=this.styleInternal.parentRule;t?t.rebase(e):this.styleInternal.rebase(e),this.updateAncestorRuleList(),this.updateRuleOrigin()}createAncestorRules(t){let n=0,i=0,s=0,o=0,r=0;this.nestingLevel=0;const a=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get();for(const e of t.ruleTypes){let l;switch(e){case"MediaRule":l=this.createMediaElement(t.media[n++]);break;case"ContainerRule":l=this.createContainerQueryElement(t.containerQueries[i++]);break;case"ScopeRule":l=this.createScopeElement(t.scopes[s++]);break;case"SupportsRule":l=this.createSupportsElement(t.supports[o++]);break;case"StyleRule":l=this.createNestingElement(t.nestingSelectors?.[r++])}if(l){this.#O.prepend(l);const e=document.createElement("div");e.createChild("span","styles-clipboard-only").textContent=a.repeat(this.nestingLevel),e.style.paddingLeft=`${this.nestingLevel}ch`,e.append("}"),this.#L.prepend(e),this.nestingLevel++}}0===this.headerText().length&&this.nestingLevel--;let l=0;for(const e of this.#O.children){const t=document.createElement("span");t.classList.add("styles-clipboard-only"),t.setAttribute("slot","indent"),t.textContent=a.repeat(l),e.prepend(t),e.style.paddingLeft=`${l}ch`,l++}}createMediaElement(e){if(!e.text||!e.text.includes("(")&&"print"!==e.text)return;let t,i="",s="";switch(e.source){case n.CSSMedia.Source.LINKED_SHEET:case n.CSSMedia.Source.INLINE_SHEET:s=`media="${e.text}"`;break;case n.CSSMedia.Source.MEDIA_RULE:i="@media",s=e.text,e.styleSheetId&&(t=this.handleQueryRuleClick.bind(this,e));break;case n.CSSMedia.Source.IMPORT_RULE:s=`@import ${e.text}`}const o=new p.CSSQuery.CSSQuery;return o.data={queryPrefix:i,queryText:s,onQueryTextClick:t,jslogContext:"media-query"},o}createContainerQueryElement(e){if(!e.text)return;let t;e.styleSheetId&&(t=this.handleQueryRuleClick.bind(this,e));const n=new p.CSSQuery.CSSQuery;return n.data={queryPrefix:"@container",queryName:e.name,queryText:e.text,onQueryTextClick:t,jslogContext:"container-query"},/^style\(.*\)/.test(e.text)||this.addContainerForContainerQuery(e),n}createScopeElement(e){let t;e.styleSheetId&&(t=this.handleQueryRuleClick.bind(this,e));const n=new p.CSSQuery.CSSQuery;return n.data={queryPrefix:"@scope",queryText:e.text,onQueryTextClick:t,jslogContext:"scope"},n}createSupportsElement(e){if(!e.text)return;let t;e.styleSheetId&&(t=this.handleQueryRuleClick.bind(this,e));const n=new p.CSSQuery.CSSQuery;return n.data={queryPrefix:"@supports",queryText:e.text,onQueryTextClick:t,jslogContext:"supports"},n}createNestingElement(e){if(!e)return;const t=document.createElement("div");return t.textContent=`${e} {`,t}async addContainerForContainerQuery(e){const t=await e.getContainerForNode(this.matchedStyles.node().id);if(!t)return;const n=new p.QueryContainer.QueryContainer;n.data={container:p.Helper.legacyNodeToElementsComponentsNode(t.containerNode),queryName:e.name,onContainerLinkClick:e=>{e.preventDefault(),Ls.instance().revealAndSelectNode(t.containerNode,!0,!0),t.containerNode.scrollIntoView()}},n.addEventListener("queriedsizerequested",(async()=>{const e=await t.getContainerSizeDetails();e&&n.updateContainerQueriedSizeDetails(e)})),this.#O.prepend(n)}updateAncestorRuleList(){this.#O.removeChildren(),this.#L.removeChildren(),this.styleInternal.parentRule&&this.styleInternal.parentRule instanceof n.CSSRule.CSSStyleRule&&this.createAncestorRules(this.styleInternal.parentRule),this.#P.style.paddingLeft=`${this.nestingLevel}ch`}isPropertyInherited(e){return!!this.matchedStyles.isInherited(this.styleInternal)&&!n.CSSMetadata.cssMetadata().isPropertyInherited(e)}nextEditableSibling(){let e=this;do{e=e.nextSibling()}while(e&&!e.editable);if(!e)for(e=this.firstSibling();e&&!e.editable;)e=e.nextSibling();return e&&e.editable?e:null}previousEditableSibling(){let e=this;do{e=e.previousSibling()}while(e&&!e.editable);if(!e)for(e=this.lastSibling();e&&!e.editable;)e=e.previousSibling();return e&&e.editable?e:null}refreshUpdate(e){this.parentPane.refreshUpdate(this,e)}updateVarFunctions(e){let t=this.propertiesTreeOutline.firstChild();for(;t;)t!==e&&t instanceof dn&&t.updateTitleIfComputedValueChanged(),t=t.traverseNextTreeElement(!1,null,!0)}update(e){const t=this.headerText();if(this.selectorElement.textContent=t,this.titleElement.classList.toggle("hidden",0===t.length),this.markSelectorMatches(),e)this.onpopulate();else{let e=this.propertiesTreeOutline.firstChild();for(;e&&e instanceof dn;)e.setOverloaded(this.isPropertyOverloaded(e.property)),e=e.traverseNextTreeElement(!1,null,!0)}}showAllItems(e){e&&e.consume(),this.forceShowAll||(this.forceShowAll=!0,this.onpopulate())}onpopulate(){this.parentPane.setActiveProperty(null),this.nextEditorTriggerButtonIdx=1,this.propertiesTreeOutline.removeChildren();const e=this.styleInternal;let t=0;const n=e.leadingProperties(),i=50+n.length-this.originalPropertiesCount;for(const s of n){if(!this.forceShowAll&&t>=i)break;t++;const n=s.getLonghandProperties().length>0,o=this.isPropertyInherited(s.name),r=this.isPropertyOverloaded(s);if(e.parentRule&&e.parentRule.isUserAgent()&&o)continue;const a=new dn({stylesPane:this.parentPane,section:this,matchedStyles:this.matchedStyles,property:s,isShorthand:n,inherited:o,overloaded:r,newProperty:!1});a.setComputedStyles(this.computedStyles),a.setParentsComputedStyles(this.parentsComputedStyles),this.propertiesTreeOutline.appendChild(a)}t<n.length?(this.showAllButton.classList.remove("hidden"),this.showAllButton.textContent=On(In.showAllPropertiesSMore,{PH1:n.length-t})):this.showAllButton.classList.add("hidden")}isPropertyOverloaded(e){return"Overloaded"===this.matchedStyles.propertyState(e)}updateFilter(){let e=!1;this.showAllItems();for(const t of this.propertiesTreeOutline.rootElement().children())if(t instanceof dn){const n=t.updateFilter();e=e||n}const t=this.parentPane.filterRegex(),n=!e&&null!==t&&!t.test(this.element.deepTextContent());return this.isHiddenInternal=n,this.element.classList.toggle("hidden",n),!n&&this.styleInternal.parentRule&&this.markSelectorHighlights(),!n}isHidden(){return this.isHiddenInternal}markSelectorMatches(){const e=this.styleInternal.parentRule;if(!(e&&e instanceof n.CSSRule.CSSStyleRule))return;const t=this.matchedStyles.getMatchingSelectors(e),i=new Array(e.selectors.length).fill(!1);for(const e of t)i[e]=!0;if(this.parentPane.isEditingStyle)return;const s=An.renderSelectors(e.selectors,i,this.elementToSelectorIndex);this.selectorElement.removeChildren(),this.selectorElement.appendChild(s),this.markSelectorHighlights()}static getSpecificityStoredForNodeElement(e){return An.#A.get(e)}static renderSelectors(e,t,n){const s=document.createDocumentFragment();for(const[o,r]of e.entries()){o&&i.UIUtils.createTextChild(s,", ");const a=document.createElement("span");a.classList.add("simple-selector"),a.classList.toggle("selector-matches",t[o]),r.specificity&&An.#A.set(a,r.specificity),n.set(a,o),a.textContent=e[o].text,s.append(a)}return s}markSelectorHighlights(){const e=this.selectorElement.getElementsByClassName("simple-selector"),t=this.parentPane.filterRegex();for(let n=0;n<e.length;++n){const i=null!==t&&t.test(e[n].textContent||"");e[n].classList.toggle("filter-match",i)}}addNewBlankProperty(e=this.propertiesTreeOutline.rootElement().childCount()){const t=this.styleInternal.newBlankProperty(e),n=new dn({stylesPane:this.parentPane,section:this,matchedStyles:this.matchedStyles,property:t,isShorthand:!1,inherited:!1,overloaded:!1,newProperty:!0});return this.propertiesTreeOutline.insertChild(n,t.index),n}handleEmptySpaceMouseDown(){this.willCauseCancelEditing=this.parentPane.isEditingStyle,this.selectedSinceMouseDown=!1}handleEmptySpaceClick(e){if(!this.editable||this.element.hasSelection()||this.willCauseCancelEditing||this.selectedSinceMouseDown)return;const t=e.target;if(t.classList.contains("header")||this.element.classList.contains("read-only")||t.enclosingNodeOrSelfWithClass("ancestor-rule-list"))return void e.consume();const n=i.UIUtils.deepElementFromEvent(e),s=n&&i.TreeOutline.TreeElement.getTreeElementBylistItemNode(n);s&&s instanceof dn?this.addNewBlankProperty(s.property.index+1).startEditingName():t.classList.contains("selector-container")||t.classList.contains("styles-section-subtitle")?this.addNewBlankProperty(0).startEditingName():this.addNewBlankProperty().startEditingName(),e.consume(!0)}handleQueryRuleClick(t,n){const s=n.currentTarget;if(i.UIUtils.isBeingEdited(s))return;if(i.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(n)&&this.navigable){const i=t.rawLocation();if(!i)return void n.consume(!0);const s=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(i);return s&&e.Revealer.reveal(s),void n.consume(!0)}if(!this.editable)return;const o=new i.InplaceEditor.Config(this.editingMediaCommitted.bind(this,t),this.editingMediaCancelled.bind(this,s),void 0,this.editingMediaBlurHandler.bind(this));i.InplaceEditor.InplaceEditor.startEditing(s,o);const r=s.getComponentSelection();r&&r.selectAllChildren(s),this.parentPane.setEditingStyle(!0);s.enclosingNodeOrSelfWithClass("query").classList.add("editing-query"),n.consume(!0)}editingMediaFinished(e){this.parentPane.setEditingStyle(!1);e.enclosingNodeOrSelfWithClass("query").classList.remove("editing-query")}editingMediaCancelled(e){this.editingMediaFinished(e),this.markSelectorMatches();const t=e.getComponentSelection();t&&t.collapse(e,0)}editingMediaBlurHandler(){return!0}async editingMediaCommitted(e,t,i,s,o,r){this.parentPane.setEditingStyle(!1),this.editingMediaFinished(t),i&&(i=i.trim()),this.parentPane.setUserOperation(!0);const a=this.parentPane.cssModel();if(a&&e.styleSheetId){const t=e.range;let s=!1;s=e instanceof n.CSSContainerQuery.CSSContainerQuery?await a.setContainerQueryText(e.styleSheetId,t,i):e instanceof n.CSSSupports.CSSSupports?await a.setSupportsText(e.styleSheetId,t,i):e instanceof n.CSSScope.CSSScope?await a.setScopeText(e.styleSheetId,t,i):await a.setMediaText(e.styleSheetId,t,i),s&&(this.matchedStyles.resetActiveProperties(),this.parentPane.refreshUpdate(this)),this.parentPane.setUserOperation(!1),this.editingMediaTextCommittedForTest()}}editingMediaTextCommittedForTest(){}handleSelectorClick(e){const t=e.target;if(t){if(i.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&this.navigable&&t.classList.contains("simple-selector")){const n=this.elementToSelectorIndex.get(t);return n&&this.navigateToSelectorSource(n,!0),void e.consume(!0)}this.element.hasSelection()||(this.startEditingAtFirstPosition(),e.consume(!0))}}handleContextMenuEvent(e){if(!e.target)return;const t=new i.ContextMenu.ContextMenu(e);t.clipboardSection().appendItem(On(In.copySelector),(()=>{const e=this.headerText();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(10)}),{jslogContext:"copy-selector"}),t.clipboardSection().appendItem(On(In.copyRule),(()=>{const e=Qn.formatLeadingProperties(this).ruleText;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(7)}),{jslogContext:"copy-rule"}),t.clipboardSection().appendItem(On(In.copyAllDeclarations),(()=>{const e=Qn.formatLeadingProperties(this).allDeclarationText;o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(8)}),{jslogContext:"copy-all-declarations"}),t.clipboardSection().appendItem(On(In.copyAllCSSChanges),(async()=>{const e=await this.parentPane.getFormattedChanges();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),o.userMetrics.styleTextCopied(2)}),{jslogContext:"copy-all-css-changes"}),t.show()}navigateToSelectorSource(e,t){const i=this.parentPane.cssModel();if(!i)return;const s=this.styleInternal.parentRule;if(!s||void 0===s.styleSheetId)return;const o=i.styleSheetHeaderForId(s.styleSheetId);if(!o)return;const r=new n.CSSModel.CSSLocation(o,s.lineNumberInSource(e),s.columnNumberInSource(e));An.revealSelectorSource(r,t)}static revealSelectorSource(t,n){const i=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(t);i&&e.Revealer.reveal(i,!n)}startEditingAtFirstPosition(){this.editable&&(this.styleInternal.parentRule?this.startEditingSelector():this.moveEditorFromSelector("forward"))}startEditingSelector(){const e=this.selectorElement;if(i.UIUtils.isBeingEdited(e))return;e.scrollIntoViewIfNeeded(!1);const t=e.textContent;null!==t&&(e.textContent=t.replace(/\s+/g," ").trim());const n=new i.InplaceEditor.Config(this.editingSelectorCommitted.bind(this),this.editingSelectorCancelled.bind(this));i.InplaceEditor.InplaceEditor.startEditing(this.selectorElement,n);const s=e.getComponentSelection();s&&s.selectAllChildren(e),this.parentPane.setEditingStyle(!0),e.classList.contains("simple-selector")&&this.navigateToSelectorSource(0,!1)}moveEditorFromSelector(e){if(this.markSelectorMatches(),e)if("forward"===e){let e=this.propertiesTreeOutline.firstChild();for(;e&&e.inherited();){const t=e.nextSibling;e=t instanceof dn?t:null}e?e.startEditingName():this.addNewBlankProperty().startEditingName()}else{const e=this.previousEditableSibling();if(!e)return;e.addNewBlankProperty().startEditingName()}}editingSelectorCommitted(e,t,n,i,s){if(this.editingSelectorEnded(),t&&(t=t.trim()),t===n)return this.selectorElement.textContent=t,void this.moveEditorFromSelector(s);const o=this.styleInternal.parentRule;o&&(this.parentPane.setUserOperation(!0),this.setHeaderText(o,t).then(function(){this.parentPane.setUserOperation(!1),this.moveEditorFromSelector(s),this.editingSelectorCommittedForTest()}.bind(this)))}setHeaderText(e,t){function i(e){const t=this.matchedStyles.getMatchingSelectors(e).length>0;this.propertiesTreeOutline.element.classList.toggle("no-affect",!t),this.matchedStyles.resetActiveProperties(),this.parentPane.refreshUpdate(this)}if(!(e instanceof n.CSSRule.CSSStyleRule))return Promise.resolve();const s=e.selectorRange();return s?(this.#k=void 0,e.setSelectorText(t).then(function(e,t){return t?this.matchedStyles.recomputeMatchingSelectors(e).then(i.bind(this,e)):Promise.resolve()}.bind(this,e,Boolean(s)))):Promise.resolve()}editingSelectorCommittedForTest(){}updateRuleOrigin(){this.selectorRefElement.removeChildren(),this.selectorRefElement.appendChild(this.createRuleOriginNode(this.matchedStyles,this.parentPane.linkifier,this.styleInternal.parentRule))}editingSelectorEnded(){this.parentPane.setEditingStyle(!1)}editingSelectorCancelled(){this.editingSelectorEnded(),this.markSelectorMatches()}closestPropertyForEditing(e){const t=this.propertiesTreeOutline.rootElement();return e>=t.childCount()?t.lastChild():t.childAt(e)}}class kn extends An{normal;ruleLocation;styleSheetId;constructor(e,t,i,s,o,r,a){const l=e.cssModel();super(e,t,n.CSSRule.CSSStyleRule.createDummyRule(l,i).style,a,null,null),this.normal=!1,this.ruleLocation=o,this.styleSheetId=s,this.selectorRefElement.removeChildren(),this.selectorRefElement.appendChild(An.linkifyRuleLocation(l,this.parentPane.linkifier,s,this.actualRuleLocation())),r&&r.parentRule&&r.parentRule instanceof n.CSSRule.CSSStyleRule&&this.createAncestorRules(r.parentRule),this.element.classList.add("blank-section")}actualRuleLocation(){const e=this.rulePrefix().split("\n"),t=e[e.length-1],n=new S.TextRange.TextRange(0,0,e.length-1,t?t.length:0);return this.ruleLocation.rebaseAfterTextEdit(S.TextRange.TextRange.createFromLocation(0,0),n)}rulePrefix(){return 0===this.ruleLocation.startLine&&0===this.ruleLocation.startColumn?"":"\n\n"}get isBlank(){return!this.normal}editingSelectorCommitted(e,t,n,i,s){if(!this.isBlank)return void super.editingSelectorCommitted(e,t,n,i,s);function o(e){const t=this.matchedStyles.getMatchingSelectors(e).length>0;this.makeNormal(e),t||this.propertiesTreeOutline.element.classList.add("no-affect"),this.updateRuleOrigin(),this.parentPane.setUserOperation(!1),this.editingSelectorEnded(),this.element.parentElement&&this.moveEditorFromSelector(s),this.markSelectorMatches(),this.editingSelectorCommittedForTest()}t&&(t=t.trim()),this.parentPane.setUserOperation(!0);const r=this.parentPane.cssModel(),a=this.rulePrefix()+t+" {}";r&&r.addRule(this.styleSheetId,a,this.ruleLocation).then(function(e){return e?this.matchedStyles.addNewRule(e,this.matchedStyles.node()).then(o.bind(this,e)):(this.editingSelectorCancelled(),this.editingSelectorCommittedForTest(),Promise.resolve())}.bind(this))}editingSelectorCancelled(){this.parentPane.setUserOperation(!1),this.isBlank?(this.editingSelectorEnded(),this.parentPane.removeSection(this)):super.editingSelectorCancelled()}makeNormal(e){this.element.classList.remove("blank-section"),this.styleInternal=e.style,this.normal=!0}}class Rn extends An{constructor(e,t,n,i,s,o){super(e,t,n,i,null,null,s),o||this.element.classList.add("hidden"),this.selectorElement.className="property-registration-key"}async setHeaderText(e,t){if(!(e instanceof n.CSSRule.CSSPropertyRule))return;e.propertyName().range&&await e.setPropertyName(t)&&this.parentPane.forceUpdate()}createRuleOriginNode(e,t,n){return n?super.createRuleOriginNode(e,t,n):document.createTextNode("CSS.registerProperty")}}class Dn extends An{constructor(e,t,n,i){super(e,t,n,i,null,null),this.selectorElement.className="font-palette-values-key"}}class Fn extends An{constructor(e,t,n,i,s){super(e,t,n,i,null,null),this.selectorElement.className="position-try-values-key",this.propertiesTreeOutline.element.classList.toggle("no-affect",!s)}}class Un extends An{constructor(e,t,n,i){super(e,t,n,i,null,null),this.selectorElement.className="keyframe-key"}headerText(){return this.styleInternal.parentRule instanceof n.CSSRule.CSSKeyframeRule?this.styleInternal.parentRule.key().text:""}setHeaderText(e,t){if(!(e instanceof n.CSSRule.CSSKeyframeRule))return Promise.resolve();return e.key().range?e.setKeyText(t).then(function(e){e&&this.parentPane.refreshUpdate(this)}.bind(this)):Promise.resolve()}isPropertyInherited(e){return!1}isPropertyOverloaded(e){return!1}markSelectorHighlights(){}markSelectorMatches(){this.styleInternal.parentRule instanceof n.CSSRule.CSSKeyframeRule&&(this.selectorElement.textContent=this.styleInternal.parentRule.key().text)}highlight(){}}class Bn extends An{isPropertyInherited(e){return!1}}var _n=Object.freeze({__proto__:null,StylePropertiesSection:An,BlankStylePropertiesSection:kn,RegisteredPropertiesSection:Rn,FontPaletteValuesRuleSection:Dn,PositionTryRuleSection:Fn,KeyframePropertiesSection:Un,HighlightPseudoStylePropertiesSection:Bn});class Vn{styleSidebarPane;constructor(e){this.styleSidebarPane=e}highlightProperty(e){for(const e of this.styleSidebarPane.allSections())for(let t=e.propertiesTreeOutline.firstChild();t;t=t.nextSibling)t.onpopulate();const t=this.styleSidebarPane.allSections().find((t=>t.style().leadingProperties().includes(e)));if(!t)return;t.showAllItems();const n=this.findTreeElementFromSection((t=>t.property===e),t);n&&(n.parent&&n.parent.expand(),this.scrollAndHighlightTreeElement(n),t.element.focus())}findAndHighlightSectionBlock(e){const t=this.styleSidebarPane.getSectionBlockByName(e);if(!t||0===t.sections.length)return;const[n]=t.sections;n.showAllItems(),b.highlightElement(t.titleElement())}findAndHighlightSection(e,t){const n=this.styleSidebarPane.getSectionBlockByName(t),i=n?.sections.find((t=>t.headerText()===e));i&&n&&(n.expand(!0),i.showAllItems(),b.highlightElement(i.element))}findAndHighlightPropertyName(e,t,n){const i=n?this.styleSidebarPane.getSectionBlockByName(n):void 0,s=i?.sections??this.styleSidebarPane.allSections();if(!s)return!1;for(const n of s){if(t&&n.headerText()!==t)continue;if(!n.style().hasActiveProperty(e))continue;i?.expand(!0),n.showAllItems();const s=this.findTreeElementFromSection((t=>t.property.name===e&&!t.overloaded()),n);if(s)return this.scrollAndHighlightTreeElement(s),n.element.focus(),!0}return!1}findTreeElementAndSection(e){for(const t of this.styleSidebarPane.allSections()){const n=this.findTreeElementFromSection(e,t);if(n)return{treeElement:n,section:t}}return{treeElement:null,section:null}}findTreeElementFromSection(e,t){let n=t.propertiesTreeOutline.firstChild();for(;n&&n instanceof dn;){if(e(n))return n;n=n.traverseNextTreeElement(!1,null,!0)}return null}scrollAndHighlightTreeElement(e){b.highlightElement(e.listItemElement)}}var Hn=Object.freeze({__proto__:null,StylePropertyHighlighter:Vn});const Wn=new CSSStyleSheet;Wn.replaceSync(".styles-section{min-height:18px;white-space:nowrap;user-select:text;border-bottom:1px solid var(--sys-color-divider);position:relative;overflow:hidden;padding:2px 2px 4px 4px;&:last-child{border-bottom:none}&.has-open-popover{z-index:1}&.read-only{background-color:var(--sys-color-cdt-base-container);font-style:italic}&:focus-visible,\n  &.read-only:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.simple-selector.filter-match{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-surface)}.devtools-link{user-select:none}.styles-section-subtitle devtools-icon{margin-bottom:-4px}.styles-section-subtitle .devtools-link{color:var(--sys-color-on-surface);text-decoration-color:var(--sys-color-neutral-bright);outline-offset:0}.selector,\n  .try-rule-selector-element,\n  .ancestor-rule-list,\n  .ancestor-closing-braces{color:var(--sys-color-token-meta)}.ancestor-rule-list,\n  .styles-section-title{overflow-wrap:break-word;white-space:normal}.ancestor-rule-list devtools-css-query{display:block}.simple-selector.selector-matches,\n  &.keyframe-key{color:var(--sys-color-on-surface)}.style-properties{margin:0;padding:2px 4px 0 0;list-style:none;clear:both;display:flex}&.matched-styles .style-properties{padding-left:0}& span.simple-selector:hover{text-decoration:var(--override-styles-section-text-hover-text-decoration);cursor:var(--override-styles-section-text-hover-cursor)}&.styles-panel-hovered:not(.read-only),\n  &.styles-panel-hovered:not(.read-only) devtools-css-query{--override-styles-section-text-hover-text-decoration:underline;--override-styles-section-text-hover-cursor:default}}.sidebar-pane-closing-brace{clear:both}.styles-section-subtitle{color:var(--sys-color-token-subtle);float:right;padding:var(--sys-size-2) var(--sys-size-2) 0 var(--sys-size-8);max-width:100%;height:15px;margin-bottom:-1px}.styles-section-subtitle *{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;max-width:100%}.sidebar-pane-open-brace,\n.sidebar-pane-closing-brace{color:var(--sys-color-on-surface)}@keyframes styles-element-state-pane-slidein{from{margin-top:-60px}to{margin-top:0}}@keyframes styles-element-state-pane-slideout{from{margin-top:0}to{margin-top:-60px}}.styles-sidebar-toolbar-pane{position:relative;animation-duration:0.1s;animation-direction:normal}.styles-sidebar-toolbar-pane-container{position:relative;overflow:hidden;flex-shrink:0}.styles-selector{cursor:text}.styles-clipboard-only{display:inline-block;width:0;opacity:0%;pointer-events:none;white-space:pre}.styles-sidebar-pane-toolbar-container{flex-shrink:0;overflow:hidden;position:sticky;top:0;background-color:var(--sys-color-cdt-base-container);z-index:2}.styles-sidebar-pane-toolbar{border-bottom:1px solid var(--sys-color-divider)}.styles-pane-toolbar{width:100%}.font-toolbar-hidden{visibility:hidden}.sidebar-separator{background-color:var(--sys-color-surface2);padding:0 5px;border-bottom:1px solid var(--sys-color-divider);color:var(--sys-color-token-subtle);white-space:nowrap;text-overflow:ellipsis;overflow:hidden;line-height:22px;> span.monospace{max-width:180px;display:inline-block;overflow:hidden;text-overflow:ellipsis;vertical-align:middle;margin-left:2px}&.layer-separator{display:flex}&.empty-section{border-bottom:none}}.sidebar-pane-section-toolbar{position:absolute;right:0;bottom:-1px;z-index:0;&.new-rule-toolbar{visibility:hidden;margin-bottom:5px;--toolbar-height:16px}&.shifted-toolbar{padding-right:32px}}.styles-pane:not(.is-editing-style) .styles-section.matched-styles:not(.read-only):hover .sidebar-pane-section-toolbar.new-rule-toolbar{visibility:visible}.styles-show-all{margin-left:16px;text-overflow:ellipsis;overflow:hidden;max-width:-webkit-fill-available}@media (forced-colors: active){.sidebar-pane-section-toolbar{forced-color-adjust:none;border-color:1px solid ButtonText;background-color:ButtonFace}.styles-section{&:focus-visible,\n    &.read-only:focus-visible{forced-color-adjust:none;background-color:Highlight}.styles-section-subtitle{.devtools-link{color:linktext;text-decoration-color:linktext;&:focus-visible{color:HighlightText}}}&:focus-visible *,\n    &.read-only:focus-visible *,\n    &:focus-visible .styles-section-subtitle .devtools-link{color:HighlightText;text-decoration-color:HighlightText}&:focus-visible .sidebar-pane-section-toolbar{background-color:ButtonFace}&:focus-visible{--webkit-css-property-color:HighlightText}}}.spinner::before{--dimension:24px;margin-top:2em;left:calc(50% - var(--dimension) / 2)}.section-block-expand-icon{margin-bottom:-4px}\n/*# sourceURL=stylesSidebarPane.css */\n");class jn{#R=new Map;fetchPromiseForTest;constructor(e){this.fetchPromiseForTest=e?fetch(`${e}third_party/vscode.web-custom-data/browsers.css-data.json`).then((e=>e.json())).then((e=>{for(const t of e.properties)this.#R.set(t.name,t)})).catch():Promise.resolve()}static create(){const e=t.Runtime.getRemoteBase();return new jn(e?.base??"")}findCssProperty(e){return this.#R.get(e)}}var zn=Object.freeze({__proto__:null,WebCustomData:jn});const $n={noMatchingSelectorOrStyle:"No matching selector or style",visibleSelectors:"{n, plural, =1 {# visible selector listed below} other {# visible selectors listed below}}",invalidPropertyValue:"Invalid property value",unknownPropertyName:"Unknown property name",pseudoSElement:"Pseudo ::{PH1} element",inheritedFroms:"Inherited from ",inheritedFromSPseudoOf:"Inherited from ::{PH1} pseudo of ",incrementdecrementWithMousewheelOne:"Increment/decrement with mousewheel or up/down keys. {PH1}: R ±1, Shift: G ±1, {PH2}: B ±1",incrementdecrementWithMousewheelHundred:"Increment/decrement with mousewheel or up/down keys. {PH1}: ±100, Shift: ±10, {PH2}: ±0.1",invalidString:"{PH1}, property name: {PH2}, property value: {PH3}",toggleRenderingEmulations:"Toggle common rendering emulations",automaticDarkMode:"Automatic dark mode",copyAllCSSChanges:"Copy CSS changes",copiedToClipboard:"Copied to clipboard",layer:"Layer",clickToRevealLayer:"Click to reveal layer in layer tree",specificity:"Specificity: {PH1}"},Kn=r.i18n.registerUIStrings("panels/elements/StylesSidebarPane.ts",$n),Gn=r.i18n.getLocalizedString.bind(void 0,Kn),Yn="@property",qn=[{mode:"padding",properties:["padding"]},{mode:"border",properties:["border"]},{mode:"margin",properties:["margin"]},{mode:"gap",properties:["gap","grid-gap"]},{mode:"column-gap",properties:["column-gap","grid-column-gap"]},{mode:"row-gap",properties:["row-gap","grid-row-gap"]},{mode:"grid-template-columns",properties:["grid-template-columns"]},{mode:"grid-template-rows",properties:["grid-template-rows"]},{mode:"grid-template-areas",properties:["grid-areas"]},{mode:"justify-content",properties:["justify-content"]},{mode:"align-content",properties:["align-content"]},{mode:"align-items",properties:["align-items"]},{mode:"flexibility",properties:["flex","flex-basis","flex-grow","flex-shrink"]}];let Xn;class Qn extends(e.ObjectWrapper.eventMixin(Qe)){currentToolbarPane;animatedToolbarPane;pendingWidget;pendingWidgetToggle;toolbar;toolbarPaneElement;lastFilterChange;visibleSections;noMatchesElement;sectionsContainer;sectionByElement;swatchPopoverHelperInternal;linkifier;decorator;lastRevealedProperty;userOperation;isEditingStyle;filterRegexInternal;isActivePropertyHighlighted;initialUpdateCompleted;hasMatchedStyles;sectionBlocks;idleCallbackManager;needsForceUpdate;resizeThrottler;scrollerElement;boundOnScroll;imagePreviewPopover;#D;#F;#U;#B=new WeakMap;activeCSSAngle;#_=new Map;#V;#H;#W;static instance(e){return Xn&&!e?.forceNew||(Xn=new Qn),Xn}constructor(){super(!0),this.setMinimumSize(96,26),this.registerCSSFiles([Wn]),e.Settings.Settings.instance().moduleSetting("text-editor-indent").addChangeListener(this.update.bind(this)),this.currentToolbarPane=null,this.animatedToolbarPane=null,this.pendingWidget=null,this.pendingWidgetToggle=null,this.toolbar=null,this.lastFilterChange=null,this.visibleSections=null,this.toolbarPaneElement=this.createStylesSidebarToolbar(),this.computedStyleModelInternal=new Z,this.noMatchesElement=this.contentElement.createChild("div","gray-info-message hidden"),this.noMatchesElement.textContent=Gn($n.noMatchingSelectorOrStyle),this.sectionsContainer=this.contentElement.createChild("div"),i.ARIAUtils.markAsList(this.sectionsContainer),this.sectionsContainer.addEventListener("keydown",this.sectionsContainerKeyDown.bind(this),!1),this.sectionsContainer.addEventListener("focusin",this.sectionsContainerFocusChanged.bind(this),!1),this.sectionsContainer.addEventListener("focusout",this.sectionsContainerFocusChanged.bind(this),!1),this.sectionByElement=new WeakMap,this.swatchPopoverHelperInternal=new y.SwatchPopoverHelper.SwatchPopoverHelper,this.swatchPopoverHelperInternal.addEventListener("WillShowPopover",this.hideAllPopovers,this),this.linkifier=new f.Linkifier.Linkifier(Jn,!0),this.decorator=new Vn(this),this.lastRevealedProperty=null,this.userOperation=!1,this.isEditingStyle=!1,this.filterRegexInternal=null,this.isActivePropertyHighlighted=!1,this.initialUpdateCompleted=!1,this.hasMatchedStyles=!1,this.contentElement.classList.add("styles-pane"),this.sectionBlocks=[],this.idleCallbackManager=null,this.needsForceUpdate=!1,Xn=this,i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.forceUpdate,this),this.contentElement.addEventListener("copy",this.clipboardCopy.bind(this)),this.resizeThrottler=new e.Throttler.Throttler(100),this.boundOnScroll=this.onScroll.bind(this),this.imagePreviewPopover=new ie(this.contentElement,(e=>{const t=e.composedPath()[0];return t instanceof Element?t:null}),(()=>this.node())),this.activeCSSAngle=null;const t=e.Settings.Settings.instance().moduleSetting("show-css-property-documentation-on-hover");t.addChangeListener((e=>{const t=Boolean(e.data)?1:2;o.userMetrics.cssPropertyDocumentation(t)})),this.#F=new i.PopoverHelper.PopoverHelper(this.contentElement,(e=>{const n=e.composedPath()[0],i=e.composedPath()[2];if(!(n&&n instanceof Element))return null;if(i instanceof Element&&i.matches(".hint")){const e=Ht.get(i);if(e)return this.#F.jslogContext="elements.css-hint",{box:n.boxInWindow(),show:async t=>{const n=new p.CSSHintDetailsView.CSSHintDetailsView(e);return t.contentElement.appendChild(n),!0}}}if(t.get()&&n.matches(".webkit-css-property")){this.#D||(this.#D=jn.create());const e=n.textContent,t=e&&this.#D.findCssProperty(e);if(t)return this.#F.jslogContext="elements.css-property-doc",{box:n.boxInWindow(),show:async e=>{const n=new p.CSSPropertyDocsView.CSSPropertyDocsView(t);return e.contentElement.appendChild(n),o.userMetrics.cssPropertyDocumentation(0),!0}}}if(n.matches(".simple-selector")){const e=An.getSpecificityStoredForNodeElement(n);return this.#F.jslogContext="elements.css-selector-specificity",{box:n.boxInWindow(),show:async t=>{t.setIgnoreLeftMargin(!0);const n=document.createElement("span");return n.textContent=Gn($n.specificity,{PH1:e?`(${e.a},${e.b},${e.c})`:"(?,?,?)"}),t.contentElement.appendChild(n),!0}}}return null})),this.#F.setDisableOnClick(!0),this.#F.setTimeout(300),this.#F.setHasPadding(!0),this.#U=new i.PopoverHelper.PopoverHelper(this.contentElement,(e=>{for(let t=e.composedPath().length-1;t>=0;--t){const n=e.composedPath()[t],i=this.#B.get(n),o=i?i.contents():void 0;if(o)return{box:n.boxInWindow(),show:async e=>(e.setJsLog(`${s.popover(`${i?.jslogContext??"elements.generic-sidebar-popover"}`).parent("popoverParent")}`),e.contentElement.classList.add("borderless-popover"),e.contentElement.appendChild(o),!0)}}return null}),"elements.generic-sidebar-popover"),this.#U.setDisableOnClick(!0),this.#U.setTimeout(500,200)}addPopover(e,t){this.#B.set(e,t)}onScroll(e){this.hideAllPopovers()}swatchPopoverHelper(){return this.swatchPopoverHelperInternal}setUserOperation(e){this.userOperation=e}createExclamationMark(e,t){const s=document.createElement("span");s.classList.add("exclamation-mark");const o=n.CSSMetadata.cssMetadata().isCSSPropertyName(e.name)?Gn($n.invalidPropertyValue):Gn($n.unknownPropertyName);null===t?i.Tooltip.Tooltip.install(s,o):this.addPopover(s,{contents:()=>t});const r=Gn($n.invalidString,{PH1:o,PH2:e.name,PH3:e.value});return e.setDisplayedStringForInvalidProperty(r),s}static ignoreErrorsForProperty(e){function t(e){return!e.startsWith("-webkit-")&&/^[-_][\w\d]+-\w/.test(e)}const n=e.name.toLowerCase();if("_"===n.charAt(0))return!0;if("filter"===n)return!0;if(n.startsWith("scrollbar-"))return!0;if(t(n))return!0;const i=e.value.toLowerCase();return!!i.endsWith("\\9")||!!t(i)}static formatLeadingProperties(t){const n=t.headerText(),i=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),s=t.style(),o=[];for(const e of s.leadingProperties())e.disabled?o.push(`${i}/* ${e.name}: ${e.value}; */`):o.push(`${i}${e.name}: ${e.value};`);const r=o.join("\n");return{allDeclarationText:r,ruleText:`${n} {\n${r}\n}`}}revealProperty(e){this.decorator.highlightProperty(e),this.lastRevealedProperty=e,this.update()}jumpToProperty(e,t,n){return this.decorator.findAndHighlightPropertyName(e,t,n)}jumpToSection(e,t){this.decorator.findAndHighlightSection(e,t)}jumpToSectionBlock(e){this.decorator.findAndHighlightSectionBlock(e)}forceUpdate(){this.needsForceUpdate=!0,this.swatchPopoverHelperInternal.hide(),this.#H?.abort(),this.resetCache(),this.update()}sectionsContainerKeyDown(e){const t=a.DOMUtilities.deepActiveElement(this.sectionsContainer.ownerDocument);if(!t)return;const n=this.sectionByElement.get(t);if(!n)return;let i=null,s=!1;switch(e.key){case"ArrowUp":case"ArrowLeft":i=n.previousSibling()||n.lastSibling(),s=!1;break;case"ArrowDown":case"ArrowRight":i=n.nextSibling()||n.firstSibling(),s=!0;break;case"Home":i=n.firstSibling(),s=!0;break;case"End":i=n.lastSibling(),s=!1}i&&this.filterRegexInternal&&(i=i.findCurrentOrNextVisible(s)),i&&(i.element.focus(),e.consume(!0))}sectionsContainerFocusChanged(){this.resetFocus()}resetFocus(){if(this.noMatchesElement.classList.contains("hidden")&&this.sectionBlocks[0]&&this.sectionBlocks[0].sections[0]){const e=this.sectionBlocks[0].sections[0].findCurrentOrNextVisible(!0);e&&(e.element.tabIndex=this.sectionsContainer.hasFocus()?-1:0)}}onAddButtonLongClick(e){const t=this.cssModel();if(!t)return;const n=t.styleSheetHeaders().filter((function(e){return!e.isViaInspector()&&!e.isInline&&Boolean(e.resourceURL())})),s=[];for(let e=0;e<n.length;++e){const t=n[e],i=this.createNewRuleInStyleSheet.bind(this,t);s.push({text:m.ResourceUtils.displayNameForURL(t.resourceURL()),handler:i})}s.sort((function(e,t){return a.StringUtilities.naturalOrderComparator(e.text,t.text)}));const o=new i.ContextMenu.ContextMenu(e);for(let e=0;e<s.length;++e){const t=s[e];o.defaultSection().appendItem(t.text,t.handler,{jslogContext:"style-sheet-header"})}o.footerSection().appendItem("inspector-stylesheet",this.createNewRuleInViaInspectorStyleSheet.bind(this),{jslogContext:"inspector-stylesheet"}),o.show()}onFilterChanged(e){const t=e.data?new RegExp(a.StringUtilities.escapeForRegExp(e.data),"i"):null;this.lastFilterChange=Date.now(),this.filterRegexInternal=t,this.updateFilter(),this.resetFocus(),setTimeout((()=>{if(this.lastFilterChange){Date.now()-this.lastFilterChange<500||i.ARIAUtils.alert(this.visibleSections?Gn($n.visibleSelectors,{n:this.visibleSections}):Gn($n.noMatchingSelectorOrStyle))}}),500)}refreshUpdate(e,t){if(t)for(const e of this.allSections())e instanceof kn&&e.isBlank||e.updateVarFunctions(t);if(this.isEditingStyle)return;const n=this.node();if(n){for(const t of this.allSections())t instanceof kn&&t.isBlank||t.update(t===e);this.filterRegexInternal&&this.updateFilter(),this.swatchPopoverHelper().reposition(),this.nodeStylesUpdatedForTest(n,!1)}}async doUpdate(){this.#H?.abort(),this.#H=new AbortController,await this.#j(this.#H.signal);const e=this?.contentElement?.enclosingNodeOrSelfWithClass("style-panes-wrapper")?.parentElement?.querySelectorAll(".style-panes-wrapper");if(e.length>0)for(const t of e)this.scrollerElement=t,this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1)}async#j(e){this.initialUpdateCompleted||window.setTimeout((()=>{e.aborted||this.initialUpdateCompleted||this.sectionsContainer.createChild("span","spinner")}),200);const n=await this.fetchMatchedCascade();if(e.aborted)return;const i=this.node()?.id,s=n?.getParentLayoutNodeId(),[o,r]=await Promise.all([this.fetchComputedStylesFor(i),this.fetchComputedStylesFor(s)]);e.aborted||(await this.innerRebuildUpdate(e,n,o,r),e.aborted||(this.initialUpdateCompleted||(this.initialUpdateCompleted=!0,this.appendToolbarItem(this.createRenderingShortcuts()),t.Runtime.experiments.isEnabled("styles-pane-css-changes")&&(this.#V=this.createCopyAllChangesButton(),this.appendToolbarItem(this.#V),this.#V.element.classList.add("hidden")),this.dispatchEventToListeners("InitialUpdateCompleted")),this.nodeStylesUpdatedForTest(this.node(),!0),this.dispatchEventToListeners("StylesUpdateCompleted",{hasMatchedStyles:this.hasMatchedStyles})))}async fetchComputedStylesFor(e){const t=this.node();return null===t||void 0===e?null:await t.domModel().cssModel().getComputedStyle(e)}onResize(){this.resizeThrottler.schedule(this.innerResize.bind(this))}innerResize(){const e=this.contentElement.getBoundingClientRect().width+"px";return this.allSections().forEach((t=>{t.propertiesTreeOutline.element.style.width=e})),this.hideAllPopovers(),Promise.resolve()}resetCache(){const e=this.cssModel();e&&e.discardCachedMatchedCascade()}fetchMatchedCascade(){const e=this.node();if(!e||!this.cssModel())return Promise.resolve(null);const t=this.cssModel();return t?t.cachedMatchedCascadeForNode(e).then(function(e){return e&&e.node()===this.node()?e:null}.bind(this)):Promise.resolve(null)}setEditingStyle(e,t){this.isEditingStyle!==e&&(this.contentElement.classList.toggle("is-editing-style",e),this.isEditingStyle=e,this.setActiveProperty(null))}setActiveProperty(e){if(this.isActivePropertyHighlighted&&n.OverlayModel.OverlayModel.hideDOMNodeHighlight(),this.isActivePropertyHighlighted=!1,!this.node())return;if(!e||e.overloaded()||e.inherited())return;const t=e.property.ownerStyle.parentRule,i=t instanceof n.CSSRule.CSSStyleRule?t.selectorText():void 0;for(const{properties:t,mode:n}of qn){if(!t.includes(e.name))continue;const s=this.node();if(s){s.domModel().overlayModel().highlightInOverlay({node:this.node(),selectorList:i},n),this.isActivePropertyHighlighted=!0;break}}}onCSSModelChanged(e){const t=e?.data&&"edit"in e.data?e.data.edit:null;if(t){for(const e of this.allSections())e.styleSheetEdited(t);this.refreshComputedStyles()}else this.userOperation||this.isEditingStyle?this.refreshComputedStyles():(this.resetCache(),this.update())}async refreshComputedStyles(){this.#W?.abort(),this.#H=new AbortController;const e=this.#H.signal,t=await this.fetchMatchedCascade(),n=this.node()?.id,i=t?.getParentLayoutNodeId(),[s,o]=await Promise.all([this.fetchComputedStylesFor(n),this.fetchComputedStylesFor(i)]);if(!e.aborted)for(const e of this.allSections())e.setComputedStyles(s),e.setParentsComputedStyles(o),e.updateAuthoringHint()}focusedSectionIndex(){let e=0;for(const t of this.sectionBlocks)for(const n of t.sections){if(n.element.hasFocus())return e;e++}return-1}continueEditingElement(e,t){const n=this.allSections()[e];if(n){const e=n.closestPropertyForEditing(t);if(!e)return void n.element.focus();e.startEditingName()}}async innerRebuildUpdate(e,t,n,i){if(this.needsForceUpdate)this.needsForceUpdate=!1;else if(this.isEditingStyle||this.userOperation)return;const s=this.focusedSectionIndex();this.linkifier.reset();const r=this.sectionBlocks.map((e=>e.sections)).flat();this.sectionBlocks=[];const a=this.node();if(this.hasMatchedStyles=null!==t&&null!==a,!this.hasMatchedStyles)return this.sectionsContainer.removeChildren(),void this.noMatchesElement.classList.remove("hidden");const l=await this.rebuildSectionsForMatchedStyleRules(t,n,i);if(e.aborted)return;this.sectionBlocks=l;const d=this.sectionBlocks.map((e=>e.sections)).flat(),c=pn.instance(),h=c.getSection();if(h){c.unbindContext();for(const[e,t]of r.entries())h===t&&e<d.length&&c.bindContext(this,d[e])}this.sectionsContainer.removeChildren();const p=document.createDocumentFragment();let u=0,m=null;for(const e of this.sectionBlocks){const t=e.titleElement();t&&p.appendChild(t);for(const t of e.sections)p.appendChild(t.element),u===s&&(m=t.element),u++}this.sectionsContainer.appendChild(p),m&&m.focus(),s>=u&&this.sectionBlocks[0].sections[0].element.focus(),this.sectionsContainerFocusChanged(),this.filterRegexInternal?this.updateFilter():this.noMatchesElement.classList.toggle("hidden",this.sectionBlocks.length>0),this.lastRevealedProperty&&(this.decorator.highlightProperty(this.lastRevealedProperty),this.lastRevealedProperty=null),this.swatchPopoverHelper().reposition(),o.userMetrics.panelLoaded("elements","DevTools.Launch.Elements"),this.dispatchEventToListeners("StylesUpdateCompleted",{hasMatchedStyles:!1})}nodeStylesUpdatedForTest(e,t){}rebuildSectionsForMatchedStyleRulesForTest(e,t,n){return this.rebuildSectionsForMatchedStyleRules(e,t,n)}async rebuildSectionsForMatchedStyleRules(e,i,s){this.idleCallbackManager&&this.idleCallbackManager.discard(),this.idleCallbackManager=new ei;const o=[new Zn(null)];let r=0,a=null,l=null,d=!1;const c=e=>{const t=e.parentRule;if(t instanceof n.CSSRule.CSSStyleRule){const e=t.layers;if((e.length||l)&&l!==e){const n=Zn.createLayerBlock(t);o.push(n),d=!0,l=e}}};rt.instance().item().setVisible(!1);const h=new Set;for(const n of e.nodeStyles()){if(t.Runtime.experiments.isEnabled("styles-pane-css-changes")&&n.parentRule){const e=n.parentRule.resourceURL();e&&!h.has(e)&&(await this.trackURLForChanges(e),h.add(e))}const l=e.isInherited(n)?e.nodeForStyle(n):null;if(l&&l!==a){a=l;const e=await Zn.createInheritedNodeBlock(a);o.push(e)}c(n);const d=o[o.length-1];d&&this.idleCallbackManager.schedule((()=>{const t=new An(this,e,n,r,i,s);r++,d.sections.push(t)}))}const p=Array.from(e.customHighlightPseudoNames()).map((t=>({highlightName:t,pseudoType:"highlight",pseudoStyles:e.customHighlightPseudoStyles(t)}))),u=[...e.pseudoTypes()].map((t=>({highlightName:null,pseudoType:t,pseudoStyles:e.pseudoStyles(t)}))),m=p.concat(u).sort(((e,t)=>"before"===e.pseudoType&&"before"!==t.pseudoType?-1:"before"!==e.pseudoType&&"before"===t.pseudoType?1:e.pseudoType<t.pseudoType?-1:e.pseudoType>t.pseudoType?1:0));for(const t of m){a=null;for(let n=0;n<t.pseudoStyles.length;++n){const d=t.pseudoStyles[n],h=e.isInherited(d)?e.nodeForStyle(d):null;if(0===n||h!==a)if(l=null,h){const e=await Zn.createInheritedPseudoTypeBlock(t.pseudoType,t.highlightName,h);o.push(e)}else{const e=Zn.createPseudoTypeBlock(t.pseudoType,t.highlightName);o.push(e)}a=h,c(d);const p=o[o.length-1];this.idleCallbackManager.schedule((()=>{const t=new Bn(this,e,d,r,i,s);r++,p.sections.push(t)}))}}for(const t of e.keyframes()){const n=Zn.createKeyframesBlock(t.name().text);for(const i of t.keyframes())this.idleCallbackManager.schedule((()=>{n.sections.push(new Un(this,e,i.style,r)),r++}));o.push(n)}const g=e.fontPaletteValuesRule();if(g){const t=Zn.createFontPaletteValuesRuleBlock(g.name().text);this.idleCallbackManager.schedule((()=>{t.sections.push(new Dn(this,e,g.style,r)),r++})),o.push(t)}for(const t of e.positionTryRules()){const n=Zn.createPositionTryBlock(t.name().text);this.idleCallbackManager.schedule((()=>{n.sections.push(new Fn(this,e,t.style,r,t.active())),r++})),o.push(n)}if(e.registeredProperties().length>0){const t=e.registeredProperties().length<=5,n=Zn.createRegisteredPropertiesBlock(t);for(const i of e.registeredProperties())this.idleCallbackManager.schedule((()=>{n.sections.push(new Rn(this,e,i.style(),r,i.propertyName(),t)),r++}));o.push(n)}return d?rt.instance().item().setVisible(!0):ot.instance().isShowing()&&Ls.instance().showToolbarPane(null,rt.instance().item()),await this.idleCallbackManager.awaitDone(),o}async createNewRuleInViaInspectorStyleSheet(){const e=this.cssModel(),t=this.node();if(!e||!t)return;this.setUserOperation(!0);const n=await e.requestViaInspectorStylesheet(t);this.setUserOperation(!1),await this.createNewRuleInStyleSheet(n)}async createNewRuleInStyleSheet(e){if(!e)return;const t=((await e.requestContent()).content||"").split("\n"),n=S.TextRange.TextRange.createFromLocation(t.length-1,t[t.length-1].length);this.sectionBlocks&&this.sectionBlocks.length>0&&this.addBlankSection(this.sectionBlocks[0].sections[0],e.id,n)}addBlankSection(e,t,n){const i=this.node(),s=new kn(this,e.matchedStyles,i?i.simpleSelector():"",t,n,e.style(),0);this.sectionsContainer.insertBefore(s.element,e.element.nextSibling);for(const t of this.sectionBlocks){const n=t.sections.indexOf(e);-1!==n&&(t.sections.splice(n+1,0,s),s.startEditingSelector())}let o=0;for(const e of this.sectionBlocks)for(const t of e.sections)t.setSectionIdx(o),o++}removeSection(e){for(const t of this.sectionBlocks){const n=t.sections.indexOf(e);-1!==n&&(t.sections.splice(n,1),e.element.remove())}}filterRegex(){return this.filterRegexInternal}updateFilter(){let e=!1,t=0;for(const n of this.sectionBlocks)t+=n.updateFilter(),e=Boolean(t)||e;this.noMatchesElement.classList.toggle("hidden",Boolean(e)),this.visibleSections=t}wasShown(){i.Context.Context.instance().setFlavor(Qn,this),super.wasShown()}willHide(){this.hideAllPopovers(),super.willHide(),i.Context.Context.instance().setFlavor(Qn,null)}hideAllPopovers(){this.swatchPopoverHelperInternal.hide(),this.imagePreviewPopover.hide(),this.activeCSSAngle&&(this.activeCSSAngle.minify(),this.activeCSSAngle=null),this.#F?.hidePopover(),this.#U?.hidePopover()}getSectionBlockByName(e){return this.sectionBlocks.find((t=>t.titleElement()?.textContent===e))}allSections(){let e=[];for(const t of this.sectionBlocks)e=e.concat(t.sections);return e}async trackURLForChanges(e){const t=this.#_.get(e);t&&E.WorkspaceDiff.workspaceDiff().unsubscribeFromDiffChange(t.uiSourceCode,t.diffChangeCallback);const n=C.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e);if(!n)return;const i=this.refreshChangedLines.bind(this,n);E.WorkspaceDiff.workspaceDiff().subscribeToDiffChange(n,i);const s={uiSourceCode:n,changedLines:new Set,diffChangeCallback:i};this.#_.set(e,s),await this.refreshChangedLines(s.uiSourceCode)}isPropertyChanged(e){const t=e.ownerStyle.parentRule?.resourceURL();if(!t)return!1;const n=this.#_.get(t);if(!n)return!1;const{changedLines:i,formattedCurrentMapping:s}=n,o=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(e,!0);if(!o)return!1;if(!s)return i.has(o.lineNumber+1);const r=s.originalToFormatted(o.lineNumber,o.columnNumber)[0];return i.has(r+1)}updateChangeStatus(){if(!this.#V)return;let e=!1;for(const t of this.#_.values())if(t.changedLines.size>0){e=!0;break}this.#V.element.classList.toggle("hidden",!e)}async refreshChangedLines(e){const t=this.#_.get(e.url());if(!t)return;const n=await E.WorkspaceDiff.workspaceDiff().requestDiff(e,{shouldFormatDiff:!0}),i=new Set;if(t.changedLines=i,!n)return;const{diff:s,formattedCurrentMapping:o}=n,{rows:r}=v.DiffView.buildDiffRows(s);for(const e of r)"addition"===e.type&&i.add(e.currentLineNumber);t.formattedCurrentMapping=o}async getFormattedChanges(){let e="";for(const[t,{uiSourceCode:n}]of this.#_){const i=await E.WorkspaceDiff.workspaceDiff().requestDiff(n,{shouldFormatDiff:!0});if(!i||i?.diff.length<2)continue;const s=await b.formatCSSChangesFromDiff(i.diff);s.length>0&&(e+=`/* ${si(t)} */\n\n${s}\n\n`)}return e}clipboardCopy(e){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleCopied)}createStylesSidebarToolbar(){const e=this.contentElement.createChild("div","styles-sidebar-pane-toolbar-container"),t=e.createChild("div","hbox styles-sidebar-pane-toolbar"),n=new i.Toolbar.Toolbar("styles-pane-toolbar",t),s=new i.Toolbar.ToolbarFilter(void 0,1,1,void 0,void 0,!1);s.addEventListener("TextChanged",this.onFilterChanged,this),n.appendToolbarItem(s),n.makeToggledGray(),n.appendItemsAtLocation("styles-sidebarpane-toolbar"),this.toolbar=n,i.ActionRegistry.ActionRegistry.instance().hasAction("freestyler.style-tab-context")&&n.appendToolbarItem(i.Toolbar.Toolbar.createActionButtonForId("freestyler.style-tab-context"));return e.createChild("div","styles-sidebar-toolbar-pane-container").createChild("div","styles-sidebar-toolbar-pane")}showToolbarPane(e,t){this.pendingWidgetToggle&&this.pendingWidgetToggle.setToggled(!1),this.pendingWidgetToggle=t,this.animatedToolbarPane?this.pendingWidget=e:this.startToolbarPaneAnimation(e),e&&t&&t.setToggled(!0)}appendToolbarItem(e){this.toolbar&&this.toolbar.appendToolbarItem(e)}startToolbarPaneAnimation(e){if(e===this.currentToolbarPane)return;if(e&&this.currentToolbarPane)return this.currentToolbarPane.detach(),e.show(this.toolbarPaneElement),this.currentToolbarPane=e,void this.currentToolbarPane.focus();this.animatedToolbarPane=e,this.currentToolbarPane?this.toolbarPaneElement.style.animationName="styles-element-state-pane-slideout":e&&(this.toolbarPaneElement.style.animationName="styles-element-state-pane-slidein"),e&&e.show(this.toolbarPaneElement);const t=function(){this.toolbarPaneElement.style.removeProperty("animation-name"),this.toolbarPaneElement.removeEventListener("animationend",t,!1),this.currentToolbarPane&&this.currentToolbarPane.detach();this.currentToolbarPane=this.animatedToolbarPane,this.currentToolbarPane&&this.currentToolbarPane.focus();this.animatedToolbarPane=null,this.pendingWidget&&(this.startToolbarPaneAnimation(this.pendingWidget),this.pendingWidget=null)}.bind(this);this.toolbarPaneElement.addEventListener("animationend",t,!1)}createRenderingShortcuts(){const t=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-color-scheme"),n=e.Settings.Settings.instance().moduleSetting("emulate-auto-dark-mode"),o=(e,t)=>`${e?"✓ ":""}${t}`,r=new i.Toolbar.ToolbarToggle(Gn($n.toggleRenderingEmulations),"brush","brush-filled");return r.element.setAttribute("jslog",`${s.dropDown("rendering-emulations").track({click:!0})}`),r.element.addEventListener("click",(e=>{const s=r.element.getBoundingClientRect(),a=new i.ContextMenu.ContextMenu(e,{x:s.left,y:s.bottom}),l=t.get(),d="light"===l,c="dark"===l,h=n.get(),p=o(d,"prefers-color-scheme: light"),u=o(c,"prefers-color-scheme: dark"),m=o(h,Gn($n.automaticDarkMode));a.defaultSection().appendItem(p,(()=>{n.set(!1),t.set(d?"":"light"),r.setToggled(Boolean(t.get()))}),{jslogContext:"prefer-light-color-scheme"}),a.defaultSection().appendItem(u,(()=>{n.set(!1),t.set(c?"":"dark"),r.setToggled(Boolean(t.get()))}),{jslogContext:"prefer-dark-color-scheme"}),a.defaultSection().appendItem(m,(()=>{n.set(!h),r.setToggled(Boolean(t.get()))}),{jslogContext:"emulate-auto-dark-mode"}),a.show(),e.stopPropagation()}),{capture:!0}),r}createCopyAllChangesButton(){const e=new i.Toolbar.ToolbarButton(Gn($n.copyAllCSSChanges),"copy");let t;return e.element.setAttribute("data-content",Gn($n.copiedToClipboard)),e.addEventListener("Click",(async()=>{const n=await this.getFormattedChanges();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(n),o.userMetrics.styleTextCopied(2),t&&(clearTimeout(t),t=void 0),e.element.classList.add("copied-to-clipboard"),t=window.setTimeout((()=>{e.element.classList.remove("copied-to-clipboard"),t=void 0}),2e3)})),e}}const Jn=23;class Zn{titleElementInternal;sections;#z=!1;#$;constructor(e,t,n){this.titleElementInternal=e,this.sections=[],this.#z=n??!1,t&&e instanceof HTMLElement&&(this.#$=w.Icon.create(this.#z?"triangle-down":"triangle-right","section-block-expand-icon"),e.classList.toggle("empty-section",!this.#z),i.ARIAUtils.setExpanded(e,this.#z),e.appendChild(this.#$),e.tabIndex=-1,e.addEventListener("click",(()=>this.expand(!this.#z)),!1))}expand(e){this.titleElementInternal&&this.#$&&(this.titleElementInternal.classList.toggle("empty-section",!e),this.#$.name=e?"triangle-down":"triangle-right",i.ARIAUtils.setExpanded(this.titleElementInternal,e),this.#z=e,this.sections.forEach((t=>t.element.classList.toggle("hidden",!e))))}static createPseudoTypeBlock(e,t){const n=document.createElement("div");n.className="sidebar-separator",n.setAttribute("jslog",`${s.sectionHeader("pseudotype")}`);const i=`${e}${t?`(${t})`:""}`;return n.textContent=Gn($n.pseudoSElement,{PH1:i}),new Zn(n)}static async createInheritedPseudoTypeBlock(t,n,o){const r=document.createElement("div");r.className="sidebar-separator",r.setAttribute("jslog",`${s.sectionHeader("inherited-pseudotype")}`);const a=`${t}${n?`(${n})`:""}`;i.UIUtils.createTextChild(r,Gn($n.inheritedFromSPseudoOf,{PH1:a}));const l=await e.Linkifier.Linkifier.linkify(o,{preventKeyboardFocus:!0,tooltip:void 0});return r.appendChild(l),new Zn(r)}static createRegisteredPropertiesBlock(e){const t=document.createElement("div"),n=new Zn(t,!0,e);return t.className="sidebar-separator",t.appendChild(document.createTextNode(Yn)),n}static createKeyframesBlock(e){const t=document.createElement("div");return t.className="sidebar-separator",t.setAttribute("jslog",`${s.sectionHeader("keyframes")}`),t.textContent=`@keyframes ${e}`,new Zn(t)}static createFontPaletteValuesRuleBlock(e){const t=document.createElement("div");return t.className="sidebar-separator",t.textContent=`@font-palette-values ${e}`,new Zn(t)}static createPositionTryBlock(e){const t=document.createElement("div");return t.className="sidebar-separator",t.setAttribute("jslog",`${s.sectionHeader("position-try")}`),t.textContent=`@position-try ${e}`,new Zn(t)}static async createInheritedNodeBlock(t){const n=document.createElement("div");n.className="sidebar-separator",n.setAttribute("jslog",`${s.sectionHeader("inherited")}`),i.UIUtils.createTextChild(n,Gn($n.inheritedFroms));const o=await e.Linkifier.Linkifier.linkify(t,{preventKeyboardFocus:!0,tooltip:void 0});return n.appendChild(o),new Zn(n)}static createLayerBlock(e){const t=document.createElement("div");t.className="sidebar-separator layer-separator",t.setAttribute("jslog",`${s.sectionHeader("layer")}`),i.UIUtils.createTextChild(t.createChild("div"),Gn($n.layer));const o=e.layers;if(!o.length&&"user-agent"===e.origin){const n="user-agent"===e.origin?" user agent stylesheet":" implicit outer layer";return i.UIUtils.createTextChild(t.createChild("div"),n),new Zn(t)}const r=t.createChild("button");r.className="link",r.title=Gn($n.clickToRevealLayer);const a=o.map((e=>n.CSSModel.CSSModel.readableLayerName(e.text))).join(".");return r.textContent=a,r.onclick=()=>ot.instance().revealLayer(a),new Zn(t)}updateFilter(){let e=!1,t=0;for(const n of this.sections)t+=n.updateFilter()?1:0,e=n.updateFilter()||e;return this.titleElementInternal&&this.titleElementInternal.classList.toggle("hidden",!e),t}titleElement(){return this.titleElementInternal}}class ei{discarded;promises;queue;constructor(){this.discarded=!1,this.promises=[],this.queue=[]}discard(){this.discarded=!0}schedule(e){if(this.discarded)return;const t=new Promise(((t,n)=>{this.queue.push({fn:e,resolve:t,reject:n})}));this.promises.push(t),this.scheduleIdleCallback(100)}scheduleIdleCallback(e){window.requestIdleCallback((()=>{const e=this.queue.shift();l(e);try{this.discarded||e.fn(),e.resolve()}catch(t){e.reject(t)}}),{timeout:e})}awaitDone(){return Promise.all(this.promises)}}function ti(e){return`'${e.replaceAll("'","\\'")}'`}class ni extends i.TextPrompt.TextPrompt{isColorAware;cssCompletions;selectedNodeComputedStyles;parentNodeComputedStyles;treeElement;isEditingName;cssVariables;constructor(e,t){super(),this.initialize(this.buildPropertyCompletions.bind(this),i.UIUtils.StyleValueDelimiters);const s=n.CSSMetadata.cssMetadata();this.isColorAware=n.CSSMetadata.cssMetadata().isColorAwareProperty(e.property.name),this.cssCompletions=[];const r=e.node();if(t)this.cssCompletions=s.allProperties(),r&&!r.isSVGNode()&&(this.cssCompletions=this.cssCompletions.filter((e=>!s.isSVGProperty(e))));else if(this.cssCompletions=s.getPropertyValues(e.property.name),r&&s.isFontFamilyProperty(e.property.name)){const e=r.domModel().cssModel().fontFaces().map((e=>ti(e.getFontFamily())));this.cssCompletions.unshift(...e)}if(this.selectedNodeComputedStyles=null,this.parentNodeComputedStyles=null,this.treeElement=e,this.isEditingName=t,this.cssVariables=e.matchedStyles().availableCSSVariables(e.property.ownerStyle),this.cssVariables.length<1e3?this.cssVariables.sort(a.StringUtilities.naturalOrderComparator):this.cssVariables.sort(),!t&&(this.disableDefaultSuggestionForEmptyInput(),e&&e.valueElement)){const t=e.valueElement.textContent,n=o.Platform.isMac()?"Cmd":"Ctrl",i=o.Platform.isMac()?"Option":"Alt";null!==t&&(t.match(/#[\da-f]{3,6}$/i)?this.setTitle(Gn($n.incrementdecrementWithMousewheelOne,{PH1:n,PH2:i})):t.match(/\d+/)&&this.setTitle(Gn($n.incrementdecrementWithMousewheelHundred,{PH1:n,PH2:i})))}}onKeyDown(e){const t=e;switch(t.key){case"ArrowUp":case"ArrowDown":case"PageUp":case"PageDown":if(!this.isSuggestBoxVisible()&&this.handleNameOrValueUpDown(t))return void t.preventDefault();break;case"Enter":if(t.shiftKey)return;return this.tabKeyPressed(),void t.preventDefault();case" ":if(this.isEditingName)return this.tabKeyPressed(),void t.preventDefault()}super.onKeyDown(t)}onMouseWheel(e){this.handleNameOrValueUpDown(e)?e.consume(!0):super.onMouseWheel(e)}tabKeyPressed(){return this.acceptAutoComplete(),!1}handleNameOrValueUpDown(e){return!(this.isEditingName||!this.treeElement.valueElement||!i.UIUtils.handleElementValueModifications(e,this.treeElement.valueElement,function(e,t){this.treeElement.nameElement&&this.treeElement.valueElement&&this.treeElement.applyStyleText(this.treeElement.nameElement.textContent+": "+this.treeElement.valueElement.textContent,!1)}.bind(this),this.isValueSuggestion.bind(this),function(e,t,i){return 0===t||i.length||!n.CSSMetadata.cssMetadata().isLengthProperty(this.treeElement.property.name)||this.treeElement.property.value.toLowerCase().startsWith("calc(")||(i="px"),e+t+i}.bind(this)))}isValueSuggestion(e){return!!e&&(e=e.toLowerCase(),-1!==this.cssCompletions.indexOf(e)||e.startsWith("--"))}async buildPropertyCompletions(t,i,s){const o=i.toLowerCase(),r=!this.isEditingName&&t.trim().endsWith("var(");if(!i&&!s&&!r&&(this.isEditingName||t))return Promise.resolve([]);const a=[],l=[];r||(this.cssCompletions.forEach((e=>m.call(this,e,!1))),this.isEditingName&&n.CSSMetadata.cssMetadata().aliasesFor().forEach(((e,t)=>{if(0!==t.toLowerCase().indexOf(o))return;const i={text:t,priority:n.CSSMetadata.cssMetadata().propertyUsageWeight(t),isCSSVariableColor:!1},s={text:e,priority:n.CSSMetadata.cssMetadata().propertyUsageWeight(e),subtitle:`= ${t}`,isCSSVariableColor:!1};a.push(i,s)})));const d=this.treeElement.node();if(this.isEditingName&&d){n.CSSMetadata.cssMetadata().nameValuePresets(d.isSVGNode()).forEach((e=>m.call(this,e,!1,!0)))}(this.isEditingName||r)&&this.cssVariables.forEach((e=>m.call(this,e,!0)));const c=a.concat(l);!this.isEditingName&&!c.length&&i.length>1&&"!important".startsWith(o)&&c.push({text:"!important",title:void 0,subtitle:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0});const h=i.replace("-","");if(h&&h===h.toUpperCase())for(let e=0;e<c.length;++e)c[e].text.startsWith("--")||(c[e].text=c[e].text.toUpperCase());for(const e of c){if(r){e.title=e.text,e.text+=")";continue}const t=n.CSSMetadata.cssMetadata().getValuePreset(this.treeElement.name,e.text);!this.isEditingName&&t&&(e.title=e.text,e.text=t.text,e.selectionRange={startColumn:t.startColumn,endColumn:t.endColumn})}const u=async()=>{if(!d||this.selectedNodeComputedStyles)return;this.selectedNodeComputedStyles=await d.domModel().cssModel().getComputedStyle(d.id);const e=d.parentNode;e&&(this.parentNodeComputedStyles=await e.domModel().cssModel().getComputedStyle(e.id))};for(const e of c){await u();const t=p.CSSPropertyIconResolver.findIcon(this.isEditingName?e.text:`${this.treeElement.property.name}: ${e.text}`,this.selectedNodeComputedStyles,this.parentNodeComputedStyles);if(!t)continue;const n=new w.Icon.Icon,i="12.5px",s="12.5px";n.data={iconName:t.iconName,width:i,height:s,color:"var(--icon-default)"},n.style.transform=`rotate(${t.rotate}deg) scale(${1.1*t.scaleX}, ${1.1*t.scaleY})`,n.style.maxHeight=s,n.style.maxWidth=i,e.iconElement=n}return this.isColorAware&&!this.isEditingName&&c.sort(((e,t)=>e.isCSSVariableColor&&t.isCSSVariableColor?0:e.isCSSVariableColor?-1:1)),Promise.resolve(c);function m(t,i,s){const r=t.toLowerCase().indexOf(o),d={text:t,title:void 0,subtitle:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0,isCSSVariableColor:!1};if(i){const n=this.treeElement.matchedStyles().computeCSSVariable(this.treeElement.property.ownerStyle,t);if(n){const t=e.Color.parse(n.value);t?(d.subtitleRenderer=g.bind(null,t),d.isCSSVariableColor=!0):d.subtitleRenderer=f.bind(null,n.value)}}s&&(d.hideGhostText=!0),0===r?(d.priority=this.isEditingName?n.CSSMetadata.cssMetadata().propertyUsageWeight(t):1,a.push(d)):r>-1&&l.push(d)}function g(e){const t=new y.ColorSwatch.ColorSwatch;return t.renderColor(e),t.style.pointerEvents="none",t}function f(e){const t=document.createElement("span");return t.className="suggestion-subtitle",t.textContent=`${e}`,t.style.maxWidth="100px",t.title=`${e}`,t}}}function ii(e){return e.replace(/(?<!\\)\\(?:([a-fA-F0-9]{1,6})|(.))[\n\t\x20]?/gs,((e,t,n)=>{if(n)return n;const i=parseInt(t,16);return 55296<=i&&i<=57343||0===i||i>1114111?"�":String.fromCodePoint(i)}))}function si(e){const t=new URL(e);return t.search?`${t.origin}${t.pathname}${t.search.replaceAll("*/","*%2F")}${t.hash}`:t.toString()}let oi;class ri{button;constructor(){this.button=i.Toolbar.Toolbar.createActionButtonForId("elements.new-style-rule");const e=w.Icon.create("triangle-bottom-right","long-click-glyph");function t(){let e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);e=e?e.enclosingElementOrSelf():null,this.button.setEnabled(Boolean(e))}this.button.element.appendChild(e),new i.UIUtils.LongClickController(this.button.element,this.longClicked.bind(this)),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,t.bind(this)),t.call(this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return oi&&!t||(oi=new ri),oi}longClicked(e){Qn.instance().onAddButtonLongClick(e)}item(){return this.button}}var ai=Object.freeze({__proto__:null,REGISTERED_PROPERTY_SECTION_NAME:Yn,StylesSidebarPane:Qn,SectionBlock:Zn,IdleCallbackManager:ei,quoteFamilyName:ti,CSSPropertyPrompt:ni,unescapeCssString:ii,escapeUrlAsCssComment:si,ActionDelegate:class{handleAction(e,t){return"elements.new-style-rule"===t&&(o.userMetrics.actionTaken(o.UserMetrics.Action.NewStyleRuleAdded),Qn.instance().createNewRuleInViaInspectorStyleSheet(),!0)}},ButtonProvider:ri});const li={cssPropertyName:"`CSS` property name: {PH1}",cssPropertyValue:"`CSS` property value: {PH1}"},di=r.i18n.registerUIStrings("panels/elements/PropertyRenderer.ts",li),ci=r.i18n.getLocalizedString.bind(void 0,di);function hi(e,t){const i=[...e];return n.CSSPropertyParser.requiresSpace(e,t)&&i.push(document.createTextNode(" ")),i.push(...t),i}class pi{ast;renderers;matchedResult;cssControls;options;constructor(e,t,n,i,s={readonly:!1}){this.ast=e,this.renderers=t,this.matchedResult=n,this.cssControls=i,this.options=s}addControl(e,t){if(this.cssControls){const n=this.cssControls.get(e);n?n.push(t):this.cssControls.set(e,[t])}}}class ui extends n.CSSPropertyParser.TreeWalker{#K;#G=[];#g;constructor(e,t,n,i,s){super(e),this.#K=n,this.#g=new pi(this.ast,t,this.#K,i,s)}static render(e,t){if(!Array.isArray(e))return this.render([e],t);const i=new n.CSSPropertyParser.CSSControlMap;return{nodes:e.map((e=>this.walkExcludingSuccessors(t.ast.subtree(e),t.renderers,t.matchedResult,i,t.options))).map((e=>e.#G)).reduce(hi),cssControls:i}}static renderInto(e,t,i){const{nodes:s,cssControls:o}=this.render(e,t);return i.lastChild&&n.CSSPropertyParser.requiresSpace([i.lastChild],s)&&i.appendChild(document.createTextNode(" ")),s.map((e=>i.appendChild(e))),{nodes:s,cssControls:o}}renderedMatchForTest(e,t){}enter({node:e}){const t=this.#K.getMatch(e),i=t&&this.#g.renderers.get(t.constructor);if(i||t instanceof n.CSSPropertyParser.TextMatch){const e=i?i.render(t,this.#g):t.render();return this.renderedMatchForTest(e,t),this.#G=hi(this.#G,e),!1}return!0}static renderNameElement(e){const t=document.createElement("span");return t.setAttribute("jslog",`${s.key().track({change:!0,keydown:"ArrowLeft|ArrowUp|PageUp|Home|PageDown|ArrowRight|ArrowDown|End|Space|Tab|Enter|Escape"})}`),i.ARIAUtils.setLabel(t,ci(li.cssPropertyName,{PH1:e})),t.className="webkit-css-property",t.textContent=e,t.normalize(),t}static renderValueElement(e,t,o){const r=document.createElement("span");r.setAttribute("jslog",`${s.value().track({change:!0,keydown:"ArrowLeft|ArrowUp|PageUp|Home|PageDown|ArrowRight|ArrowDown|End|Space|Tab|Enter|Escape"})}`),i.ARIAUtils.setLabel(r,ci(li.cssPropertyValue,{PH1:t})),r.className="value";const a=n.CSSPropertyParser.tokenizeDeclaration(e,t);if(!a)return r.appendChild(document.createTextNode(t)),r;const l=[],d=new Map;for(const e of o){const t=e.matcher();l.push(t),d.set(t.matchType,e)}const c=n.CSSPropertyParser.BottomUpTreeMatching.walk(a,l);a.trailingNodes.forEach((e=>c.matchText(e)));const h=new pi(a,d,c);return ui.render([a.tree,...a.trailingNodes],h).nodes.forEach((e=>r.appendChild(e))),r.normalize(),r}}class mi{rule;node;constructor(e,t){this.rule=e,this.node=t}render(t){const n=ii(t.url),s=document.createDocumentFragment();i.UIUtils.createTextChild(s,"url(");let o=null;this.rule&&this.rule.resourceURL()?o=e.ParsedURL.ParsedURL.completeURL(this.rule.resourceURL(),n):this.node&&(o=this.node.resolveURL(n));const r=ie.setImageUrl(f.Linkifier.Linkifier.linkifyURL(o||n,{text:n,preventClick:!1,bypassURLTrimming:!0,showColumnNumber:!1,inlineFrameIndex:0}),o||n);return s.appendChild(r),i.UIUtils.createTextChild(s,")"),[s]}matcher(){return new Ee}}class gi{render(e){const t=document.createElement("span");return t.innerText=e.text,i.Tooltip.Tooltip.install(t,ii(e.text)),[t]}matcher(){return new Ae}}var yi=Object.freeze({__proto__:null,RenderingContext:pi,Renderer:ui,URLRenderer:mi,StringRenderer:gi});const fi={showAll:"Show all",group:"Group",noMatchingProperty:"No matching property",navigateToSelectorSource:"Navigate to selector source",navigateToStyle:"Navigate to style"},Si=r.i18n.registerUIStrings("panels/elements/ComputedStyleWidget.ts",fi),Ci=r.i18n.getLocalizedString.bind(void 0,Si),Ei=new Map;const bi=(e,t,n,i,s,o,r)=>{const{name:a,value:l}=function(e,t,n){const i=t+":"+n,s=Ei.get(i);if(s)return s;const o=ui.renderNameElement(t);o.slot="name";const r=ui.renderValueElement(t,n,[new vi,new mi(null,e),new gi]);return r.slot="value",Ei.set(i,{name:o,value:r}),{name:o,value:r}}(e,t,n);return u.html`<${p.ComputedStyleProperty.ComputedStyleProperty.litTagName}
        .traceable=${i}
        .inherited=${s}
        @oncontextmenu=${r}
        @onnavigatetosource=${e=>{o&&wi(o,e)}}>
          ${a}
          ${l}
      </${p.ComputedStyleProperty.ComputedStyleProperty.litTagName}>`};class vi{render(t,n){const i=e.Color.parse(t.text);if(!i)return[document.createTextNode(t.text)];const s=new y.ColorSwatch.ColorSwatch;s.setReadonly(!0),s.renderColor(i);const o=document.createElement("span");return o.textContent=t.text,s.append(o),s.addEventListener(y.ColorSwatch.ColorChangedEvent.eventName,(e=>{const{data:{color:t}}=e;o.textContent=t.getAuthoredText()??t.asString()})),n.addControl("color",s),[s]}matcher(){return new xe}}const wi=(t,n)=>{n&&(e.Revealer.reveal(t),n.consume(!0))},xi=(e,t)=>{if(e.startsWith("--")!==t.startsWith("--"))return e.startsWith("--")?1:-1;if(e.startsWith("-webkit")!==t.startsWith("-webkit"))return e.startsWith("-webkit")?1:-1;const i=n.CSSMetadata.cssMetadata().canonicalPropertyName(e),s=n.CSSMetadata.cssMetadata().canonicalPropertyName(t);return a.StringUtilities.compare(i,s)};class Ti extends i.ThrottledWidget.ThrottledWidget{computedStyleModel;showInheritedComputedStylePropertiesSetting;groupComputedStylesSetting;input;filterRegex;noMatchesElement;linkifier;imagePreviewPopover;#Y=new h.TreeOutline.TreeOutline;#q;constructor(){super(!0),this.contentElement.classList.add("styles-sidebar-computed-style-widget"),this.computedStyleModel=new Z,this.computedStyleModel.addEventListener("ComputedStyleChanged",this.update,this),this.showInheritedComputedStylePropertiesSetting=e.Settings.Settings.instance().createSetting("show-inherited-computed-style-properties",!1),this.showInheritedComputedStylePropertiesSetting.addChangeListener(this.update.bind(this)),this.groupComputedStylesSetting=e.Settings.Settings.instance().createSetting("group-computed-styles",!1),this.groupComputedStylesSetting.addChangeListener((()=>{this.update()}));const t=this.contentElement.createChild("div","hbox styles-sidebar-pane-toolbar"),n=new i.Toolbar.Toolbar("styles-pane-toolbar",t),s=new i.Toolbar.ToolbarFilter(void 0,1,1,void 0,void 0,!1);s.addEventListener("TextChanged",this.onFilterChanged,this),n.appendToolbarItem(s),this.input=s,this.filterRegex=null,n.appendToolbarItem(new i.Toolbar.ToolbarSettingCheckbox(this.showInheritedComputedStylePropertiesSetting,void 0,Ci(fi.showAll))),n.appendToolbarItem(new i.Toolbar.ToolbarSettingCheckbox(this.groupComputedStylesSetting,void 0,Ci(fi.group))),this.noMatchesElement=this.contentElement.createChild("div","gray-info-message"),this.noMatchesElement.textContent=Ci(fi.noMatchingProperty),this.contentElement.appendChild(this.#Y),this.linkifier=new f.Linkifier.Linkifier(Mi),this.imagePreviewPopover=new ie(this.contentElement,(e=>{const t=e.composedPath()[0];return t instanceof Element?t:null}),(()=>this.computedStyleModel.node()));new de(this.computedStyleModel).show(this.contentElement)}onResize(){const e=this.contentElement.offsetWidth<260;this.#Y.classList.toggle("computed-narrow",e)}wasShown(){super.wasShown(),this.registerCSSFiles([ne])}async doUpdate(){const[e,t]=await Promise.all([this.computedStyleModel.fetchComputedStyle(),this.fetchMatchedCascade()]);if(!e||!t)return void this.noMatchesElement.classList.remove("hidden");this.groupComputedStylesSetting.get()?await this.rebuildGroupedList(e,t):await this.rebuildAlphabeticalList(e,t)}async fetchMatchedCascade(){const e=this.computedStyleModel.node();if(!e||!this.computedStyleModel.cssModel())return null;const t=this.computedStyleModel.cssModel();return t?t.cachedMatchedCascadeForNode(e).then(function(e){return e&&e.node()===this.computedStyleModel.node()?e:null}.bind(this)):null}async rebuildAlphabeticalList(e,t){this.imagePreviewPopover.hide(),this.linkifier.reset();if(!this.computedStyleModel.cssModel())return;const i=[...e.computedStyle.keys()];i.sort(xi);const s=e.node,o=this.computePropertyTraces(t),r=this.computeNonInheritedProperties(t),a=this.showInheritedComputedStylePropertiesSetting.get(),l=[];for(const t of i){const i=e.computedStyle.get(t)||"",s=n.CSSMetadata.cssMetadata().canonicalPropertyName(t),d=!r.has(s);(a||!d||Ni.has(t))&&(!a&&t.startsWith("--")||t!==s&&i===e.computedStyle.get(s)||l.push(this.buildTreeNode(o,t,i,d)))}const d=this.createTreeNodeRenderer(o,s,t);this.#q={tree:l,compact:!0,defaultRenderer:d},this.filterAlphabeticalList()}async rebuildGroupedList(e,t){this.imagePreviewPopover.hide(),this.linkifier.reset();const i=this.computedStyleModel.cssModel();if(!e||!t||!i)return void this.noMatchesElement.classList.remove("hidden");const s=e.node,o=this.computePropertyTraces(t),r=this.computeNonInheritedProperties(t),a=this.showInheritedComputedStylePropertiesSetting.get(),l=new Map,d=[];for(const[t,i]of e.computedStyle){const s=n.CSSMetadata.cssMetadata().canonicalPropertyName(t),o=!r.has(s);if(!a&&o&&!Ni.has(t))continue;if(!a&&t.startsWith("--"))continue;if(t!==s&&i===e.computedStyle.get(s))continue;const d=Xe(t);for(const e of d)l.has(e)||l.set(e,[]),l.get(e)?.push(t)}this.#Y.removeChildren();for(const t of Ke){const i=l.get(t);if(i&&i.length>0){const s=[];for(const t of i){const i=e.computedStyle.get(t)||"",a=n.CSSMetadata.cssMetadata().canonicalPropertyName(t),l=!r.has(a);s.push(this.buildTreeNode(o,t,i,l))}d.push({id:t,treeNodeData:{tag:"category",name:t},children:async()=>s})}}const c=this.createTreeNodeRenderer(o,s,t);return this.#q={tree:d,compact:!0,defaultRenderer:c},this.filterGroupLists()}buildTraceNode(e){const t=e.ownerStyle.parentRule;return{treeNodeData:{tag:"traceElement",property:e,rule:t},id:(t?.origin||"")+": "+e.ownerStyle.styleSheetId+(e.range||e.name)}}createTreeNodeRenderer(e,t,n){return i=>{const s=i.treeNodeData;if("property"===s.tag){const i=e.get(s.propertyName),o=i?.find((e=>"Active"===n.propertyState(e)));return bi(t,s.propertyName,s.propertyValue,e.has(s.propertyName),s.inherited,o,(e=>{o&&this.handleContextMenuEvent(n,o,e)}))}if("traceElement"===s.tag){const e="Overloaded"===n.propertyState(s.property),i=((e,t,n,i,s)=>{const o=new p.ComputedStyleTrace.ComputedStyleTrace,r=ui.renderValueElement(t.name,t.value,[new vi,new mi(null,e),new gi]);r.slot="trace-value",o.appendChild(r);const a=t.ownerStyle.parentRule;let l;return a&&(l=An.createRuleOriginNode(i,s,a)),o.data={selector:a?a.selectorText():"element.style",active:!n,onNavigateToSource:wi.bind(null,t),ruleOriginNode:l},o})(t,s.property,e,n,this.linkifier);return i.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this,n,s.property)),u.html`${i}`}return u.html`<span style="cursor: text; color: var(--sys-color-token-subtle);">${s.name}</span>`}}buildTreeNode(e,t,n,i){const s={tag:"property",propertyName:t,propertyValue:n,inherited:i},o=e.get(t);return o?{treeNodeData:s,jslogContext:t,id:t,children:async()=>o.map(this.buildTraceNode)}:{treeNodeData:s,jslogContext:t,id:t}}handleContextMenuEvent(t,n,s){const o=new i.ContextMenu.ContextMenu(s),r=n.ownerStyle.parentRule;if(r){const e=r.styleSheetId?t.cssModel().styleSheetHeaderForId(r.styleSheetId):null;e&&!e.isAnonymousInlineStyleSheet()&&o.defaultSection().appendItem(Ci(fi.navigateToSelectorSource),(()=>{An.tryNavigateToRuleLocation(t,r)}),{jslogContext:"navigate-to-selector-source"})}o.defaultSection().appendItem(Ci(fi.navigateToStyle),(()=>e.Revealer.reveal(n)),{jslogContext:"navigate-to-style"}),o.show()}computePropertyTraces(e){const t=new Map;for(const n of e.nodeStyles()){const i=n.allProperties();for(const n of i)n.activeInStyle()&&e.propertyState(n)&&(t.has(n.name)||t.set(n.name,[]),t.get(n.name).push(n))}return t}computeNonInheritedProperties(e){const t=new Set;for(const i of e.nodeStyles())for(const s of i.allProperties())e.propertyState(s)&&t.add(n.CSSMetadata.cssMetadata().canonicalPropertyName(s.name));return t}onFilterChanged(e){this.filterComputedStyles(e.data?new RegExp(a.StringUtilities.escapeForRegExp(e.data),"i"):null)}async filterComputedStyles(e){return this.filterRegex=e,this.groupComputedStylesSetting.get()?this.filterGroupLists():this.filterAlphabeticalList()}nodeFilter(e){const t=this.filterRegex,n=e.treeNodeData;if("property"===n.tag){return!t||t.test(n.propertyName)||t.test(n.propertyValue)}return!0}filterAlphabeticalList(){if(!this.#q)return;const e=this.#q.tree.filter(this.nodeFilter.bind(this));this.#Y.data={tree:e,defaultRenderer:this.#q.defaultRenderer,compact:this.#q.compact},this.noMatchesElement.classList.toggle("hidden",Boolean(e.length))}async filterGroupLists(){if(!this.#q)return;const e=[];for(const t of this.#q.tree){const n=t.treeNodeData;if("category"!==n.tag||!t.children)continue;const i=(await t.children()).filter(this.nodeFilter.bind(this));i.length&&e.push({id:n.name,treeNodeData:{tag:"category",name:n.name},children:async()=>i})}this.#Y.data={tree:e,defaultRenderer:this.#q.defaultRenderer,compact:this.#q.compact},await this.#Y.expandRecursively(0),this.noMatchesElement.classList.toggle("hidden",Boolean(e.length))}}const Mi=30,Ni=new Set(["display","height","width"]);var Ii=Object.freeze({__proto__:null,ComputedStyleWidget:Ti});const Pi=new CSSStyleSheet;Pi.replaceSync("#main-content{position:relative;flex:1 1}#elements-content{overflow:auto;padding:2px 0 0;height:100%}.style-panes-wrapper{overflow:hidden scroll;background-color:var(--sys-color-cdt-base-container);& > div:not(:last-child){border-bottom:1px solid var(--sys-color-divider)}}#elements-content:not(.elements-wrap) > div{display:inline-block;min-width:100%}#elements-crumbs{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);overflow:hidden;width:100%}devtools-adorner-settings-pane{margin-bottom:10px;border-bottom:1px solid var(--sys-color-divider);overflow:auto}devtools-tree-outline{overflow:auto}.axtree-button{position:absolute;top:16px;right:20px;background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;z-index:1;border:1px solid var(--sys-color-neutral-outline);border-radius:3px}\n/*# sourceURL=elementsPanel.css */\n");const Oi=function(e,t){if(e.nodeType()!==Node.ELEMENT_NODE)return"";const n=[];let i=e;for(;i;){const s=ki(i,Boolean(t),i===e);if(!s)break;if(n.push(s),s.optimized)break;i=i.parentNode}return n.reverse(),n.join(" > ")},Li=function(e){let t=e;for(;t;){const e=t.ancestorShadowRoot();if(e&&e.shadowRootType()!==n.DOMModel.DOMNode.ShadowRootTypes.Open)return!1;t=t.ancestorShadowHost()}return!0},Ai=function(e,t){if(e.nodeType()!==Node.ELEMENT_NODE)return"";const n=[];let i=e;for(;i;)n.push(Oi(i,t)),i=i.ancestorShadowHost();n.reverse();let s="";for(let e=0;e<n.length;++e){const t=JSON.stringify(n[e]);s+=e?`.shadowRoot.querySelector(${t})`:`document.querySelector(${t})`}return s},ki=function(e,t,n){if(e.nodeType()!==Node.ELEMENT_NODE)return null;const i=e.getAttribute("id");if(t){if(i)return new Ui(a(i),!0);const t=e.nodeName().toLowerCase();if("body"===t||"head"===t||"html"===t)return new Ui(e.nodeNameInCorrectCase(),!0)}const s=e.nodeNameInCorrectCase();if(i)return new Ui(s+a(i),!0);const o=e.parentNode;if(!o||o.nodeType()===Node.DOCUMENT_NODE)return new Ui(s,!0);function r(e){const t=e.getAttribute("class");return t?t.split(/\s+/g).filter(Boolean).map((function(e){return"$"+e})):[]}function a(e){return"#"+CSS.escape(e)}const l=r(e);let d=!1,c=!1,h=-1,p=-1;const u=o.children();for(let t=0;u&&(-1===h||!c)&&t<u.length;++t){const n=u[t];if(n.nodeType()!==Node.ELEMENT_NODE)continue;if(p+=1,n===e){h=p;continue}if(c)continue;if(n.nodeNameInCorrectCase()!==s)continue;d=!0;const i=new Set(l);if(!i.size){c=!0;continue}const o=r(n);for(let e=0;e<o.length;++e){const t=o[e];if(i.has(t)&&(i.delete(t),!i.size)){c=!0;break}}}let m=s;if(n&&"input"===s.toLowerCase()&&e.getAttribute("type")&&!e.getAttribute("id")&&!e.getAttribute("class")&&(m+="[type="+CSS.escape(e.getAttribute("type")||"")+"]"),c)m+=":nth-child("+(h+1)+")";else if(d)for(const e of l)m+="."+CSS.escape(e.slice(1));return new Ui(m,!1)},Ri=function(e,t){if(e.nodeType()===Node.DOCUMENT_NODE)return"/";const n=[];let i=e;for(;i;){const e=Di(i,t);if(!e)break;if(n.push(e),e.optimized)break;i=i.parentNode}return n.reverse(),(n.length&&n[0].optimized?"":"/")+n.join("/")},Di=function(e,t){let n;const i=Fi(e);if(-1===i)return null;switch(e.nodeType()){case Node.ELEMENT_NODE:if(t&&e.getAttribute("id"))return new Ui('//*[@id="'+e.getAttribute("id")+'"]',!0);n=e.localName();break;case Node.ATTRIBUTE_NODE:n="@"+e.nodeName();break;case Node.TEXT_NODE:case Node.CDATA_SECTION_NODE:n="text()";break;case Node.PROCESSING_INSTRUCTION_NODE:n="processing-instruction()";break;case Node.COMMENT_NODE:n="comment()";break;case Node.DOCUMENT_NODE:default:n=""}return i>0&&(n+="["+i+"]"),new Ui(n,e.nodeType()===Node.DOCUMENT_NODE)},Fi=function(e){function t(e,t){if(e===t)return!0;if(e.nodeType()===Node.ELEMENT_NODE&&t.nodeType()===Node.ELEMENT_NODE)return e.localName()===t.localName();if(e.nodeType()===t.nodeType())return!0;return(e.nodeType()===Node.CDATA_SECTION_NODE?Node.TEXT_NODE:e.nodeType())===(t.nodeType()===Node.CDATA_SECTION_NODE?Node.TEXT_NODE:t.nodeType())}const n=e.parentNode?e.parentNode.children():null;if(!n)return 0;let i;for(let s=0;s<n.length;++s)if(t(e,n[s])&&n[s]!==e){i=!0;break}if(!i)return 0;let s=1;for(let i=0;i<n.length;++i)if(t(e,n[i])){if(n[i]===e)return s;++s}return-1};class Ui{value;optimized;constructor(e,t){this.value=e,this.optimized=t||!1}toString(){return this.value}}var Bi=Object.freeze({__proto__:null,fullQualifiedSelector:function(e,t){return e.nodeType()!==Node.ELEMENT_NODE?e.localName()||e.nodeName().toLowerCase():Oi(e,t)},cssPath:Oi,canGetJSPath:Li,jsPath:Ai,xPath:Ri,Step:Ui});const _i=new CSSStyleSheet;_i.replaceSync('.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.editing br{display:none}.adorner-reveal{margin:0 3px}.adorner-with-icon{display:flex;justify-content:center;align-items:center;overflow:hidden;height:100%;devtools-icon{width:var(--sys-size-6);height:var(--sys-size-6);color:var(--icon-primary)}}.adorner-with-icon *:not(:last-child){margin-right:2px}.elements-disclosure{width:100%;display:inline-block;line-height:normal}.elements-disclosure li{padding:1px 0 0 14px;margin-left:-2px;word-break:normal;position:relative;min-height:15px;line-height:1.36;min-width:200px}.elements-disclosure li::after{content:"";position:absolute;inset:0;left:calc(var(--indent) * -1);width:var(--indent)}.elements-disclosure li.parent{display:flex}.elements-disclosure li.parent:not(.always-parent){margin-left:-12px}.elements-disclosure li .selected-hint::before{font-style:italic;content:" == $0";opacity:0%;position:absolute;white-space:pre}.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected .selected-hint::before{position:static;opacity:60%}.elements-disclosure li.parent:not(.always-parent)::before{box-sizing:border-box;user-select:none;mask-image:var(--image-file-triangle-right);height:14px;width:14px;content:"\\A0\\A0";color:transparent;text-shadow:none;margin-left:-3px;background-color:var(--icon-default);transition:transform 200ms}.elements-disclosure li.parent.expanded::before{transform:rotate(90deg);margin-top:-2px}.elements-disclosure li .selection{display:none;z-index:-1}.elements-disclosure li.selected .selection{display:block}.elements-disclosure li.elements-drag-over .selection{display:block;margin-top:-2px;border-top:2px solid var(--sys-color-primary)}.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) .selection{background-color:var(--sys-color-neutral-container)}.elements-disclosure li.hovered:not(.selected) .selection{display:block;left:3px;right:3px;background-color:var(--sys-color-state-hover-on-subtle);border-radius:5px}.elements-disclosure li .webkit-html-tag.close{margin-left:-12px}.elements-disclosure .elements-tree-outline.hide-selection-when-blurred .selected:focus-visible .highlight > *{background:var(--sys-color-state-focus-highlight);border-radius:2px;outline:2px solid var(--sys-color-state-focus-ring)}.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection{background-color:var(--sys-color-tonal-container)}.elements-disclosure ol{list-style-type:none;padding-inline-start:12px;margin:0}.elements-disclosure ol.children{display:none;min-width:100%}.elements-disclosure ol.children.expanded{display:inline-block}.elements-disclosure > ol{position:relative;margin:0;min-width:100%;min-height:100%;padding-left:2px}.elements-disclosure li.in-clipboard .highlight{outline:1px dotted var(--sys-color-divider)}.elements-tree-outline ol.shadow-root-deep{background-color:transparent}.elements-tree-editor{box-shadow:var(--drop-shadow);margin-right:4px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}.elements-gutter-decoration{position:absolute;top:3px;left:2px;height:9px;width:9px;border-radius:5px;border:1px solid var(--sys-color-orange-bright);background-color:var(--sys-color-orange-bright)}.elements-gutter-decoration.elements-has-decorated-children{opacity:50%}.add-attribute{margin-left:1px;margin-right:1px;white-space:nowrap}.elements-tree-nowrap,\n.elements-tree-nowrap .li{white-space:pre!important}.elements-disclosure .elements-tree-nowrap li{word-wrap:normal}@keyframes dom-update-highlight-animation{from{background-color:var(--sys-color-token-tag);color:var(--sys-color-cdt-base-container)}80%{background-color:var(--sys-color-token-meta)}to{background-color:inherit}}@keyframes dom-update-highlight-animation-dark{from{background-color:var(--sys-color-token-tag);color:var(--sys-color-cdt-base-container)}80%{background-color:var(--sys-color-cdt-base-container);color:inherit}to{background-color:inherit}}.dom-update-highlight{animation:dom-update-highlight-animation 1.4s 1 cubic-bezier(0,0,0.2,1);border-radius:2px}:host-context(.theme-with-dark-background) .dom-update-highlight{animation:dom-update-highlight-animation-dark 1.4s 1 cubic-bezier(0,0,0.2,1)}.elements-disclosure.single-node li{padding-left:2px}.elements-tree-shortcut-title,\n.elements-tree-shortcut-link{color:var(--sys-color-token-subtle)}.elements-disclosure .gutter-container{position:absolute;top:0;left:0;width:15px;height:15px;z-index:1}.elements-hide-gutter .gutter-container{display:none}.gutter-container > devtools-icon{display:block;visibility:hidden;position:relative;left:-1px}.elements-disclosure li.selected .gutter-container:not(.has-decorations) > devtools-icon{visibility:visible}li.hovered:not(.always-parent) + ol.children,\n.elements-tree-outline ol.shadow-root,\nli.selected:not(.always-parent) + ol.children{background:linear-gradient(to right,var(--override-indentation-level-border-color),var(--override-indentation-level-border-color) 0.5px,transparent 0);background-position-x:5px;background-size:0.5px 100%;background-repeat:no-repeat}li.selected:not(.always-parent) + ol.children{--override-indentation-level-border-color:var(--sys-color-divider)!important}li.hovered:not(.always-parent) + ol.children:not(.shadow-root){--override-indentation-level-border-color:color-mix(in sRGB,var(--ref-palette-neutral0) 10%,transparent)}.elements-tree-outline ol.shadow-root{--override-indentation-level-border-color:var(--ref-palette-orange95)}@media (forced-colors: active){.elements-disclosure li.parent::before{forced-color-adjust:none;background-color:ButtonText!important}.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected .selected-hint::before{opacity:unset}.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) .selection,\n  .elements-disclosure li.hovered:not(.selected) .selection,\n  .elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection{forced-color-adjust:none;background:canvas!important;border:1px solid Highlight!important}.gutter-container > devtools-icon{forced-color-adjust:none}}.violating-element{background-image:var(--image-file-errorWave);background-repeat:repeat-x;background-position:bottom;padding-bottom:1px}\n/*# sourceURL=elementsTreeOutline.css */\n');const Vi={reveal:"reveal"},Hi=r.i18n.registerUIStrings("panels/elements/TopLayerContainer.ts",Vi),Wi=r.i18n.getLocalizedString.bind(void 0,Hi);class ji extends i.TreeOutline.TreeElement{tree;document;currentTopLayerDOMNodes=new Set;topLayerUpdateThrottler;constructor(t,n){super("#top-layer"),this.tree=t,this.document=n,this.topLayerUpdateThrottler=new e.Throttler.Throttler(1)}async throttledUpdateTopLayerElements(){await this.topLayerUpdateThrottler.schedule((()=>this.updateTopLayerElements()))}async updateTopLayerElements(){this.removeChildren(),this.removeCurrentTopLayerElementsAdorners(),this.currentTopLayerDOMNodes=new Set;const e=this.document.domModel(),t=await e.getTopLayerElements();if(!t||0===t.length)return;let i=0;for(let s=0;s<t.length;s++){const o=e.idToDOMNode.get(t[s]);if(o&&o.ownerDocument===this.document&&"::backdrop"!==o.nodeName()){const r=new n.DOMModel.DOMNodeShortcut(e.target(),o.backendNodeId(),0,o.nodeName()),a=new ts(r);this.appendChild(a),this.currentTopLayerDOMNodes.add(o);const l=s>0?e.idToDOMNode.get(t[s-1]):void 0;if(l&&"::backdrop"===l.nodeName()){const t=new n.DOMModel.DOMNodeShortcut(e.target(),l.backendNodeId(),0,l.nodeName()),i=new ts(t);a.appendChild(i)}const d=this.tree.treeElementByNode.get(o);d&&this.addTopLayerAdorner(d,a,++i)}}}removeCurrentTopLayerElementsAdorners(){for(const e of this.currentTopLayerDOMNodes){const t=this.tree.treeElementByNode.get(e);t?.removeAllAdorners()}}addTopLayerAdorner(e,t,n){const i=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.TOP_LAYER),s=document.createElement("span");s.classList.add("adorner-with-icon");const o=new w.Icon.Icon;o.name="select-element";const r=document.createElement("span");r.textContent=`top-layer (${n})`,s.append(o),s.append(r);const a=e?.adorn(i,s);if(a){const e=()=>{t.revealAndSelect()};a.addInteraction(e,{isToggle:!1,shouldPropagateOnKeydown:!1,ariaLabelDefault:Wi(Vi.reveal),ariaLabelActive:Wi(Vi.reveal)}),a.addEventListener("mousedown",(e=>e.consume()),!1)}}}var zi=Object.freeze({__proto__:null,TopLayerContainer:ji});const $i={pageDom:"Page DOM",storeAsGlobalVariable:"Store as global variable",showAllNodesDMore:"Show all nodes ({PH1} more)",reveal:"reveal",adornerSettings:"Badge settings…"},Ki=r.i18n.registerUIStrings("panels/elements/ElementsTreeOutline.ts",$i),Gi=r.i18n.getLocalizedString.bind(void 0,Ki),Yi=new WeakMap,qi=new Set;class Xi extends(e.ObjectWrapper.eventMixin(i.TreeOutline.TreeOutline)){treeElementByNode;shadowRoot;elementInternal;includeRootDOMNode;selectEnabled;rootDOMNodeInternal;selectedDOMNodeInternal;visible;imagePreviewPopover;updateRecords;treeElementsBeingUpdated;decoratorExtensions;showHTMLCommentsSetting;multilineEditing;visibleWidthInternal;clipboardNodeData;isXMLMimeTypeInternal;suppressRevealAndSelect=!1;previousHoveredElement;treeElementBeingDragged;dragOverTreeElement;updateModifiedNodesTimeout;#X=[];#Q=new Map;#J;#Z;#ee=new Map;constructor(n,o,r){if(super(),t.Runtime.experiments.isEnabled("highlight-errors-elements-panel")){this.#J=O.IssuesManager.IssuesManager.instance(),this.#J.addEventListener("IssueAdded",this.#te,this);for(const e of this.#J.issues())e instanceof O.GenericIssue.GenericIssue&&this.#ne(e)}this.treeElementByNode=new WeakMap;const a=document.createElement("div");this.shadowRoot=i.UIUtils.createShadowRootWithCoreStyles(a,{cssFile:[_i,M.Style.default],delegatesFocus:void 0});const l=this.shadowRoot.createChild("div","elements-disclosure");this.elementInternal=this.element,this.elementInternal.classList.add("elements-tree-outline","source-code"),r&&this.elementInternal.classList.add("elements-hide-gutter"),i.ARIAUtils.setLabel(this.elementInternal,Gi($i.pageDom)),this.elementInternal.addEventListener("focusout",this.onfocusout.bind(this),!1),this.elementInternal.addEventListener("mousedown",this.onmousedown.bind(this),!1),this.elementInternal.addEventListener("mousemove",this.onmousemove.bind(this),!1),this.elementInternal.addEventListener("mouseleave",this.onmouseleave.bind(this),!1),this.elementInternal.addEventListener("dragstart",this.ondragstart.bind(this),!1),this.elementInternal.addEventListener("dragover",this.ondragover.bind(this),!1),this.elementInternal.addEventListener("dragleave",this.ondragleave.bind(this),!1),this.elementInternal.addEventListener("drop",this.ondrop.bind(this),!1),this.elementInternal.addEventListener("dragend",this.ondragend.bind(this),!1),this.elementInternal.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),this.elementInternal.addEventListener("clipboard-beforecopy",this.onBeforeCopy.bind(this),!1),this.elementInternal.addEventListener("clipboard-copy",this.onCopyOrCut.bind(this,!1),!1),this.elementInternal.addEventListener("clipboard-cut",this.onCopyOrCut.bind(this,!0),!1),this.elementInternal.addEventListener("clipboard-paste",this.onPaste.bind(this),!1),this.elementInternal.addEventListener("keydown",this.onKeyDown.bind(this),!1),l.appendChild(this.elementInternal),this.element=a,this.contentElement.setAttribute("jslog",`${s.tree("elements")}`),this.includeRootDOMNode=!n,this.selectEnabled=o,this.rootDOMNodeInternal=null,this.selectedDOMNodeInternal=null,this.visible=!1,this.imagePreviewPopover=new ie(this.contentElement,(e=>{let t=e.target;for(;t&&!ie.getImageURL(t);)t=t.parentElementOrShadowHost();return t}),(e=>{const t=i.UIUtils.enclosingNodeOrSelfWithNodeName(e,"li");if(!t)return null;const n=i.TreeOutline.TreeElement.getTreeElementBylistItemNode(t);return n?n.node():null})),this.updateRecords=new Map,this.treeElementsBeingUpdated=new Set,this.decoratorExtensions=null,this.showHTMLCommentsSetting=e.Settings.Settings.instance().moduleSetting("show-html-comments"),this.showHTMLCommentsSetting.addChangeListener(this.onShowHTMLCommentsChange.bind(this)),this.setUseLightSelectionColor(!0),t.Runtime.experiments.isEnabled("highlight-errors-elements-panel")&&(this.#Z=new i.PopoverHelper.PopoverHelper(this.elementInternal,(t=>{const n=t.composedPath()[0];if(!n||!n.matches(".violating-element"))return null;const i=this.#ee.get(n);if(!i)return null;const s=i.details(),o=this.#ie(s.errorType),r=new w.Icon.Icon;r.data=L.IssueCounter.getIssueKindIconData(i.getKind()),r.style.cursor="pointer";const a=document.createElement("a");a.href="#",a.textContent="View issue:";const l=document.createElement("span");l.textContent=o;const d=document.createElement("div");return d.appendChild(r),d.appendChild(a),d.appendChild(l),d.style.display="flex",d.style.alignItems="center",d.style.gap="5px",{box:n.boxInWindow(),show:async t=>{t.setIgnoreLeftMargin(!0);const n=()=>e.Revealer.reveal(i);return a.addEventListener("click",(()=>n())),r.addEventListener("click",(()=>n())),t.contentElement.appendChild(d),!0}}}),"elements.issue"),this.#Z.setTimeout(300),this.#Z.setHasPadding(!0))}#ie(e){switch(e){case"FormLabelForNameError":case"FormLabelForMatchesNonExistingIdError":return"Incorrect use of <label for=FORM_ELEMENT>";case"FormDuplicateIdForInputError":return"Duplicate form field id in the same form";case"FormInputWithNoLabelError":return"Form field without valid aria-labelledby attribute or associated label";case"FormAutocompleteAttributeEmptyError":return"Incorrect use of autocomplete attribute";case"FormEmptyIdAndNameAttributesForInputError":return"A form field element should have an id or name attribute";case"FormAriaLabelledByToNonExistingId":return"An aria-labelledby attribute doesn't match any element id";case"FormInputAssignedAutocompleteValueToIdOrNameAttributeError":return"An element doesn't have an autocomplete attribute";case"FormLabelHasNeitherForNorNestedInput":return"No label associated with a form field";case"FormInputHasWrongButWellIntendedAutocompleteValueError":return"Non-standard autocomplete attribute value";default:return""}}static forDOMModel(e){return Yi.get(e)||null}async#te(e){e.data.issue instanceof O.GenericIssue.GenericIssue&&(this.#ne(e.data.issue),await this.#se(e.data.issue))}#ne(e){this.#X.push(e)}#oe(){for(const e of this.#X)this.#se(e)}async#se(e){const t=e.details();if(!this.#ie(t.errorType))return;if(!this.rootDOMNode||!t.violatingNodeId)return;const i=new n.DOMModel.DeferredDOMNode(this.rootDOMNode.domModel().target(),t.violatingNodeId),s=await i.resolvePromise();if(!s)return;const o=this.findTreeElement(s);if(o){o.addIssue(e);const t=o.issuesByNodeElement;for(const[e,n]of t)this.#ee.set(e,n)}}onShowHTMLCommentsChange(){const e=this.selectedDOMNode();e&&e.nodeType()===Node.COMMENT_NODE&&!this.showHTMLCommentsSetting.get()&&this.selectDOMNode(e.parentNode),this.update()}setWordWrap(e){this.elementInternal.classList.toggle("elements-tree-nowrap",!e)}setMultilineEditing(e){this.multilineEditing=e}visibleWidth(){return this.visibleWidthInternal||0}setVisibleWidth(e){this.visibleWidthInternal=e,this.multilineEditing&&this.multilineEditing.resize()}setClipboardData(e){if(this.clipboardNodeData){const e=this.findTreeElement(this.clipboardNodeData.node);e&&e.setInClipboard(!1),delete this.clipboardNodeData}if(e){const t=this.findTreeElement(e.node);t&&t.setInClipboard(!0),this.clipboardNodeData=e}}resetClipboardIfNeeded(e){this.clipboardNodeData&&this.clipboardNodeData.node===e&&this.setClipboardData(null)}onBeforeCopy(e){e.handled=!0}onCopyOrCut(e,t){this.setClipboardData(null);const n=t.original;if(!n||!n.target)return;if(n.target instanceof Node&&n.target.hasSelection())return;if(i.UIUtils.isEditing())return;const s=this.selectedDOMNode();s&&n.clipboardData&&(n.clipboardData.clearData(),t.handled=!0,this.performCopyOrCut(e,s))}performCopyOrCut(e,t){t&&(e&&(t.isShadowRoot()||t.ancestorUserAgentShadowRoot())||(t.copyNode(),this.setClipboardData({node:t,isCut:e})))}canPaste(e){if(e.isShadowRoot()||e.ancestorUserAgentShadowRoot())return!1;if(!this.clipboardNodeData)return!1;const t=this.clipboardNodeData.node;return(!this.clipboardNodeData.isCut||t!==e&&!t.isAncestor(e))&&e.domModel()===t.domModel()}pasteNode(e){this.canPaste(e)&&this.performPaste(e)}duplicateNode(e){this.performDuplicate(e)}onPaste(e){if(i.UIUtils.isEditing())return;const t=this.selectedDOMNode();t&&this.canPaste(t)&&(e.handled=!0,this.performPaste(t))}performPaste(e){function t(e,t){!e&&t&&this.selectDOMNode(t)}this.clipboardNodeData&&(this.clipboardNodeData.isCut?(this.clipboardNodeData.node.moveTo(e,null,t.bind(this)),this.setClipboardData(null)):this.clipboardNodeData.node.copyTo(e,null,t.bind(this)))}performDuplicate(e){if(e.isInShadowTree())return;const t=e.parentNode?e.parentNode:e;"#document"!==t.nodeName()&&e.copyTo(t,e.nextSibling)}setVisible(e){if(e!==this.visible){if(this.visible=e,!this.visible)return this.imagePreviewPopover.hide(),void(this.multilineEditing&&this.multilineEditing.cancel());this.runPendingUpdates(),this.selectedDOMNodeInternal&&this.revealAndSelectNode(this.selectedDOMNodeInternal,!1)}}get rootDOMNode(){return this.rootDOMNodeInternal}set rootDOMNode(e){this.rootDOMNodeInternal!==e&&(this.rootDOMNodeInternal=e,this.isXMLMimeTypeInternal=e&&e.isXMLNode(),this.update())}get isXMLMimeType(){return Boolean(this.isXMLMimeTypeInternal)}selectedDOMNode(){return this.selectedDOMNodeInternal}selectDOMNode(e,t){this.selectedDOMNodeInternal!==e?(this.selectedDOMNodeInternal=e,this.revealAndSelectNode(e,!t),this.selectedDOMNodeInternal===e&&this.selectedNodeChanged(Boolean(t))):this.revealAndSelectNode(e,!t)}editing(){const e=this.selectedDOMNode();if(!e)return!1;const t=this.findTreeElement(e);return t&&t.isEditing()||!1}update(){const e=this.selectedDOMNode();if(this.removeChildren(),this.rootDOMNode){if(this.includeRootDOMNode){const e=this.createElementTreeElement(this.rootDOMNode);this.appendChild(e)}else{const e=this.visibleChildren(this.rootDOMNode);for(const t of e){const e=this.createElementTreeElement(t);this.appendChild(e)}}this.rootDOMNode instanceof n.DOMModel.DOMDocument&&this.createTopLayerContainer(this.rootElement(),this.rootDOMNode),e&&this.revealAndSelectNode(e,!0)}}selectedNodeChanged(e){this.dispatchEventToListeners(Xi.Events.SelectedNodeChanged,{node:this.selectedDOMNodeInternal,focus:e})}fireElementsTreeUpdated(e){this.dispatchEventToListeners(Xi.Events.ElementsTreeUpdated,e)}findTreeElement(e){let t=this.lookUpTreeElement(e);return t||e.nodeType()!==Node.TEXT_NODE||(t=this.lookUpTreeElement(e.parentNode)),t}lookUpTreeElement(e){if(!e)return null;const t=this.treeElementByNode.get(e);if(t)return t;const n=[];let i;for(i=e.parentNode;i&&(n.push(i),!this.treeElementByNode.has(i));i=i.parentNode);if(!i)return null;for(let t=n.length-1;t>=0;--t){const i=n[t-1]||e,s=this.treeElementByNode.get(n[t]);s&&(s.onpopulate(),i.index&&i.index>=s.expandedChildrenLimit()&&this.setExpandedChildrenLimit(s,i.index+1))}return this.treeElementByNode.get(e)||null}createTreeElementFor(e){let t=this.findTreeElement(e);return t||(e.parentNode?(t=this.createTreeElementFor(e.parentNode),t?this.showChild(t,e):null):null)}revealAndSelectNode(e,t){if(this.suppressRevealAndSelect)return;if(!this.includeRootDOMNode&&e===this.rootDOMNode&&this.rootDOMNode&&(e=this.rootDOMNode.firstChild),!e)return;const n=this.createTreeElementFor(e);n&&n.revealAndSelect(t)}treeElementFromEventInternal(e){if(!this.element.parentElement)return null;const t=e.pageX,n=e.pageY,i=this.treeElementFromPoint(t,n);let s;return s=i===this.treeElementFromPoint(t,n-2)?i:this.treeElementFromPoint(t,n+2),s}onfocusout(e){n.OverlayModel.OverlayModel.hideDOMNodeHighlight()}onmousedown(e){const t=this.treeElementFromEventInternal(e);t&&t.select()}setHoverEffect(e){this.previousHoveredElement!==e&&(this.previousHoveredElement instanceof gs&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement),e instanceof gs&&(e.hovered=!0,this.previousHoveredElement=e))}onmousemove(e){const t=this.treeElementFromEventInternal(e);t&&this.previousHoveredElement===t||(this.setHoverEffect(t),this.highlightTreeElement(t,!i.KeyboardShortcut.KeyboardShortcut.eventHasEitherCtrlOrMeta(e)))}highlightTreeElement(e,t){e instanceof gs?e.node().domModel().overlayModel().highlightInOverlay({node:e.node(),selectorList:void 0},"all",t):e instanceof ts&&e.domModel().overlayModel().highlightInOverlay({deferredNode:e.deferredNode(),selectorList:void 0},"all",t)}onmouseleave(e){this.setHoverEffect(null),n.OverlayModel.OverlayModel.hideDOMNodeHighlight()}ondragstart(e){const t=e.target;if(!t||t.hasSelection())return!1;if("A"===t.nodeName)return!1;const i=this.validDragSourceOrTarget(this.treeElementFromEventInternal(e));return!!i&&("BODY"!==i.node().nodeName()&&"HEAD"!==i.node().nodeName()&&(e.dataTransfer&&i.listItemElement.textContent?(e.dataTransfer.setData("text/plain",i.listItemElement.textContent.replace(/\u200b/g,"")),e.dataTransfer.effectAllowed="copyMove",this.treeElementBeingDragged=i,n.OverlayModel.OverlayModel.hideDOMNodeHighlight(),!0):void 0))}ondragover(e){if(!this.treeElementBeingDragged)return!1;const t=this.validDragSourceOrTarget(this.treeElementFromEventInternal(e));if(!t)return!1;let n=t.node();for(;n;){if(n===this.treeElementBeingDragged.nodeInternal)return!1;n=n.parentNode}return t.listItemElement.classList.add("elements-drag-over"),this.dragOverTreeElement=t,e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),!1}ondragleave(e){return this.clearDragOverTreeElementMarker(),e.preventDefault(),!1}validDragSourceOrTarget(e){if(!e)return null;if(!(e instanceof gs))return null;const t=e,n=t.node();return n.parentNode&&n.parentNode.nodeType()===Node.ELEMENT_NODE?t:null}ondrop(e){e.preventDefault();const t=this.treeElementFromEventInternal(e);t instanceof gs&&this.doMove(t)}doMove(e){if(!this.treeElementBeingDragged)return;let t,n;if(e.isClosingTag())t=e.node(),n=null;else{const i=e.node();t=i.parentNode,n=i}if(!t)return;const i=this.treeElementBeingDragged.expanded;this.treeElementBeingDragged.nodeInternal.moveTo(t,n,this.selectNodeAfterEdit.bind(this,i)),delete this.treeElementBeingDragged}ondragend(e){e.preventDefault(),this.clearDragOverTreeElementMarker(),delete this.treeElementBeingDragged}clearDragOverTreeElementMarker(){this.dragOverTreeElement&&(this.dragOverTreeElement.listItemElement.classList.remove("elements-drag-over"),delete this.dragOverTreeElement)}contextMenuEventFired(e){const t=this.treeElementFromEventInternal(e);t instanceof gs&&this.showContextMenu(t,e)}showContextMenu(e,t){if(i.UIUtils.isEditing())return;const n=new i.ContextMenu.ContextMenu(t),s=Boolean(e.node().pseudoType()),o=e.node().nodeType()===Node.ELEMENT_NODE&&!s,r=t.target;if(!r)return;let a=r.enclosingNodeOrSelfWithClass("webkit-html-text-node");a&&a.classList.contains("bogus")&&(a=null);const l=r.enclosingNodeOrSelfWithClass("webkit-html-comment");n.saveSection().appendItem(Gi($i.storeAsGlobalVariable),this.saveNodeToTempVariable.bind(this,e.node()),{jslogContext:"store-as-global-variable"}),a?e.populateTextContextMenu(n,a):o?e.populateTagContextMenu(n,t):l?e.populateNodeContextMenu(n):s&&e.populatePseudoElementContextMenu(n),n.viewSection().appendItem(Gi($i.adornerSettings),(()=>{Ls.instance().showAdornerSettingsPane()}),{jslogContext:"show-adorner-settings"}),n.appendApplicableItems(e.node()),n.show()}async saveNodeToTempVariable(e){const t=await e.resolveToObject(),s=t?.runtimeModel().target()?.model(n.ConsoleModel.ConsoleModel);await(s?.saveToTempVariable(i.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext),t))}runPendingUpdates(){this.updateModifiedNodes()}onKeyDown(e){const t=e;if(i.UIUtils.isEditing())return;const n=this.selectedDOMNode();if(!n)return;const s=this.treeElementByNode.get(n);if(s&&i.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(t)&&n.parentNode){if("ArrowUp"===t.key&&n.previousSibling)return n.moveTo(n.parentNode,n.previousSibling,this.selectNodeAfterEdit.bind(this,s.expanded)),void t.consume(!0);if("ArrowDown"===t.key&&n.nextSibling)return n.moveTo(n.parentNode,n.nextSibling.nextSibling,this.selectNodeAfterEdit.bind(this,s.expanded)),void t.consume(!0)}}toggleEditAsHTML(e,t,n){const i=this.treeElementByNode.get(e);if(!i||!i.hasEditableNode())return;if(e.pseudoType())return;const s=e.parentNode,o=e.index,r=i.expanded;i.toggleEditAsHTML(function(e){n&&n();if(!e)return;if(this.runPendingUpdates(),!o)return;const t=s&&s.children(),i=t&&t[o]||s;if(!i)return;if(this.selectDOMNode(i,!0),r){const e=this.findTreeElement(i);e&&e.expand()}}.bind(this),t)}selectNodeAfterEdit(e,t,n){if(t)return null;if(this.runPendingUpdates(),!n)return null;this.selectDOMNode(n,!0);const i=this.findTreeElement(n);return e&&i&&i.expand(),i}async toggleHideElement(e){const t=e.pseudoType(),n=t?e.parentNode:e;if(!n)return;const i=e.marker("hidden-marker"),s=await n.resolveToObject("");s&&(await s.callFunction((function(e,t){const n="__web-inspector-hide-shortcut-style__",i=[];i.push(".__web-inspector-hide-shortcut__"),i.push(".__web-inspector-hide-shortcut__ *"),i.push(".__web-inspector-hidebefore-shortcut__::before"),i.push(".__web-inspector-hideafter-shortcut__::after");const s=i.join(", "),o="\n"+s+"\n{\n    visibility: hidden !important;\n}\n",r="__web-inspector-hide"+(e||"")+"-shortcut__";this.classList.toggle(r,t);let a=this;for(;a.parentNode;)a=a.parentNode;a.nodeType===Node.DOCUMENT_NODE&&(a=document.head);let l=a.querySelector("style#"+n);if(l)return;l=document.createElement("style"),l.id=n,l.textContent=o,a.appendChild(l)}),[{value:t},{value:!i}]),s.release(),e.setMarker("hidden-marker",!i||null))}isToggledToHidden(e){return Boolean(e.marker("hidden-marker"))}reset(){this.rootDOMNode=null,this.selectDOMNode(null,!1),this.imagePreviewPopover.hide(),delete this.clipboardNodeData,n.OverlayModel.OverlayModel.hideDOMNodeHighlight(),this.updateRecords.clear()}wireToDOMModel(e){Yi.set(e,this),e.addEventListener(n.DOMModel.Events.MarkersChanged,this.markersChanged,this),e.addEventListener(n.DOMModel.Events.NodeInserted,this.nodeInserted,this),e.addEventListener(n.DOMModel.Events.NodeRemoved,this.nodeRemoved,this),e.addEventListener(n.DOMModel.Events.AttrModified,this.attributeModified,this),e.addEventListener(n.DOMModel.Events.AttrRemoved,this.attributeRemoved,this),e.addEventListener(n.DOMModel.Events.CharacterDataModified,this.characterDataModified,this),e.addEventListener(n.DOMModel.Events.DocumentUpdated,this.documentUpdated,this),e.addEventListener(n.DOMModel.Events.ChildNodeCountUpdated,this.childNodeCountUpdated,this),e.addEventListener(n.DOMModel.Events.DistributedNodesChanged,this.distributedNodesChanged,this),e.addEventListener(n.DOMModel.Events.TopLayerElementsChanged,this.topLayerElementsChanged,this)}unwireFromDOMModel(e){e.removeEventListener(n.DOMModel.Events.MarkersChanged,this.markersChanged,this),e.removeEventListener(n.DOMModel.Events.NodeInserted,this.nodeInserted,this),e.removeEventListener(n.DOMModel.Events.NodeRemoved,this.nodeRemoved,this),e.removeEventListener(n.DOMModel.Events.AttrModified,this.attributeModified,this),e.removeEventListener(n.DOMModel.Events.AttrRemoved,this.attributeRemoved,this),e.removeEventListener(n.DOMModel.Events.CharacterDataModified,this.characterDataModified,this),e.removeEventListener(n.DOMModel.Events.DocumentUpdated,this.documentUpdated,this),e.removeEventListener(n.DOMModel.Events.ChildNodeCountUpdated,this.childNodeCountUpdated,this),e.removeEventListener(n.DOMModel.Events.DistributedNodesChanged,this.distributedNodesChanged,this),e.removeEventListener(n.DOMModel.Events.TopLayerElementsChanged,this.topLayerElementsChanged,this),Yi.delete(e)}addUpdateRecord(e){let t=this.updateRecords.get(e);return t||(t=new Ji,this.updateRecords.set(e,t)),t}updateRecordForHighlight(e){return this.visible&&this.updateRecords.get(e)||null}documentUpdated(e){const n=e.data;this.reset(),n.existingDocument()&&(this.rootDOMNode=n.existingDocument(),t.Runtime.experiments.isEnabled("highlight-errors-elements-panel")&&this.#oe())}attributeModified(e){const{node:t}=e.data;this.addUpdateRecord(t).attributeModified(e.data.name),this.updateModifiedNodesSoon()}attributeRemoved(e){const{node:t}=e.data;this.addUpdateRecord(t).attributeRemoved(e.data.name),this.updateModifiedNodesSoon()}characterDataModified(e){const t=e.data;this.addUpdateRecord(t).charDataModified(),t.parentNode&&t.parentNode.firstChild===t.parentNode.lastChild&&this.addUpdateRecord(t.parentNode).childrenModified(),this.updateModifiedNodesSoon()}nodeInserted(e){const t=e.data;this.addUpdateRecord(t.parentNode).nodeInserted(t),this.updateModifiedNodesSoon()}nodeRemoved(e){const{node:t,parent:n}=e.data;this.resetClipboardIfNeeded(t),this.addUpdateRecord(n).nodeRemoved(t),this.updateModifiedNodesSoon()}childNodeCountUpdated(e){const t=e.data;this.addUpdateRecord(t).childrenModified(),this.updateModifiedNodesSoon()}distributedNodesChanged(e){const t=e.data;this.addUpdateRecord(t).childrenModified(),this.updateModifiedNodesSoon()}updateModifiedNodesSoon(){this.updateRecords.size&&(this.updateModifiedNodesTimeout||(this.updateModifiedNodesTimeout=window.setTimeout(this.updateModifiedNodes.bind(this),50)))}updateModifiedNodes(){this.updateModifiedNodesTimeout&&(clearTimeout(this.updateModifiedNodesTimeout),delete this.updateModifiedNodesTimeout);const e=[...this.updateRecords.keys()],t=e.length>10;let n,i;t&&(n=this.element.parentNode,i=n?n.scrollTop:0,this.elementInternal.classList.add("hidden"));const s=this.rootDOMNodeInternal&&this.updateRecords.get(this.rootDOMNodeInternal);if(s&&s.hasChangedChildren())this.update();else for(const[e,t]of this.updateRecords)t.hasChangedChildren()?this.updateModifiedParentNode(e):this.updateModifiedNode(e);t&&(this.elementInternal.classList.remove("hidden"),n&&i&&(n.scrollTop=i)),this.updateRecords.clear(),this.fireElementsTreeUpdated(e)}updateModifiedNode(e){const t=this.findTreeElement(e);t&&t.updateTitle(this.updateRecordForHighlight(e))}updateModifiedParentNode(e){const t=this.findTreeElement(e);t&&(t.setExpandable(this.hasVisibleChildren(e)),t.updateTitle(this.updateRecordForHighlight(e)),qi.has(t)&&this.updateChildren(t))}populateTreeElement(e){return e.childCount()||!e.isExpandable()?Promise.resolve():new Promise((t=>{e.node().getChildNodes((()=>{qi.add(e),this.updateModifiedParentNode(e.node()),t()}))}))}async createTopLayerContainer(e,t){if(!(e.treeOutline&&e.treeOutline instanceof Xi))return;const n=new ji(e.treeOutline,t);await n.throttledUpdateTopLayerElements(),n.currentTopLayerDOMNodes.size>0&&e.appendChild(n),this.#Q.set(e,n)}createElementTreeElement(e,t){const n=new gs(e,t);return n.setExpandable(!t&&this.hasVisibleChildren(e)),e.nodeType()===Node.ELEMENT_NODE&&e.parentNode&&e.parentNode.nodeType()===Node.DOCUMENT_NODE&&!e.parentNode.parentNode&&n.setCollapsible(!1),e.hasAssignedSlot()&&n.createSlotLink(e.assignedSlot),n.selectable=Boolean(this.selectEnabled),n}showChild(e,t){if(e.isClosingTag())return null;const n=this.visibleChildren(e.node()).indexOf(t);return-1===n?null:(n>=e.expandedChildrenLimit()&&this.setExpandedChildrenLimit(e,n+1),e.childAt(n))}visibleChildren(e){let t=gs.visibleShadowRoots(e);const n=e.contentDocument();n&&t.push(n);const i=e.templateContent();i&&t.push(i),t.push(...e.viewTransitionPseudoElements());const s=e.markerPseudoElement();s&&t.push(s);const o=e.beforePseudoElement();if(o&&t.push(o),e.childNodeCount()){let n=e.children()||[];this.showHTMLCommentsSetting.get()||(n=n.filter((e=>e.nodeType()!==Node.COMMENT_NODE))),t=t.concat(n)}const r=e.afterPseudoElement();r&&t.push(r);const a=e.backdropPseudoElement();return a&&t.push(a),t}hasVisibleChildren(e){return!!e.isIframe()||(!!e.contentDocument()||(!!e.templateContent()||(!!gs.visibleShadowRoots(e).length||(!!e.hasPseudoElements()||(!!e.isInsertionPoint()||Boolean(e.childNodeCount())&&!gs.canShowInlineText(e))))))}createExpandAllButtonTreeElement(e){const t=i.UIUtils.createTextButton("",function(t){const n=this.visibleChildren(e.node()).length;this.setExpandedChildrenLimit(e,Math.max(n,e.expandedChildrenLimit()+ys)),t.consume()}.bind(this));t.value="";const n=new i.TreeOutline.TreeElement(t);return n.selectable=!1,n.button=t,n}setExpandedChildrenLimit(e,t){e.expandedChildrenLimit()!==t&&(e.setExpandedChildrenLimit(t),e.treeOutline&&!this.treeElementsBeingUpdated.has(e)&&this.updateModifiedParentNode(e.node()))}updateChildren(e){if(!e.isExpandable()){if(!e.treeOutline)return;const t=e.treeOutline.selectedTreeElement;return t&&t.hasAncestor(e)&&e.select(!0),void e.removeChildren()}console.assert(!e.isClosingTag()),this.innerUpdateChildren(e)}insertChildElement(e,t,i,s){const o=this.createElementTreeElement(t,s);return e.insertChild(o,i),t instanceof n.DOMModel.DOMDocument&&this.createTopLayerContainer(o,t),o}moveChild(e,t,n){if(e.indexOfChild(t)===n)return;const i=t.selected;t.parent&&t.parent.removeChild(t),e.insertChild(t,n),i&&t.select()}innerUpdateChildren(e){if(this.treeElementsBeingUpdated.has(e))return;this.treeElementsBeingUpdated.add(e);const t=e.node(),n=this.visibleChildren(t),i=new Set(n),s=new Map;for(let t=e.childCount()-1;t>=0;--t){const n=e.childAt(t);if(!(n instanceof gs)){e.removeChildAtIndex(t);continue}const o=n.node();i.has(o)?s.set(o,n):e.removeChildAtIndex(t)}for(let i=0;i<n.length&&i<e.expandedChildrenLimit();++i){const o=n[i],r=s.get(o)||this.findTreeElement(o);if(r&&r!==e)this.moveChild(e,r,i);else{const n=this.insertChildElement(e,o,i);this.updateRecordForHighlight(t)&&e.expanded&&gs.animateOnDOMUpdate(n),e.childCount()>e.expandedChildrenLimit()&&this.setExpandedChildrenLimit(e,e.expandedChildrenLimit()+1)}}const o=e.childCount();if(n.length>o){const t=o;e.expandAllButtonElement||(e.expandAllButtonElement=this.createExpandAllButtonTreeElement(e)),e.insertChild(e.expandAllButtonElement,t),e.expandAllButtonElement.title=Gi($i.showAllNodesDMore,{PH1:n.length-o})}else e.expandAllButtonElement&&(e.expandAllButtonElement=null);if(t.isInsertionPoint())for(const n of t.distributedNodes())e.appendChild(new ts(n));t.nodeType()===Node.ELEMENT_NODE&&!t.pseudoType()&&e.isExpandable()&&this.insertChildElement(e,t,e.childCount(),!0),this.treeElementsBeingUpdated.delete(e)}markersChanged(e){const t=e.data,n=this.treeElementByNode.get(t);n&&n.updateDecorations()}async topLayerElementsChanged(){for(const[e,t]of this.#Q)await t.throttledUpdateTopLayerElements(),t.currentTopLayerDOMNodes.size>0&&t.parent!==e&&e.appendChild(t),t.hidden=0===t.currentTopLayerDOMNodes.size}static treeOutlineSymbol=Symbol("treeOutline")}!function(e){let t;!function(e){e.SelectedNodeChanged="SelectedNodeChanged",e.ElementsTreeUpdated="ElementsTreeUpdated"}(t=e.Events||(e.Events={}))}(Xi||(Xi={}));const Qi=new Map([[" ","nbsp"],["­","shy"],[" ","ensp"],[" ","emsp"],[" ","thinsp"],[" ","hairsp"],["​","ZeroWidthSpace"],["‌","zwnj"],["‍","zwj"],["‎","lrm"],["‏","rlm"],["‪","#x202A"],["‫","#x202B"],["‬","#x202C"],["‭","#x202D"],["‮","#x202E"],["⁠","NoBreak"],["\ufeff","#xFEFF"]]);class Ji{modifiedAttributes;removedAttributes;hasChangedChildrenInternal;hasRemovedChildrenInternal;charDataModifiedInternal;attributeModified(e){this.removedAttributes&&this.removedAttributes.has(e)&&this.removedAttributes.delete(e),this.modifiedAttributes||(this.modifiedAttributes=new Set),this.modifiedAttributes.add(e)}attributeRemoved(e){this.modifiedAttributes&&this.modifiedAttributes.has(e)&&this.modifiedAttributes.delete(e),this.removedAttributes||(this.removedAttributes=new Set),this.removedAttributes.add(e)}nodeInserted(e){this.hasChangedChildrenInternal=!0}nodeRemoved(e){this.hasChangedChildrenInternal=!0,this.hasRemovedChildrenInternal=!0}charDataModified(){this.charDataModifiedInternal=!0}childrenModified(){this.hasChangedChildrenInternal=!0}isAttributeModified(e){return null!==this.modifiedAttributes&&void 0!==this.modifiedAttributes&&this.modifiedAttributes.has(e)}hasRemovedAttributes(){return null!==this.removedAttributes&&void 0!==this.removedAttributes&&Boolean(this.removedAttributes.size)}isCharDataModified(){return Boolean(this.charDataModifiedInternal)}hasChangedChildren(){return Boolean(this.hasChangedChildrenInternal)}hasRemovedChildren(){return Boolean(this.hasRemovedChildrenInternal)}}let Zi;class es{static instance(e={forceNew:null}){const{forceNew:t}=e;return Zi&&!t||(Zi=new es),Zi}async render(e){let t=null;if(e instanceof n.DOMModel.DOMNode?t=e:e instanceof n.DOMModel.DeferredDOMNode&&(t=await e.resolvePromise()),!t)return null;const i=new Xi(!1,!0,!0);i.rootDOMNode=t;const s=i.firstChild();return s&&!s.isExpandable()&&i.element.classList.add("single-node"),i.setVisible(!0),i.element.treeElementForTest=s,i.setShowSelectionOnKeyboardFocus(!0,!0),{node:i.element,tree:i}}}class ts extends i.TreeOutline.TreeElement{nodeShortcut;hoveredInternal;constructor(e){super(""),this.listItemElement.createChild("div","selection fill");const t=this.listItemElement.createChild("span","elements-tree-shortcut-title");let n=e.nodeName.toLowerCase();e.nodeType===Node.ELEMENT_NODE&&(n="<"+n+">"),t.textContent="↪ "+n,this.nodeShortcut=e,this.addRevealAdorner()}addRevealAdorner(){const t=new T.Adorner.Adorner;t.classList.add("adorner-reveal");const n=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.REVEAL).name,i=document.createElement("span"),s=new w.Icon.Icon;s.name="select-element";const o=document.createElement("span");o.textContent=n,i.append(s),i.append(o),i.classList.add("adorner-with-icon"),t.data={name:n,content:i,jslogContext:"reveal"},this.listItemElement.appendChild(t);t.addInteraction((()=>{this.nodeShortcut.deferredNode.resolve((t=>{e.Revealer.reveal(t)}))}),{isToggle:!1,shouldPropagateOnKeydown:!1,ariaLabelDefault:Gi($i.reveal),ariaLabelActive:Gi($i.reveal)}),t.addEventListener("mousedown",(e=>e.consume()),!1),Ls.instance().registerAdorner(t)}get hovered(){return Boolean(this.hoveredInternal)}set hovered(e){this.hoveredInternal!==e&&(this.hoveredInternal=e,this.listItemElement.classList.toggle("hovered",e))}deferredNode(){return this.nodeShortcut.deferredNode}domModel(){return this.nodeShortcut.deferredNode.domModel()}setLeftIndentOverlay(){let e=24;if(this.parent&&this.parent instanceof gs){e+=parseFloat(this.parent.listItemElement.style.getPropertyValue("--indent"))||0}this.listItemElement.style.setProperty("--indent",e+"px")}onattach(){this.setLeftIndentOverlay()}onselect(e){if(!e)return!0;return this.nodeShortcut.deferredNode.highlight(),this.nodeShortcut.deferredNode.resolve(function(e){e&&this.treeOutline instanceof Xi&&(this.treeOutline.selectedDOMNodeInternal=e,this.treeOutline.selectedNodeChanged(!1))}.bind(this)),!0}}var ns=Object.freeze({__proto__:null,get ElementsTreeOutline(){return Xi},MappedCharToEntity:Qi,UpdateRecord:Ji,Renderer:es,ShortcutTreeElement:ts});const is={domBreakpoint:"DOM Breakpoint",elementIsHidden:"Element is hidden"},ss=r.i18n.registerUIStrings("panels/elements/MarkerDecorator.ts",is),os=r.i18n.getLazilyComputedLocalizedString.bind(void 0,ss);class rs{title;color;constructor(e){if(!e.title||!e.color)throw new Error(`Generic decorator requires a color and a title: ${e.marker}`);this.title=e.title(),this.color=e.color}decorate(e){return{title:this.title,color:this.color}}}const as={marker:"breakpoint-marker",title:os(is.domBreakpoint),color:"var(--sys-color-primary-bright)"},ls={marker:"hidden-marker",title:os(is.elementIsHidden),color:"var(--sys-color-neutral-bright)"};function ds(){return[{...as,decorator:()=>new rs(as)},{...ls,decorator:()=>new rs(ls)},{decorator:Rs.instance,marker:"pseudo-state-marker",title:void 0,color:void 0}]}var cs=Object.freeze({__proto__:null,GenericDecorator:rs,getRegisteredDecorators:ds});const hs={thisFrameWasIdentifiedAsAnAd:"This frame was identified as an ad frame",forceState:"Force state",useSInTheConsoleToReferToThis:"Use {PH1} in the console to refer to this element.",addAttribute:"Add attribute",editAttribute:"Edit attribute",focus:"Focus",scrollIntoView:"Scroll into view",editText:"Edit text",editAsHtml:"Edit as HTML",cut:"Cut",copy:"Copy",paste:"Paste",copyOuterhtml:"Copy outerHTML",copySelector:"Copy `selector`",copyJsPath:"Copy JS path",copyStyles:"Copy styles",copyXpath:"Copy XPath",copyFullXpath:"Copy full XPath",copyElement:"Copy element",duplicateElement:"Duplicate element",hideElement:"Hide element",deleteElement:"Delete element",expandRecursively:"Expand recursively",collapseChildren:"Collapse children",captureNodeScreenshot:"Capture node screenshot",showFrameDetails:"Show `iframe` details",valueIsTooLargeToEdit:"<value is too large to edit>",children:"Children:",enableGridMode:"Enable grid mode",disableGridMode:"Disable grid mode",enableFlexMode:"Enable flex mode",disableFlexMode:"Disable flex mode",enableScrollSnap:"Enable scroll-snap overlay",disableScrollSnap:"Disable scroll-snap overlay",openMediaPanel:"Jump to Media panel",showPopoverTarget:"Show popover target"},ps=r.i18n.registerUIStrings("panels/elements/ElementsTreeElement.ts",hs),us=r.i18n.getLocalizedString.bind(void 0,ps);function ms(e){return"OPENING_TAG"===e.tagType}class gs extends i.TreeOutline.TreeElement{nodeInternal;treeOutline;gutterContainer;decorationsElement;searchQuery;expandedChildrenLimitInternal;decorationsThrottler;inClipboard;hoveredInternal;editing;htmlEditElement;expandAllButtonElement;selectionElement;hintElement;contentElement;#re=new Map;#ee=new Map;#ae=[];tagTypeContext;constructor(t,n){super(),this.nodeInternal=t,this.treeOutline=null,this.listItemElement.setAttribute("jslog",`${s.treeItem().parent("elementsTreeOutline").track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Backspace|Delete|Enter|Space|Home|End",drag:!0,click:!0})}`),this.contentElement=this.listItemElement.createChild("div"),this.gutterContainer=this.contentElement.createChild("div","gutter-container"),this.gutterContainer.addEventListener("click",this.showContextMenu.bind(this));const o=new w.Icon.Icon;if(o.data={color:"var(--icon-default)",iconName:"dots-horizontal",height:"16px",width:"16px"},this.gutterContainer.append(o),this.decorationsElement=this.gutterContainer.createChild("div","hidden"),this.searchQuery=null,this.expandedChildrenLimitInternal=ys,this.decorationsThrottler=new e.Throttler.Throttler(100),this.inClipboard=!1,this.hoveredInternal=!1,this.editing=null,n)this.tagTypeContext={tagType:"CLOSING_TAG"};else if(this.tagTypeContext={tagType:"OPENING_TAG",adornerContainer:this.contentElement.createChild("div","adorner-container hidden"),adorners:[],styleAdorners:[],adornersThrottler:new e.Throttler.Throttler(100),canAddAttributes:this.nodeInternal.nodeType()===Node.ELEMENT_NODE},this.updateStyleAdorners(),t.isAdFrameNode()){const e=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.AD),t=this.adorn(e);i.Tooltip.Tooltip.install(t,us(hs.thisFrameWasIdentifiedAsAnAd))}this.expandAllButtonElement=null}static animateOnDOMUpdate(e){const t=e.listItemElement.querySelector(".webkit-html-tag-name");i.UIUtils.runCSSAnimationOnce(t||e.listItemElement,"dom-update-highlight")}static visibleShadowRoots(t){let i=t.shadowRoots();return i.length&&!e.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom").get()&&(i=i.filter((function(e){return e.shadowRootType()!==n.DOMModel.DOMNode.ShadowRootTypes.UserAgent}))),i}static canShowInlineText(e){if(e.contentDocument()||e.templateContent()||gs.visibleShadowRoots(e).length||e.hasPseudoElements())return!1;if(e.nodeType()!==Node.ELEMENT_NODE)return!1;if(!e.firstChild||e.firstChild!==e.lastChild||e.firstChild.nodeType()!==Node.TEXT_NODE)return!1;return e.firstChild.nodeValue().length<80}static populateForcedPseudoStateItems(e,t){const n=["active","hover","focus","visited","focus-within","focus-visible"],i=t.domModel().cssModel().pseudoState(t),s=e.debugSection().appendSubMenuItem(us(hs.forceState),!1,"force-state");for(const e of n){const t=!!i&&i.indexOf(e)>=0;s.defaultSection().appendCheckboxItem(":"+e,o.bind(null,e,!t),{checked:t,jslogContext:e})}function o(e,n){t.domModel().cssModel().forcePseudoState(t,e,n)}}isClosingTag(){return!ms(this.tagTypeContext)}node(){return this.nodeInternal}isEditing(){return Boolean(this.editing)}highlightSearchResults(e){this.searchQuery=e,this.editing||this.highlightSearchResultsInternal()}hideSearchHighlights(){N.HighlightManager.HighlightManager.instance().removeHighlights(this.#ae),this.#ae=[]}setInClipboard(e){this.inClipboard!==e&&(this.inClipboard=e,this.listItemElement.classList.toggle("in-clipboard",e))}get hovered(){return this.hoveredInternal}set hovered(e){this.hoveredInternal!==e&&(this.hoveredInternal=e,this.listItemElement&&(e?(this.createSelection(),this.listItemElement.classList.add("hovered")):this.listItemElement.classList.remove("hovered")))}addIssue(e){this.#re.has(e.primaryKey())||(this.#re.set(e.primaryKey(),e),this.#le(e))}#le(e){const t=e.details();t.violatingNodeAttribute?this.#de(t.violatingNodeAttribute,e):this.#ce(e)}get issuesByNodeElement(){return this.#ee}#de(e,t){const n=this.listItemElement.getElementsByClassName("webkit-html-tag")[0].getElementsByClassName("webkit-html-attribute");for(const i of n)if(i.getElementsByClassName("webkit-html-attribute-name")[0].textContent===e){const e=i.getElementsByClassName("webkit-html-attribute-name")[0];e.classList.add("violating-element"),this.#ee.set(e,t)}}#ce(e){const t=this.listItemElement.getElementsByClassName("webkit-html-tag-name")[0];t.classList.add("violating-element"),this.#ee.set(t,e)}expandedChildrenLimit(){return this.expandedChildrenLimitInternal}setExpandedChildrenLimit(e){this.expandedChildrenLimitInternal=e}createSlotLink(t){if(ms(this.tagTypeContext)&&t){const n=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.SLOT);this.tagTypeContext.slot=this.adornSlot(n,this.tagTypeContext);const i=t.deferredNode;this.tagTypeContext.slot.addEventListener("click",(()=>{i.resolve((t=>{e.Revealer.reveal(t)}))})),this.tagTypeContext.slot.addEventListener("mousedown",(e=>e.consume()),!1)}}createSelection(){const e=this.contentElement;e&&(this.selectionElement||(this.selectionElement=document.createElement("div"),this.selectionElement.className="selection fill",this.selectionElement.style.setProperty("margin-left",-this.computeLeftIndent()+"px"),e.prepend(this.selectionElement)))}createHint(){if(this.contentElement&&!this.hintElement){this.hintElement=this.contentElement.createChild("span","selected-hint");const e="$0";i.Tooltip.Tooltip.install(this.hintElement,us(hs.useSInTheConsoleToReferToThis,{PH1:e})),i.ARIAUtils.markAsHidden(this.hintElement)}}onbind(){this.treeOutline&&!this.isClosingTag()&&this.treeOutline.treeElementByNode.set(this.nodeInternal,this)}onunbind(){this.editing&&this.editing.cancel(),this.treeOutline&&this.treeOutline.treeElementByNode.get(this.nodeInternal)===this&&this.treeOutline.treeElementByNode.delete(this.nodeInternal)}onattach(){this.hoveredInternal&&(this.createSelection(),this.listItemElement.classList.add("hovered")),this.updateTitle(),this.listItemElement.draggable=!0}async onpopulate(){if(this.treeOutline)return this.treeOutline.populateTreeElement(this)}async expandRecursively(){await this.nodeInternal.getSubtree(-1,!0),await super.expandRecursively(Number.MAX_VALUE)}onexpand(){this.isClosingTag()||this.updateTitle()}oncollapse(){this.isClosingTag()||this.updateTitle()}select(e,t){return!this.editing&&super.select(e,t)}onselect(e){return!!this.treeOutline&&(this.treeOutline.suppressRevealAndSelect=!0,this.treeOutline.selectDOMNode(this.nodeInternal,e),e&&(this.nodeInternal.highlight(),o.userMetrics.actionTaken(o.UserMetrics.Action.ChangeInspectedNodeInElementsPanel)),this.createSelection(),this.createHint(),this.treeOutline.suppressRevealAndSelect=!1,!0)}ondelete(){if(!this.treeOutline)return!1;const e=this.treeOutline.findTreeElement(this.nodeInternal);return e?e.remove():this.remove(),!0}onenter(){return!this.editing&&(this.startEditing(),!0)}selectOnMouseDown(e){super.selectOnMouseDown(e),this.editing||e.detail>=2&&e.preventDefault()}ondblclick(e){return this.editing||this.isClosingTag()||this.startEditingTarget(e.target)||this.isExpandable()&&!this.expanded&&this.expand(),!1}hasEditableNode(){return!this.nodeInternal.isShadowRoot()&&!this.nodeInternal.ancestorUserAgentShadowRoot()}insertInLastAttributePosition(e,t){if(e.getElementsByClassName("webkit-html-attribute").length>0)e.insertBefore(t,e.lastChild);else if(null!==e.textContent){const n=e.textContent.match(/^<(.*?)>$/);if(!n)return;const s=n[1];e.textContent="",i.UIUtils.createTextChild(e,"<"+s),e.appendChild(t),i.UIUtils.createTextChild(e,">")}}startEditingTarget(e){if(!this.treeOutline||this.treeOutline.selectedDOMNode()!==this.nodeInternal)return!1;if(this.nodeInternal.nodeType()!==Node.ELEMENT_NODE&&this.nodeInternal.nodeType()!==Node.TEXT_NODE)return!1;const t=e.enclosingNodeOrSelfWithClass("webkit-html-text-node");if(t)return this.startEditingTextNode(t);const n=e.enclosingNodeOrSelfWithClass("webkit-html-attribute");if(n)return this.startEditingAttribute(n,e);const i=e.enclosingNodeOrSelfWithClass("webkit-html-tag-name");if(i)return this.startEditingTagName(i);return!!e.enclosingNodeOrSelfWithClass("add-attribute")&&this.addNewAttribute()}showContextMenu(e){this.treeOutline&&this.treeOutline.showContextMenu(this,e)}populateTagContextMenu(e,t){const n=this.isClosingTag()&&this.treeOutline?this.treeOutline.findTreeElement(this.nodeInternal):this;if(!n)return;e.editSection().appendItem(us(hs.addAttribute),n.addNewAttribute.bind(n),{jslogContext:"add-attribute"});const i=t.target,s=i.enclosingNodeOrSelfWithClass("webkit-html-attribute"),o=i.enclosingNodeOrSelfWithClass("add-attribute");s&&!o&&e.editSection().appendItem(us(hs.editAttribute),this.startEditingAttribute.bind(this,s,i),{jslogContext:"edit-attribute"}),this.populateNodeContextMenu(e),gs.populateForcedPseudoStateItems(e,n.node()),this.populateScrollIntoView(e),e.viewSection().appendItem(us(hs.focus),(async()=>{await this.nodeInternal.focus()}),{jslogContext:"focus"})}populatePseudoElementContextMenu(e){0!==this.childCount()&&this.populateExpandRecursively(e),this.populateScrollIntoView(e)}populateExpandRecursively(e){e.viewSection().appendItem(us(hs.expandRecursively),this.expandRecursively.bind(this),{jslogContext:"expand-recursively"})}populateScrollIntoView(e){e.viewSection().appendItem(us(hs.scrollIntoView),(()=>this.nodeInternal.scrollIntoView()),{jslogContext:"scroll-into-view"})}populateTextContextMenu(e,t){this.editing||e.editSection().appendItem(us(hs.editText),this.startEditingTextNode.bind(this,t),{jslogContext:"edit-text"}),this.populateNodeContextMenu(e)}populateNodeContextMenu(t){const s=this.hasEditableNode();s&&!this.editing&&t.editSection().appendItem(us(hs.editAsHtml),this.editAsHTML.bind(this),{jslogContext:"elements.edit-as-html"});const o=this.nodeInternal.isShadowRoot(),r=i.KeyboardShortcut.KeyboardShortcut.shortcutToString.bind(null),a=i.KeyboardShortcut.Modifiers.CtrlOrMeta,l=this.treeOutline;if(!l)return;let d;i.ActionRegistry.ActionRegistry.instance().hasAction("freestyler.element-panel-context")&&t.headerSection().appendAction("freestyler.element-panel-context"),d=t.clipboardSection().appendItem(us(hs.cut),l.performCopyOrCut.bind(l,!0,this.nodeInternal),{disabled:!this.hasEditableNode(),jslogContext:"cut"}),d.setShortcut(r("X",a));const c=t.clipboardSection().appendSubMenuItem(us(hs.copy),!1,"copy"),h=c.section();if(o||(d=h.appendItem(us(hs.copyOuterhtml),l.performCopyOrCut.bind(l,!1,this.nodeInternal),{jslogContext:"copy-outer-html"}),d.setShortcut(r("V",a))),this.nodeInternal.nodeType()===Node.ELEMENT_NODE&&(h.appendItem(us(hs.copySelector),this.copyCSSPath.bind(this),{jslogContext:"copy-selector"}),h.appendItem(us(hs.copyJsPath),this.copyJSPath.bind(this),{disabled:!Li(this.nodeInternal),jslogContext:"copy-js-path"}),h.appendItem(us(hs.copyStyles),this.copyStyles.bind(this),{jslogContext:"elements.copy-styles"})),o||(h.appendItem(us(hs.copyXpath),this.copyXPath.bind(this),{jslogContext:"copy-xpath"}),h.appendItem(us(hs.copyFullXpath),this.copyFullXPath.bind(this),{jslogContext:"copy-full-xpath"})),!o){d=c.clipboardSection().appendItem(us(hs.copyElement),l.performCopyOrCut.bind(l,!1,this.nodeInternal),{jslogContext:"copy-element"}),d.setShortcut(r("C",a));const e=!this.nodeInternal.parentNode||"#document"===this.nodeInternal.parentNode.nodeName();d=t.editSection().appendItem(us(hs.duplicateElement),l.duplicateNode.bind(l,this.nodeInternal),{disabled:this.nodeInternal.isInShadowTree()||e,jslogContext:"elements.duplicate-element"})}d=t.clipboardSection().appendItem(us(hs.paste),l.pasteNode.bind(l,this.nodeInternal),{disabled:!l.canPaste(this.nodeInternal),jslogContext:"paste"}),d.setShortcut(r("V",a)),d=t.debugSection().appendCheckboxItem(us(hs.hideElement),l.toggleHideElement.bind(l,this.nodeInternal),{checked:l.isToggledToHidden(this.nodeInternal),jslogContext:"elements.hide-element"}),d.setShortcut(i.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("elements.hide-element")||""),s&&t.editSection().appendItem(us(hs.deleteElement),this.remove.bind(this),{jslogContext:"delete-element"}),this.populateExpandRecursively(t),t.viewSection().appendItem(us(hs.collapseChildren),this.collapseChildren.bind(this),{jslogContext:"collapse-children"});const p=new P.DeviceModeWrapper.ActionDelegate;t.viewSection().appendItem(us(hs.captureNodeScreenshot),p.handleAction.bind(null,i.Context.Context.instance(),"emulation.capture-node-screenshot"),{jslogContext:"emulation.capture-node-screenshot"}),this.nodeInternal.frameOwnerFrameId()&&t.viewSection().appendItem(us(hs.showFrameDetails),(()=>{const t=this.nodeInternal.frameOwnerFrameId();if(t){const i=n.FrameManager.FrameManager.instance().getFrame(t);e.Revealer.reveal(i)}}),{jslogContext:"show-frame-details"})}startEditing(){if(!this.treeOutline||this.treeOutline.selectedDOMNode()!==this.nodeInternal)return;const e=this.listItemElement;if(ms(this.tagTypeContext)&&this.tagTypeContext.canAddAttributes){const t=e.getElementsByClassName("webkit-html-attribute")[0];return t?this.startEditingAttribute(t,t.getElementsByClassName("webkit-html-attribute-value")[0]):this.addNewAttribute()}if(this.nodeInternal.nodeType()===Node.TEXT_NODE){const t=e.getElementsByClassName("webkit-html-text-node")[0];if(t)return this.startEditingTextNode(t)}}addNewAttribute(){const e=document.createElement("span"),t=this.buildAttributeDOM(e," ","",null);t.style.marginLeft="2px",t.style.marginRight="2px",t.setAttribute("jslog",`${s.value("new-attribute").track({change:!0,resize:!0})}`);const n=this.listItemElement.getElementsByClassName("webkit-html-tag")[0];return this.insertInLastAttributePosition(n,t),t.scrollIntoViewIfNeeded(!0),this.startEditingAttribute(t,t)}triggerEditAttribute(e){const t=this.listItemElement.getElementsByClassName("webkit-html-attribute-name");for(let n=0,i=t.length;n<i;++n)if(t[n].textContent===e)for(let e=t[n].nextSibling;e;e=e.nextSibling)if(e.nodeType===Node.ELEMENT_NODE&&e.classList.contains("webkit-html-attribute-value"))return this.startEditingAttribute(e.parentElement,e)}startEditingAttribute(t,n){if(console.assert(this.listItemElement.isAncestor(t)),i.UIUtils.isBeingEdited(t))return!0;const s=t.getElementsByClassName("webkit-html-attribute-name")[0];if(!s)return!1;const o=s.textContent,r=t.getElementsByClassName("webkit-html-attribute-value")[0];n=r.isAncestor(n)?r:n;const a=o&&r?this.nodeInternal.getAttribute(o)?.replaceAll('"',"&quot;"):void 0;void 0!==a&&r.setTextContentTruncatedIfNeeded(a,us(hs.valueIsTooLargeToEdit)),function e(t){if(t.nodeType!==Node.TEXT_NODE){if(t.nodeType===Node.ELEMENT_NODE)for(let n=t.firstChild;n;n=n.nextSibling)e(n)}else t.nodeValue=t.nodeValue?t.nodeValue.replace(/\u200B/g,""):""}(t);const l=new i.InplaceEditor.Config(this.attributeEditingCommitted.bind(this),this.editingCancelled.bind(this),o||void 0);e.ParsedURL.ParsedURL.fromString(r.textContent||"")||l.setPostKeydownFinishHandler((function(e){return i.UIUtils.handleElementValueModifications(e,t),""})),this.updateEditorHandles(t,l);const d=this.listItemElement.getComponentSelection();return d&&d.selectAllChildren(n),!0}startEditingTextNode(e){if(i.UIUtils.isBeingEdited(e))return!0;let t=this.nodeInternal;t.nodeType()===Node.ELEMENT_NODE&&t.firstChild&&(t=t.firstChild);const n=e.enclosingNodeOrSelfWithClass("webkit-html-text-node");n&&(n.textContent=t.nodeValue());const s=new i.InplaceEditor.Config(this.textNodeEditingCommitted.bind(this,t),this.editingCancelled.bind(this));this.updateEditorHandles(e,s);const o=this.listItemElement.getComponentSelection();return o&&o.selectAllChildren(e),!0}startEditingTagName(e){if(!e&&!(e=this.listItemElement.getElementsByClassName("webkit-html-tag-name")[0]))return!1;const t=e.textContent;if(null!==t&&Ss.has(t.toLowerCase()))return!1;if(i.UIUtils.isBeingEdited(e))return!0;const n=this.distinctClosingTagElement();function s(){n&&e&&(n.textContent="</"+e.textContent+">")}const o=e=>{" "===e.key&&(this.editing&&this.editing.commit(),e.consume(!0))};e.addEventListener("keyup",s,!1),e.addEventListener("keydown",o,!1);const r=new i.InplaceEditor.Config(function(t,n,i,r,a){e&&(e.removeEventListener("keyup",s,!1),e.removeEventListener("keydown",o,!1),this.tagNameEditingCommitted(t,n,i,r,a))}.bind(this),function(t,n){e&&(e.removeEventListener("keyup",s,!1),e.removeEventListener("keydown",o,!1),this.editingCancelled(t,n))}.bind(this),t);this.updateEditorHandles(e,r);const a=this.listItemElement.getComponentSelection();return a&&a.selectAllChildren(e),!0}updateEditorHandles(e,t){const n=i.InplaceEditor.InplaceEditor.startEditing(e,t);this.editing=n?{commit:n.commit,cancel:n.cancel,editor:void 0,resize:()=>{}}:null}async startEditingAsHTML(e,t,n){if(null===n)return;if(this.editing)return;const i=this.convertWhitespaceToEntities(n).text;this.htmlEditElement=document.createElement("div"),this.htmlEditElement.className="source-code elements-tree-editor";let s=this.listItemElement.firstChild;for(;s;)s.style.display="none",s=s.nextSibling;this.childrenListElement&&(this.childrenListElement.style.display="none"),this.listItemElement.append(this.htmlEditElement),this.htmlEditElement.addEventListener("keydown",(e=>{"Escape"===e.key&&e.consume(!0)}));const o=new I.TextEditor.TextEditor(x.EditorState.create({doc:i,extensions:[x.keymap.of([{key:"Mod-Enter",run:()=>(this.editing?.commit(),!0)},{key:"Escape",run:()=>(this.editing?.cancel(),!0)}]),I.Config.baseConfiguration(i),I.Config.closeBrackets.instance(),I.Config.autocompletion.instance(),x.html.html({autoCloseTags:!1,selfClosingTags:!0}),I.Config.domWordWrap.instance(),x.EditorView.theme({"&.cm-editor":{maxHeight:"300px"},".cm-scroller":{overflowY:"auto"}}),x.EditorView.domEventHandlers({focusout:e=>{const t=e.relatedTarget;t&&!t.isSelfOrDescendant(o)&&this.editing&&this.editing.commit()}})]}));function r(){this.treeOutline&&this.htmlEditElement&&(this.htmlEditElement.style.width=this.treeOutline.visibleWidth()-this.computeLeftIndent()-30+"px")}function a(){if(!this.editing||!this.editing.editor)return;this.editing=null,this.htmlEditElement&&this.listItemElement.removeChild(this.htmlEditElement),this.htmlEditElement=void 0,this.childrenListElement&&this.childrenListElement.style.removeProperty("display");let e=this.listItemElement.firstChild;for(;e;)e.style.removeProperty("display"),e=e.nextSibling;this.treeOutline&&(this.treeOutline.setMultilineEditing(null),this.treeOutline.focus()),t()}this.editing={commit:function(){this.editing&&this.editing.editor&&e(i,this.editing.editor.state.doc.toString());a.call(this)}.bind(this),cancel:a.bind(this),editor:o,resize:r.bind(this)},r.call(this),this.htmlEditElement.appendChild(o),o.editor.focus(),this.treeOutline&&this.treeOutline.setMultilineEditing(this.editing)}attributeEditingCommitted(e,t,n,i,s){this.editing=null;const o=this.treeOutline;function r(n){if(n&&this.editingCancelled(e,i),!s)return;o&&(o.runPendingUpdates(),o.focus());const r=this.nodeInternal.attributes();for(let e=0;e<r.length;++e)if(r[e].name===i)return void("backward"===s?0===e?this.startEditingTagName():this.triggerEditAttribute(r[e-1].name):e===r.length-1?this.addNewAttribute():this.triggerEditAttribute(r[e+1].name));"backward"===s?" "===t?r.length>0&&this.triggerEditAttribute(r[r.length-1].name):r.length>1&&this.triggerEditAttribute(r[r.length-2].name):"forward"===s&&(a.StringUtilities.isWhitespace(t)?this.startEditingTagName():this.addNewAttribute())}!i.trim()&&!t.trim()||n===t?(this.updateTitle(),r.call(this)):this.nodeInternal.setAttribute(i,t,r.bind(this))}tagNameEditingCommitted(e,t,n,i,s){this.editing=null;const o=this;function r(){const t=o.distinctClosingTagElement();t&&(t.textContent="</"+i+">"),o.editingCancelled(e,i),a.call(o)}function a(){if("forward"!==s)return void this.addNewAttribute();const e=this.nodeInternal.attributes();e.length>0?this.triggerEditAttribute(e[0].name):this.addNewAttribute()}if((t=t.trim())===n)return void r();const l=this.treeOutline,d=this.expanded;this.nodeInternal.setNodeName(t,((e,t)=>{if(e||!t)return void r();if(!l)return;const n=l.selectNodeAfterEdit(d,e,t);a.call(n)}))}textNodeEditingCommitted(e,t,n){this.editing=null,e.setNodeValue(n,function(){this.updateTitle()}.bind(this))}editingCancelled(e,t){this.editing=null,this.updateTitle()}distinctClosingTagElement(){if(this.expanded){const e=this.childrenListElement.querySelectorAll(".close");return e[e.length-1]}const e=this.listItemElement.getElementsByClassName("webkit-html-tag");return 1===e.length?null:e[e.length-1]}updateTitle(e){if(this.editing)return;const t=this.nodeTitleInfo(e||null);if(this.nodeInternal.nodeType()===Node.DOCUMENT_FRAGMENT_NODE&&this.nodeInternal.isInShadowTree()&&this.nodeInternal.shadowRootType()){this.childrenListElement.classList.add("shadow-root");let e=4;for(let t=this.nodeInternal;e&&t;t=t.parentNode)t.nodeType()===Node.DOCUMENT_FRAGMENT_NODE&&e--;e?this.childrenListElement.classList.add("shadow-root-depth-"+e):this.childrenListElement.classList.add("shadow-root-deep")}this.contentElement.removeChildren();this.contentElement.createChild("span","highlight").append(t),this.title=this.contentElement,this.updateDecorations(),this.contentElement.prepend(this.gutterContainer),ms(this.tagTypeContext)&&(this.contentElement.append(this.tagTypeContext.adornerContainer),this.tagTypeContext.slot&&this.contentElement.append(this.tagTypeContext.slot)),delete this.selectionElement,delete this.hintElement,this.selected&&(this.createSelection(),this.createHint());for(const e of this.#re.values())this.#le(e);this.highlightSearchResultsInternal()}computeLeftIndent(){let e=this.parent,t=0;for(;null!==e;)t++,e=e.parent;return 12*(t-2)+(this.isExpandable()&&this.isCollapsible()?1:12)}updateDecorations(){const e=this.computeLeftIndent();this.gutterContainer.style.left=-e+"px",this.listItemElement.style.setProperty("--indent",e+"px"),this.isClosingTag()||this.nodeInternal.nodeType()===Node.ELEMENT_NODE&&this.decorationsThrottler.schedule(this.updateDecorationsInternal.bind(this))}updateDecorationsInternal(){if(!this.treeOutline)return Promise.resolve();const e=this.nodeInternal;this.treeOutline.decoratorExtensions||(this.treeOutline.decoratorExtensions=ds());const t=new Map;for(const e of this.treeOutline.decoratorExtensions)t.set(e.marker,e);const n=[],s=[],o=[];function r(t,n){const i=n.decorate(t);i&&(t===e?s:o).push(i)}return e.traverseMarkers((function(e,i){const s=t.get(i);if(!s)return;n.push(Promise.resolve(s.decorator()).then(r.bind(null,e)))})),Promise.all(n).then(function(){if(this.decorationsElement.removeChildren(),this.decorationsElement.classList.add("hidden"),this.gutterContainer.classList.toggle("has-decorations",Boolean(s.length||o.length)),i.ARIAUtils.setLabel(this.decorationsElement,""),!s.length&&!o.length)return;const e=new Set,t=document.createElement("div");for(const n of s){t.createChild("div").textContent=n.title,e.add(n.color)}if(this.expanded&&!s.length)return;const n=new Set;if(o.length){let e=t.createChild("div");e.textContent=us(hs.children);for(const i of o)e=t.createChild("div"),e.style.marginLeft="15px",e.textContent=i.title,n.add(i.color)}let r=0;a.call(this,e,"elements-gutter-decoration"),this.expanded||a.call(this,n,"elements-gutter-decoration elements-has-decorated-children");function a(e,t){for(const n of e){const e=this.decorationsElement.createChild("div",t);this.decorationsElement.classList.remove("hidden"),e.style.backgroundColor=n,e.style.borderColor=n,r&&(e.style.marginLeft=r+"px"),r+=3}}i.Tooltip.Tooltip.install(this.decorationsElement,t.textContent),i.ARIAUtils.setLabel(this.decorationsElement,t.textContent||"")}.bind(this))}buildAttributeDOM(e,t,n,o,r,l){const d=/[\/;:\)\]\}]/g;let c=0,h=0,p=0;function u(e,t){const n=this.convertWhitespaceToEntities(t);for(h=n.entityRanges.length,t=n.text.replace(d,((e,t)=>{for(;c<h&&n.entityRanges[c].offset<t;)n.entityRanges[c].offset+=p,++c;return p+=1,e+"​"}));c<h;)n.entityRanges[c].offset+=p,++c;e.setTextContentTruncatedIfNeeded(t),i.UIUtils.highlightRangesWithStyleClass(e,n.entityRanges,"webkit-html-entity-value")}const m=r||n.length>0,g=e.createChild("span","webkit-html-attribute");g.setAttribute("jslog",`${s.value("style"===t?"style-attribute":"attribute").track({change:!0,dblclick:!0})}`);const y=g.createChild("span","webkit-html-attribute-name");y.textContent=t,m&&i.UIUtils.createTextChild(g,'=​"');const S=g.createChild("span","webkit-html-attribute-value");function C(e){const t=l?l.resolveURL(e):null;if(null===t){const t=document.createElement("span");return u.call(this,t,e),t}(e=e.replace(d,"$&​")).startsWith("data:")&&(e=a.StringUtilities.trimMiddle(e,60));const n=l&&"a"===l.nodeName().toLowerCase()?i.XLink.XLink.create(t,e,"",!0,"image-url"):f.Linkifier.Linkifier.linkifyURL(t,{text:e,preventClick:!0,showColumnNumber:!1,inlineFrameIndex:0});return ie.setImageUrl(n,t)}o&&o.isAttributeModified(t)&&i.UIUtils.runCSSAnimationOnce(m?S:y,"dom-update-highlight");const E=l?l.nodeName().toLowerCase():"";if(E&&("src"===t||"href"===t)&&n?S.appendChild(C.call(this,n)):("img"!==E&&"source"!==E||"srcset"!==t)&&("image"!==E||"xlink:href"!==t&&"href"!==t)?u.call(this,S,n):S.appendChild(b.call(this,n)),"popovertarget"===t){const e=n?S:y;this.linkifyElementByRelation(e,"PopoverTarget",us(hs.showPopoverTarget))}function b(e){const t=document.createDocumentFragment();let n=0;for(;e.length;){n++>0&&i.UIUtils.createTextChild(t," ");let s="",o="";const r=(e=e.trim()).search(/\s/);if(-1===r)s=e;else if(r>0&&","===e[r-1])s=e.substring(0,r);else{s=e.substring(0,r);const t=e.indexOf(",",r);o=-1!==t?e.substring(r,t+1):e.substring(r)}s&&(s.endsWith(",")?(t.appendChild(C.call(this,s.substring(0,s.length-1))),i.UIUtils.createTextChild(t,",")):t.appendChild(C.call(this,s))),o&&i.UIUtils.createTextChild(t,o),e=e.substring(s.length+o.length)}return t}return m&&i.UIUtils.createTextChild(g,'"'),g}async linkifyElementByRelation(t,n,i){const s=await this.nodeInternal.domModel().getElementByRelation(this.nodeInternal.id,n),o=this.nodeInternal.domModel().nodeForId(s);if(!o)return;const r=await e.Linkifier.Linkifier.linkify(o,{preventKeyboardFocus:!0,tooltip:i,textContent:t.textContent||void 0,isDynamicLink:!0});t.removeChildren(),t.append(r)}buildPseudoElementDOM(e,t){e.createChild("span","webkit-html-pseudo-element").textContent="::"+t,i.UIUtils.createTextChild(e,"​")}buildTagDOM(e,t,n,o,r){const a=this.nodeInternal,l=["webkit-html-tag"];n&&o&&l.push("close");const d=e.createChild("span",l.join(" "));i.UIUtils.createTextChild(d,"<");const c=d.createChild("span",n?"webkit-html-close-tag-name":"webkit-html-tag-name");if(n||c.setAttribute("jslog",`${s.value("tag-name").track({change:!0,dblclick:!0})}`),c.textContent=(n?"/":"")+t,!n){if(a.hasAttributes()){const e=a.attributes();for(let t=0;t<e.length;++t){const n=e[t];i.UIUtils.createTextChild(d," "),this.buildAttributeDOM(d,n.name,n.value,r,!1,a)}}if(r){let e=r.hasRemovedAttributes()||r.hasRemovedChildren();e=e||!this.expanded&&r.hasChangedChildren(),e&&i.UIUtils.runCSSAnimationOnce(c,"dom-update-highlight")}}i.UIUtils.createTextChild(d,">"),i.UIUtils.createTextChild(e,"​"),d.textContent&&i.ARIAUtils.setLabel(d,d.textContent)}convertWhitespaceToEntities(e){let t="",n=0;const i=[],s=Qi;for(let o=0,r=e.length;o<r;++o){const r=e.charAt(o);if(s.has(r)){t+=e.substring(n,o);const a="&"+s.get(r)+";";i.push(new S.TextRange.SourceRange(t.length,a.length)),t+=a,n=o+1}}return t&&(t+=e.substring(n)),{text:t||e,entityRanges:i}}nodeTitleInfo(e){const t=this.nodeInternal,n=document.createDocumentFragment(),o=()=>{this.highlightSearchResultsInternal()};switch(t.nodeType()){case Node.ATTRIBUTE_NODE:this.buildAttributeDOM(n,t.name,t.value,e,!0);break;case Node.ELEMENT_NODE:{let o=t.pseudoType();if(o){const e=t.pseudoIdentifier();e&&(o+=`(${e})`),this.buildPseudoElementDOM(n,o);break}const r=t.nodeNameInCorrectCase();if(this.isClosingTag()){this.buildTagDOM(n,r,!0,!0,e);break}if(this.buildTagDOM(n,r,!1,!1,e),this.isExpandable()){if(!this.expanded){const t=new p.ElementsTreeExpandButton.ElementsTreeExpandButton;t.data={clickHandler:()=>this.expand()},n.appendChild(t);const s=document.createElement("span");s.textContent="…",s.style.fontSize="0",n.appendChild(s),i.UIUtils.createTextChild(n,"​"),this.buildTagDOM(n,r,!0,!1,e)}break}if(gs.canShowInlineText(t)){const o=n.createChild("span","webkit-html-text-node");o.setAttribute("jslog",`${s.value("text-node").track({change:!0,dblclick:!0})}`);const l=t.firstChild;if(!l)throw new Error("ElementsTreeElement._nodeTitleInfo expects node.firstChild to be defined.");const d=this.convertWhitespaceToEntities(l.nodeValue());o.textContent=a.StringUtilities.collapseWhitespace(d.text),i.UIUtils.highlightRangesWithStyleClass(o,d.entityRanges,"webkit-html-entity-value"),i.UIUtils.createTextChild(n,"​"),this.buildTagDOM(n,r,!0,!1,e),e&&e.hasChangedChildren()&&i.UIUtils.runCSSAnimationOnce(o,"dom-update-highlight"),e&&e.isCharDataModified()&&i.UIUtils.runCSSAnimationOnce(o,"dom-update-highlight");break}(this.treeOutline&&this.treeOutline.isXMLMimeType||!fs.has(r))&&this.buildTagDOM(n,r,!0,!1,e);break}case Node.TEXT_NODE:if(t.parentNode&&"script"===t.parentNode.nodeName().toLowerCase()){const e=n.createChild("span","webkit-html-text-node webkit-html-js-node");e.setAttribute("jslog",`${s.value("script-text-node").track({change:!0,dblclick:!0})}`);const i=t.nodeValue();e.textContent=i.replace(/^[\n\r]+|\s+$/g,""),M.CodeHighlighter.highlightNode(e,"text/javascript").then(o)}else if(t.parentNode&&"style"===t.parentNode.nodeName().toLowerCase()){const e=n.createChild("span","webkit-html-text-node webkit-html-css-node");e.setAttribute("jslog",`${s.value("css-text-node").track({change:!0,dblclick:!0})}`);const i=t.nodeValue();e.textContent=i.replace(/^[\n\r]+|\s+$/g,""),M.CodeHighlighter.highlightNode(e,"text/css").then(o)}else{i.UIUtils.createTextChild(n,'"');const o=n.createChild("span","webkit-html-text-node");o.setAttribute("jslog",`${s.value("text-node").track({change:!0,dblclick:!0})}`);const r=this.convertWhitespaceToEntities(t.nodeValue());o.textContent=a.StringUtilities.collapseWhitespace(r.text),i.UIUtils.highlightRangesWithStyleClass(o,r.entityRanges,"webkit-html-entity-value"),i.UIUtils.createTextChild(n,'"'),e&&e.isCharDataModified()&&i.UIUtils.runCSSAnimationOnce(o,"dom-update-highlight")}break;case Node.COMMENT_NODE:{const e=n.createChild("span","webkit-html-comment");i.UIUtils.createTextChild(e,"\x3c!--"+t.nodeValue()+"--\x3e");break}case Node.DOCUMENT_TYPE_NODE:{const e=n.createChild("span","webkit-html-doctype");i.UIUtils.createTextChild(e,"<!DOCTYPE "+t.nodeName()),t.publicId?(i.UIUtils.createTextChild(e,' PUBLIC "'+t.publicId+'"'),t.systemId&&i.UIUtils.createTextChild(e,' "'+t.systemId+'"')):t.systemId&&i.UIUtils.createTextChild(e,' SYSTEM "'+t.systemId+'"'),t.internalSubset&&i.UIUtils.createTextChild(e," ["+t.internalSubset+"]"),i.UIUtils.createTextChild(e,">");break}case Node.CDATA_SECTION_NODE:{const e=n.createChild("span","webkit-html-text-node");i.UIUtils.createTextChild(e,"<![CDATA["+t.nodeValue()+"]]>");break}case Node.DOCUMENT_NODE:{const e=n.createChild("span");i.UIUtils.createTextChild(e,"#document (");const s=t.documentURL;e.appendChild(f.Linkifier.Linkifier.linkifyURL(s,{text:s,preventClick:!0,showColumnNumber:!1,inlineFrameIndex:0})),i.UIUtils.createTextChild(e,")");break}case Node.DOCUMENT_FRAGMENT_NODE:n.createChild("span","webkit-html-fragment").textContent=a.StringUtilities.collapseWhitespace(t.nodeNameInCorrectCase());break;default:{const e=a.StringUtilities.collapseWhitespace(t.nodeNameInCorrectCase());i.UIUtils.createTextChild(n,e)}}return n}remove(){if(this.nodeInternal.pseudoType())return;this.parent&&this.nodeInternal.parentNode&&this.nodeInternal.parentNode.nodeType()!==Node.DOCUMENT_NODE&&this.nodeInternal.removeNode()}toggleEditAsHTML(e,t){if(this.editing&&this.htmlEditElement)return void this.editing.commit();if(!1===t)return;function n(t){e&&e(!t)}const i=this.nodeInternal;i.getOuterHTML().then(this.startEditingAsHTML.bind(this,(function(e,t){e!==t&&i.setOuterHTML(t,n)}),(function(){e&&e(!1)})))}copyCSSPath(){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(Oi(this.nodeInternal,!0))}copyJSPath(){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(Ai(this.nodeInternal,!0))}copyXPath(){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(Ri(this.nodeInternal,!0))}copyFullXPath(){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(Ri(this.nodeInternal,!1))}async copyStyles(){const t=this.nodeInternal,i=t.domModel().cssModel(),s=await i.cachedMatchedCascadeForNode(t);if(!s)return;const r=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),a=[];for(const e of s.nodeStyles().reverse())for(const t of e.leadingProperties())t.parsedOk&&!t.disabled&&t.activeInStyle()&&!t.implicit&&(s.isInherited(e)&&!n.CSSMetadata.cssMetadata().isPropertyInherited(t.name)||e.parentRule&&e.parentRule.isUserAgent()||"Active"===s.propertyState(t)&&a.push(`${r}${t.name}: ${t.value};`));o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(a.join("\n"))}highlightSearchResultsInternal(){if(this.hideSearchHighlights(),!this.searchQuery)return;const e=this.listItemElement.textContent||"",t=a.StringUtilities.createPlainTextSearchRegex(this.searchQuery,"gi"),n=[];let i=t.exec(e);for(;i;)n.push(new S.TextRange.SourceRange(i.index,i[0].length)),i=t.exec(e);n.length||n.push(new S.TextRange.SourceRange(0,e.length)),this.#ae=N.HighlightManager.HighlightManager.instance().highlightOrderedTextRanges(this.listItemElement,n)}editAsHTML(){e.Revealer.reveal(this.node()).then((()=>i.ActionRegistry.ActionRegistry.instance().getAction("elements.edit-as-html").execute()))}adorn({name:e},t){let n=t;n||(n=document.createElement("span"),n.textContent=e);const i=new T.Adorner.Adorner;return i.data={name:e,content:n,jslogContext:e},ms(this.tagTypeContext)&&(this.tagTypeContext.adorners.push(i),Ls.instance().registerAdorner(i),this.updateAdorners(this.tagTypeContext)),i}adornSlot({name:e},t){const n=new w.Icon.Icon;n.name="select-element";const i=document.createElement("span");i.textContent=e;const s=document.createElement("span");s.append(n),s.append(i),s.classList.add("adorner-with-icon");const o=new T.Adorner.Adorner;return o.data={name:e,content:s,jslogContext:"slot"},t.adorners.push(o),Ls.instance().registerAdorner(o),this.updateAdorners(t),o}adornMedia({name:e}){const t=document.createElement("span");t.textContent=e,t.classList.add("adorner-with-icon");const n=new w.Icon.Icon;n.name="select-element",t.append(n);const i=new T.Adorner.Adorner;return i.data={name:e,content:t,jslogContext:"media"},ms(this.tagTypeContext)&&(this.tagTypeContext.adorners.push(i),Ls.instance().registerAdorner(i),this.updateAdorners(this.tagTypeContext)),i}removeAdorner(e,t){const n=t.adorners;Ls.instance().deregisterAdorner(e),e.remove();for(let i=0;i<n.length;++i)if(n[i]===e)return n.splice(i,1),void this.updateAdorners(t)}removeAllAdorners(){if(ms(this.tagTypeContext)){for(const e of this.tagTypeContext.adorners)Ls.instance().deregisterAdorner(e),e.remove();this.tagTypeContext.adorners=[],this.updateAdorners(this.tagTypeContext)}}updateAdorners(e){e.adornersThrottler.schedule(this.updateAdornersInternal.bind(null,e))}updateAdornersInternal(e){const t=e.adornerContainer;if(!t)return Promise.resolve();const n=e.adorners;if(0===n.length)return t.classList.add("hidden"),Promise.resolve();n.sort(Cs),t.removeChildren();for(const e of n)t.appendChild(e);return t.classList.remove("hidden"),Promise.resolve()}async updateStyleAdorners(){if(!ms(this.tagTypeContext))return;const e=this.node(),t=e.id;if(e.nodeType()===Node.COMMENT_NODE||e.nodeType()===Node.DOCUMENT_FRAGMENT_NODE||e.nodeType()===Node.TEXT_NODE||void 0===t)return;const i=await e.domModel().cssModel().getComputedStyle(t);for(const e of this.tagTypeContext.styleAdorners)this.removeAdorner(e,this.tagTypeContext);if(this.tagTypeContext.styleAdorners=[],!i)return;const s=i.get("display"),o="grid"===s||"inline-grid"===s,r="flex"===s||"inline-flex"===s,a=(o&&(i.get("grid-template-columns")?.startsWith("subgrid")||i.get("grid-template-rows")?.startsWith("subgrid")))??!1,l=i.get("container-type"),d=i.get("contain"),c=""!==n.CSSContainerQuery.getQueryAxis(`${l} ${d}`);o&&this.pushGridAdorner(this.tagTypeContext,a),r&&this.pushFlexAdorner(this.tagTypeContext),i.get("scroll-snap-type")&&"none"!==i.get("scroll-snap-type")&&this.pushScrollSnapAdorner(this.tagTypeContext),c&&this.pushContainerAdorner(this.tagTypeContext),e.isMediaNode()&&this.pushMediaAdorner(this.tagTypeContext)}pushGridAdorner(e,t){const n=this.node(),i=n.id;if(!i)return;const s=p.AdornerManager.getRegisteredAdorner(t?p.AdornerManager.RegisteredAdorners.SUBGRID:p.AdornerManager.RegisteredAdorners.GRID),o=this.adorn(s);o.classList.add("grid");o.addInteraction((()=>{o.isActive()?n.domModel().overlayModel().highlightGridInPersistentOverlay(i):n.domModel().overlayModel().hideGridInPersistentOverlay(i)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:us(hs.enableGridMode),ariaLabelActive:us(hs.disableGridMode)}),n.domModel().overlayModel().addEventListener("PersistentGridOverlayStateChanged",(e=>{const{nodeId:t,enabled:n}=e.data;t===i&&o.toggle(n)})),e.styleAdorners.push(o),n.domModel().overlayModel().isHighlightedGridInPersistentOverlay(i)&&o.toggle(!0)}pushScrollSnapAdorner(e){const t=this.node(),n=t.id;if(!n)return;const i=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.SCROLL_SNAP),s=this.adorn(i);s.classList.add("scroll-snap");s.addInteraction((()=>{const e=t.domModel().overlayModel();s.isActive()?e.highlightScrollSnapInPersistentOverlay(n):e.hideScrollSnapInPersistentOverlay(n)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:us(hs.enableScrollSnap),ariaLabelActive:us(hs.disableScrollSnap)}),t.domModel().overlayModel().addEventListener("PersistentScrollSnapOverlayStateChanged",(e=>{const{nodeId:t,enabled:i}=e.data;t===n&&s.toggle(i)})),e.styleAdorners.push(s),t.domModel().overlayModel().isHighlightedScrollSnapInPersistentOverlay(n)&&s.toggle(!0)}pushFlexAdorner(e){const t=this.node(),n=t.id;if(!n)return;const i=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.FLEX),s=this.adorn(i);s.classList.add("flex");s.addInteraction((()=>{const e=t.domModel().overlayModel();s.isActive()?e.highlightFlexContainerInPersistentOverlay(n):e.hideFlexContainerInPersistentOverlay(n)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:us(hs.enableFlexMode),ariaLabelActive:us(hs.disableFlexMode)}),t.domModel().overlayModel().addEventListener("PersistentFlexContainerOverlayStateChanged",(e=>{const{nodeId:t,enabled:i}=e.data;t===n&&s.toggle(i)})),e.styleAdorners.push(s),t.domModel().overlayModel().isHighlightedFlexContainerInPersistentOverlay(n)&&s.toggle(!0)}pushContainerAdorner(e){const t=this.node(),n=t.id;if(!n)return;const i=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.CONTAINER),s=this.adorn(i);s.classList.add("container");s.addInteraction((()=>{const e=t.domModel().overlayModel();s.isActive()?e.highlightContainerQueryInPersistentOverlay(n):e.hideContainerQueryInPersistentOverlay(n)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:us(hs.enableScrollSnap),ariaLabelActive:us(hs.disableScrollSnap)}),t.domModel().overlayModel().addEventListener("PersistentContainerQueryOverlayStateChanged",(e=>{const{nodeId:t,enabled:i}=e.data;t===n&&s.toggle(i)})),e.styleAdorners.push(s),t.domModel().overlayModel().isHighlightedContainerQueryInPersistentOverlay(n)&&s.toggle(!0)}pushMediaAdorner(e){if(!this.node().id)return;const t=p.AdornerManager.getRegisteredAdorner(p.AdornerManager.RegisteredAdorners.MEDIA),n=this.adornMedia(t);n.classList.add("media");n.addInteraction((()=>{i.ViewManager.ViewManager.instance().showView("medias")}),{isToggle:!1,shouldPropagateOnKeydown:!1,ariaLabelDefault:us(hs.openMediaPanel),ariaLabelActive:us(hs.openMediaPanel)}),e.styleAdorners.push(n)}}const ys=500,fs=new Set(["area","base","basefont","br","canvas","col","command","embed","frame","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr"]),Ss=new Set(["html","head","body"]);function Cs(e,t){const n=p.AdornerManager.compareAdornerNamesByCategory(t.name,t.name);return 0===n?e.name.localeCompare(t.name):n}s.registerParentProvider("elementsTreeOutline",(function(e){const t=i.TreeOutline.TreeElement.getTreeElementBylistItemNode(e);return t?.treeOutline?.contentElement}));var Es=Object.freeze({__proto__:null,ElementsTreeElement:gs,InitialChildrenLimit:ys,ForbiddenClosingTagElements:fs,EditTagBlocklist:Ss,adornerComparator:Cs});class bs{throttler;treeOutline;currentHighlightedElement;alreadyExpandedParentElement;pendingHighlightNode;isModifyingTreeOutline;constructor(e,t){this.throttler=t,this.treeOutline=e,this.treeOutline.addEventListener(i.TreeOutline.Events.ElementExpanded,this.clearState,this),this.treeOutline.addEventListener(i.TreeOutline.Events.ElementCollapsed,this.clearState,this),this.treeOutline.addEventListener(Xi.Events.SelectedNodeChanged,this.clearState,this),n.TargetManager.TargetManager.instance().addModelListener(n.OverlayModel.OverlayModel,"HighlightNodeRequested",this.highlightNode,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.OverlayModel.OverlayModel,"InspectModeWillBeToggled",this.clearState,this,{scoped:!0}),this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,this.pendingHighlightNode=null,this.isModifyingTreeOutline=!1}highlightNode(t){if(!e.Settings.Settings.instance().moduleSetting("highlight-node-on-hover-in-overlay").get())return;const n=t.data;this.throttler.schedule((async()=>{this.highlightNodeInternal(this.pendingHighlightNode),this.pendingHighlightNode=null})),this.pendingHighlightNode=this.treeOutline===Xi.forDOMModel(n.domModel())?n:null}highlightNodeInternal(e){this.isModifyingTreeOutline=!0;let t=null;if(this.currentHighlightedElement){let e=this.currentHighlightedElement;for(;e&&e!==this.alreadyExpandedParentElement;){e.expanded&&e.collapse();const t=e.parent;e=t instanceof gs?t:null}}if(this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,e){let n=e;const i=this.treeOutline.treeElementByNode,s=e=>{const t=i.get(e);return!t||!t.expanded};for(;n&&s(n);)n=n.parentNode;this.alreadyExpandedParentElement=n?i.get(n):this.treeOutline.rootElement(),t=this.treeOutline.createTreeElementFor(e)}this.currentHighlightedElement=t,this.treeOutline.setHoverEffect(t),t&&t.reveal(!0),this.isModifyingTreeOutline=!1}clearState(){this.isModifyingTreeOutline||(this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,this.pendingHighlightNode=null)}}var vs=Object.freeze({__proto__:null,ElementsTreeElementHighlighter:bs});const ws=new CSSStyleSheet;ws.replaceSync(".metrics{padding:8px;font-size:10px;text-align:center;white-space:nowrap;min-height:var(--metrics-height);display:flex;flex-direction:column;align-items:center;justify-content:center;--override-box-model-separator-color:var(--ref-palette-neutral0);--override-box-model-text-color:var(--ref-palette-neutral10)}:host{--metrics-height:190px;height:var(--metrics-height);contain:strict}:host(.invisible){visibility:hidden;height:0}:host(.collapsed){visibility:collapse;height:0}:host-context(.platform-windows){--metrics-height:214px}.metrics .label{position:absolute;font-size:10px;left:4px}.metrics .position{border:1px var(--sys-color-token-subtle) dotted;background-color:var(--sys-color-cdt-base-container);display:inline-block;text-align:center;padding:3px;margin:3px;position:relative}.metrics .margin{border:1px dashed var(--override-box-model-separator-color);background-color:var(--sys-color-cdt-base-container);display:inline-block;text-align:center;vertical-align:middle;padding:3px 6px;margin:3px;position:relative}.metrics .border{border:1px solid var(--override-box-model-separator-color);background-color:var(--sys-color-cdt-base-container);display:inline-block;text-align:center;vertical-align:middle;padding:3px 6px;margin:3px;position:relative}.metrics .padding{border:1px dashed var(--override-box-model-separator-color);background-color:var(--sys-color-cdt-base-container);display:inline-block;text-align:center;vertical-align:middle;padding:3px 6px;margin:3px;position:relative;min-width:120px}.metrics .content{position:static;border:1px solid var(--override-box-model-separator-color);background-color:var(--sys-color-cdt-base-container);display:inline-block;text-align:center;vertical-align:middle;padding:3px;margin:3px;min-width:80px;overflow:visible}.metrics .content span{display:inline-block}.metrics .editing{position:relative;z-index:100;cursor:text}.metrics .left{display:inline-block;vertical-align:middle}.metrics .right{display:inline-block;vertical-align:middle}.metrics .top{display:inline-block}.metrics .bottom{display:inline-block}:host-context(.theme-with-dark-background) .margin:hover,\n:host-context(.theme-with-dark-background) .margin:hover *{border-color:var(--sys-color-token-subtle)}.metrics .highlighted:not(.position) > *:not(.border):not(.padding):not(.content){color:var(--override-box-model-text-color)}\n/*# sourceURL=metricsSidebarPane.css */\n");class xs extends Qe{originalPropertyData;previousPropertyDataCandidate;inlineStyle;highlightMode;boxElements;isEditingMetrics;constructor(){super(),this.originalPropertyData=null,this.previousPropertyDataCandidate=null,this.inlineStyle=null,this.highlightMode="",this.boxElements=[],this.contentElement.setAttribute("jslog",`${s.pane("styles-metrics")}`)}doUpdate(){if(this.isEditingMetrics)return Promise.resolve();const e=this.node(),t=this.cssModel();if(!e||e.nodeType()!==Node.ELEMENT_NODE||!t)return this.contentElement.removeChildren(),this.element.classList.add("collapsed"),Promise.resolve();if(!e.id)return Promise.resolve();const n=[t.getComputedStyle(e.id).then(function(t){t&&this.node()===e&&this.updateMetrics(t)}.bind(this)),t.getInlineStyles(e.id).then((t=>{t&&this.node()===e&&(this.inlineStyle=t.inlineStyle)}))];return Promise.all(n)}onCSSModelChanged(){this.update()}toggleVisibility(e){this.element.classList.toggle("invisible",!e)}getPropertyValueAsPx(e,t){const n=e.get(t);return n?Number(n.replace(/px$/,"")||0):0}getBox(e,t){const n="border"===t?"-width":"";return{left:this.getPropertyValueAsPx(e,t+"-left"+n),top:this.getPropertyValueAsPx(e,t+"-top"+n),right:this.getPropertyValueAsPx(e,t+"-right"+n),bottom:this.getPropertyValueAsPx(e,t+"-bottom"+n)}}highlightDOMNode(e,t,i){i.consume();const s=this.node();if(e&&s){if(this.highlightMode===t)return;this.highlightMode=t,s.highlight(t)}else this.highlightMode="",n.OverlayModel.OverlayModel.hideDOMNodeHighlight();for(const{element:e,name:n,backgroundColor:i}of this.boxElements){const o=!s||"all"===t||n===t;e.style.backgroundColor=o?i:"",e.classList.toggle("highlighted",o)}}updateMetrics(t){const n=document.createElement("div");n.className="metrics";const i=this;function o(e,t,n,i){const o=document.createElement("div");o.className=n;const r=("position"!==t?t+"-":"")+n+i;let l=e.get(r);return void 0===l||((""===l||"position"!==t&&"0px"===l||"position"===t&&"auto"===l)&&(l="‒"),l=l.replace(/px$/,""),l=a.NumberUtilities.toFixedIfFloating(l),o.textContent=l,o.setAttribute("jslog",`${s.value(r).track({dblclick:!0,keydown:"Enter|Escape|ArrowUp|ArrowDown|PageUp|PageDown",change:!0})}`),o.addEventListener("dblclick",this.startEditing.bind(this,o,t,r,e),!1)),o}function r(e){let t=e.get("width");if(!t)return"";t=t.replace(/px$/,"");const n=Number(t);if(!isNaN(n)&&"border-box"===e.get("box-sizing")){const s=i.getBox(e,"border"),o=i.getBox(e,"padding");t=(n-s.left-s.right-o.left-o.right).toString()}return a.NumberUtilities.toFixedIfFloating(t)}function l(e){let t=e.get("height");if(!t)return"";t=t.replace(/px$/,"");const n=Number(t);if(!isNaN(n)&&"border-box"===e.get("box-sizing")){const s=i.getBox(e,"border"),o=i.getBox(e,"padding");t=(n-s.top-s.bottom-o.top-o.bottom).toString()}return a.NumberUtilities.toFixedIfFloating(t)}const d=new Set(["table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group"]),c=new Set(["table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group"]),h=new Set(["static"]),p=["content","padding","border","margin","position"],u=[e.Color.PageHighlight.Content,e.Color.PageHighlight.Padding,e.Color.PageHighlight.Border,e.Color.PageHighlight.Margin,e.Color.Legacy.fromRGBA([0,0,0,0])],m=["content","padding","border","margin","position"];let g=null;this.boxElements=[];for(let e=0;e<p.length;++e){const n=p[e],i=t.get("display"),a=t.get("position");if(!i||!a)continue;if("margin"===n&&d.has(i))continue;if("padding"===n&&c.has(i))continue;if("position"===n&&h.has(a))continue;const y=document.createElement("div");y.className=`${n} highlighted`;const f=u[e].asString("rgba")||"";if(y.style.backgroundColor=f,y.setAttribute("jslog",`${s.metricsBox().context(n).track({hover:!0})}`),y.addEventListener("mouseover",this.highlightDOMNode.bind(this,!0,"position"===n?"all":n),!1),this.boxElements.push({element:y,name:n,backgroundColor:f}),"content"===n){const e=document.createElement("span");e.textContent=r(t),e.addEventListener("dblclick",this.startEditing.bind(this,e,"width","width",t),!1),e.setAttribute("jslog",`${s.value("width").track({dblclick:!0,keydown:"Enter|Escape|ArrowUp|ArrowDown|PageUp|PageDown",change:!0})}`);const n=document.createElement("span");n.textContent=l(t),n.addEventListener("dblclick",this.startEditing.bind(this,n,"height","height",t),!1),n.setAttribute("jslog",`${s.value("height").track({dblclick:!0,keydown:"Enter|Escape|ArrowUp|ArrowDown|PageUp|PageDown",change:!0})}`);const i=document.createElement("span");i.textContent=" × ",y.appendChild(e),y.appendChild(i),y.appendChild(n)}else{const i="border"===n?"-width":"",s=document.createElement("div");s.className="label",s.textContent=m[e],y.appendChild(s),y.appendChild(o.call(this,t,n,"top",i)),y.appendChild(document.createElement("br")),y.appendChild(o.call(this,t,n,"left",i)),g&&y.appendChild(g),y.appendChild(o.call(this,t,n,"right",i)),y.appendChild(document.createElement("br")),y.appendChild(o.call(this,t,n,"bottom",i))}g=y}n.appendChild(g),n.addEventListener("mouseover",this.highlightDOMNode.bind(this,!1,"all"),!1),n.addEventListener("mouseleave",this.highlightDOMNode.bind(this,!1,"all"),!1),this.contentElement.removeChildren(),this.contentElement.appendChild(n),this.element.classList.remove("collapsed")}startEditing(e,t,n,s){if(i.UIUtils.isBeingEdited(e))return;const o={box:t,styleProperty:n,computedStyle:s,keyDownHandler:()=>{}},r=this.handleKeyDown.bind(this,o);o.keyDownHandler=r,e.addEventListener("keydown",r,!1),this.isEditingMetrics=!0;const a=new i.InplaceEditor.Config(this.editingCommitted.bind(this),this.editingCancelled.bind(this),o);i.InplaceEditor.InplaceEditor.startEditing(e,a);const l=e.getComponentSelection();l&&l.selectAllChildren(e)}handleKeyDown(e,t){const n=t.currentTarget;i.UIUtils.handleElementValueModifications(t,n,function(t,i){this.applyUserInput(n,i,t,e,!1)}.bind(this),void 0,(function(t,n,i){return"margin"!==e.styleProperty&&n<0&&(n=0),t+n+i}))}editingEnded(e,t){this.originalPropertyData=null,this.previousPropertyDataCandidate=null,e.removeEventListener("keydown",t.keyDownHandler,!1),delete this.isEditingMetrics}editingCancelled(e,t){if(this.inlineStyle)if(this.originalPropertyData)this.inlineStyle.allProperties()[this.originalPropertyData.index].setText(this.originalPropertyData.propertyText||"",!1);else{const e=this.inlineStyle.pastLastSourcePropertyIndex();e&&this.inlineStyle.allProperties()[e-1].setText("",!1)}this.editingEnded(e,t),this.update()}applyUserInput(t,n,i,s,o){if(!this.inlineStyle)return this.editingCancelled(t,s);if(o&&n===i)return this.editingCancelled(t,s);"position"===s.box||n&&"‒"!==n?"position"!==s.box||n&&"‒"!==n||(n="auto"):n="0px",n=n.toLowerCase(),/^\d+$/.test(n)&&(n+="px");const r=s.styleProperty,a=s.computedStyle;if("border-box"===a.get("box-sizing")&&("width"===r||"height"===r)){if(!n.match(/px$/))return void e.Console.Console.instance().error("For elements with box-sizing: border-box, only absolute content area dimensions can be applied");const t=this.getBox(a,"border"),i=this.getBox(a,"padding");let s=Number(n.replace(/px$/,""));if(isNaN(s))return;s+="width"===r?t.left+t.right+i.left+i.right:t.top+t.bottom+i.top+i.bottom,n=s+"px"}this.previousPropertyDataCandidate=null;const l=this.inlineStyle.allProperties();for(let e=0;e<l.length;++e){const t=l[e];if(t.name===s.styleProperty&&t.activeInStyle())return this.previousPropertyDataCandidate=t,void t.setValue(n,o,!0,d.bind(this))}function d(e){if(e){if(this.originalPropertyData||(this.originalPropertyData=this.previousPropertyDataCandidate),this.highlightMode){const e=this.node();if(!e)return;e.highlight(this.highlightMode)}o&&this.update()}}this.inlineStyle.appendProperty(s.styleProperty,n,d.bind(this))}editingCommitted(e,t,n,i){this.editingEnded(e,i),this.applyUserInput(e,t,n,i,!0)}wasShown(){super.wasShown(),this.registerCSSFiles([ws])}}var Ts=Object.freeze({__proto__:null,MetricsSidebarPane:xs});const Ms={findByStringSelectorOrXpath:"Find by string, selector, or `XPath`",switchToAccessibilityTreeView:"Switch to Accessibility Tree view",switchToDomTreeView:"Switch to DOM Tree view",showComputedStylesSidebar:"Show Computed Styles sidebar",hideComputedStylesSidebar:"Hide Computed Styles sidebar",computedStylesShown:"Computed Styles sidebar shown",computedStylesHidden:"Computed Styles sidebar hidden",computed:"Computed",styles:"Styles",revealInElementsPanel:"Reveal in Elements panel",nodeCannotBeFoundInTheCurrent:"Node cannot be found in the current page.",theRemoteObjectCouldNotBe:"The remote object could not be resolved to a valid node.",theDeferredDomNodeCouldNotBe:"The deferred `DOM` Node could not be resolved to a valid node.",elementStateS:"Element state: {PH1}",sidePanelToolbar:"Side panel toolbar",sidePanelContent:"Side panel content",domTreeExplorer:"DOM tree explorer"},Ns=r.i18n.registerUIStrings("panels/elements/ElementsPanel.ts",Ms),Is=r.i18n.getLocalizedString.bind(void 0,Ns),Ps=e=>{const t=new c.Button.Button,n=Is(e?Ms.switchToDomTreeView:Ms.switchToAccessibilityTreeView);return t.data={active:e,variant:"toolbar",iconUrl:new URL("../../Images/person.svg",import.meta.url).toString(),title:n},t.tabIndex=0,t.classList.add("axtree-button"),e&&t.classList.add("active"),t};let Os;class Ls extends i.Panel.Panel{splitWidget;searchableViewInternal;mainContainer;domTreeContainer;splitMode;accessibilityTreeView;breadcrumbs;stylesWidget;computedStyleWidget;metricsWidget;treeOutlines=new Set;searchResults;currentSearchResultIndex;pendingNodeReveal;adornerManager;adornerSettingsPane;adornersByName;accessibilityTreeButton;domTreeButton;selectedNodeOnReset;hasNonDefaultSelectedNode;searchConfig;omitDefaultSelection;notFirstInspectElement;sidebarPaneView;stylesViewToReveal;nodeInsertedTaskRunner={queue:Promise.resolve(),run(e){this.queue=this.queue.then(e)}};cssStyleTrackerByCSSModel;constructor(){super("elements"),this.splitWidget=new i.SplitWidget.SplitWidget(!0,!0,"elements-panel-split-view-state",325,325),this.splitWidget.addEventListener("SidebarSizeChanged",this.updateTreeOutlineVisibleWidth.bind(this)),this.splitWidget.show(this.element),this.searchableViewInternal=new i.SearchableView.SearchableView(this,null),this.searchableViewInternal.setMinimalSearchQuerySize(0),this.searchableViewInternal.setMinimumSize(25,28),this.searchableViewInternal.setPlaceholder(Is(Ms.findByStringSelectorOrXpath));const s=this.searchableViewInternal.element;this.mainContainer=document.createElement("div"),this.domTreeContainer=document.createElement("div");const o=document.createElement("div");t.Runtime.experiments.isEnabled("full-accessibility-tree")&&this.initializeFullAccessibilityTreeView(),this.mainContainer.appendChild(this.domTreeContainer),s.appendChild(this.mainContainer),s.appendChild(o),i.ARIAUtils.markAsMain(this.domTreeContainer),i.ARIAUtils.setLabel(this.domTreeContainer,Is(Ms.domTreeExplorer)),this.splitWidget.setMainWidget(this.searchableViewInternal),this.splitMode=null,this.mainContainer.id="main-content",this.domTreeContainer.id="elements-content",this.domTreeContainer.tabIndex=-1,e.Settings.Settings.instance().moduleSetting("dom-word-wrap").get()&&this.domTreeContainer.classList.add("elements-wrap"),e.Settings.Settings.instance().moduleSetting("dom-word-wrap").addChangeListener(this.domWordWrapSettingChanged.bind(this)),o.id="elements-crumbs",this.domTreeButton&&(this.accessibilityTreeView=new j(this.domTreeButton,new h.TreeOutline.TreeOutline)),this.breadcrumbs=new p.ElementsBreadcrumbs.ElementsBreadcrumbs,this.breadcrumbs.addEventListener("breadcrumbsnodeselected",(e=>{this.crumbNodeSelected(e)})),o.appendChild(this.breadcrumbs),this.stylesWidget=Qn.instance(),this.computedStyleWidget=new Ti,this.metricsWidget=new xs,e.Settings.Settings.instance().moduleSetting("sidebar-position").addChangeListener(this.updateSidebarPosition.bind(this)),this.updateSidebarPosition(),this.cssStyleTrackerByCSSModel=new Map,n.TargetManager.TargetManager.instance().observeModels(n.DOMModel.DOMModel,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addEventListener("NameChanged",(e=>this.targetNameChanged(e.data))),e.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom").addChangeListener(this.showUAShadowDOMChanged.bind(this)),d.ExtensionServer.ExtensionServer.instance().addEventListener("SidebarPaneAdded",this.extensionSidebarPaneAdded,this),this.currentSearchResultIndex=-1,this.pendingNodeReveal=!1,this.adornerManager=new p.AdornerManager.AdornerManager(e.Settings.Settings.instance().moduleSetting("adorner-settings")),this.adornerSettingsPane=null,this.adornersByName=new Map}initializeFullAccessibilityTreeView(){this.accessibilityTreeButton=Ps(!1),this.accessibilityTreeButton.addEventListener("click",this.showAccessibilityTree.bind(this)),this.domTreeButton=Ps(!0),this.domTreeButton.addEventListener("click",this.showDOMTree.bind(this)),this.mainContainer.appendChild(this.accessibilityTreeButton)}showAccessibilityTree(){this.accessibilityTreeView&&this.splitWidget.setMainWidget(this.accessibilityTreeView)}showDOMTree(){this.splitWidget.setMainWidget(this.searchableViewInternal);const e=this.selectedDOMNode();if(!e)return;const t=this.treeElementForNode(e);t&&t.select()}static instance(e={forceNew:null}){const{forceNew:t}=e;return Os&&!t||(Os=new Ls),Os}revealProperty(e){return this.sidebarPaneView&&this.stylesViewToReveal?this.sidebarPaneView.showView(this.stylesViewToReveal).then((()=>{this.stylesWidget.revealProperty(e)})):Promise.resolve()}resolveLocation(e){return this.sidebarPaneView||null}showToolbarPane(e,t){this.stylesWidget.showToolbarPane(e,t)}modelAdded(t){const i=t.parentModel();let s=i?Xi.forDOMModel(i):null;s||(s=new Xi(!0,!0),s.setWordWrap(e.Settings.Settings.instance().moduleSetting("dom-word-wrap").get()),s.addEventListener(Xi.Events.SelectedNodeChanged,this.selectedNodeChanged,this),s.addEventListener(Xi.Events.ElementsTreeUpdated,this.updateBreadcrumbIfNeeded,this),new bs(s,new e.Throttler.Throttler(100)),this.treeOutlines.add(s)),s.wireToDOMModel(t),this.setupStyleTracking(t.cssModel()),this.isShowing()&&this.wasShown(),this.domTreeContainer.hasFocus()&&s.focus(),t.addEventListener(n.DOMModel.Events.DocumentUpdated,this.documentUpdatedEvent,this),t.addEventListener(n.DOMModel.Events.NodeInserted,this.handleNodeInserted,this)}handleNodeInserted(e){this.nodeInsertedTaskRunner.run((async()=>{const t=e.data;if(!t.isViewTransitionPseudoNode())return;const n=t.domModel().cssModel(),i=await n.requestViaInspectorStylesheet(t);if(!i)return;const s=await n.getStyleSheetText(i.id);s?.includes(`${t.simpleSelector()} {`)||await n.setStyleSheetText(i.id,`${s}\n${t.simpleSelector()} {}`,!1)}))}modelRemoved(e){e.removeEventListener(n.DOMModel.Events.DocumentUpdated,this.documentUpdatedEvent,this),e.removeEventListener(n.DOMModel.Events.NodeInserted,this.handleNodeInserted,this);const t=Xi.forDOMModel(e);t&&(t.unwireFromDOMModel(e),e.parentModel()||(this.treeOutlines.delete(t),t.element.remove(),this.removeStyleTracking(e.cssModel())))}targetNameChanged(e){const t=e.model(n.DOMModel.DOMModel);if(!t)return;Xi.forDOMModel(t)}updateTreeOutlineVisibleWidth(){if(!this.treeOutlines.size)return;let e=this.splitWidget.element.offsetWidth;this.splitWidget.isVertical()&&(e-=this.splitWidget.sidebarSize());for(const t of this.treeOutlines)t.setVisibleWidth(e)}focus(){this.treeOutlines.size?this.treeOutlines.values().next().value.focus():this.domTreeContainer.focus()}searchableView(){return this.searchableViewInternal}wasShown(){super.wasShown(),i.Context.Context.instance().setFlavor(Ls,this),this.registerCSSFiles([Pi]);for(const e of this.treeOutlines)e.element.parentElement!==this.domTreeContainer&&this.domTreeContainer.appendChild(e.element);const e=n.TargetManager.TargetManager.instance().models(n.DOMModel.DOMModel,{scoped:!0});for(const t of e){if(t.parentModel())continue;const e=Xi.forDOMModel(t);e&&(e.setVisible(!0),e.rootDOMNode||(t.existingDocument()?(e.rootDOMNode=t.existingDocument(),this.documentUpdated(t)):t.requestDocument()))}}willHide(){n.OverlayModel.OverlayModel.hideDOMNodeHighlight();for(const e of this.treeOutlines)e.setVisible(!1),this.domTreeContainer.removeChild(e.element);super.willHide(),i.Context.Context.instance().setFlavor(Ls,null)}onResize(){this.element.window().requestAnimationFrame(this.updateSidebarPosition.bind(this)),this.updateTreeOutlineVisibleWidth()}selectedNodeChanged(e){let t=e.data.node;t&&t.pseudoType()&&!t.parentNode&&(t=null);const{focus:s}=e.data;for(const e of this.treeOutlines)t&&Xi.forDOMModel(t.domModel())===e||e.selectDOMNode(null);if(t){const e=[p.Helper.legacyNodeToElementsComponentsNode(t)];for(let n=t.parentNode;n;n=n.parentNode)e.push(p.Helper.legacyNodeToElementsComponentsNode(n));this.breadcrumbs.data={crumbs:e,selectedNode:p.Helper.legacyNodeToElementsComponentsNode(t)},this.accessibilityTreeView&&this.accessibilityTreeView.selectedNodeChanged(t)}else this.breadcrumbs.data={crumbs:[],selectedNode:null};if(i.Context.Context.instance().setFlavor(n.DOMModel.DOMNode,t),!t)return;t.setAsInspectedNode(),s&&(this.selectedNodeOnReset=t,this.hasNonDefaultSelectedNode=!0);const o=t.domModel().runtimeModel().executionContexts(),r=t.frameId();for(const e of o)if(e.frameId===r){i.Context.Context.instance().setFlavor(n.RuntimeModel.ExecutionContext,e);break}}documentUpdatedEvent(e){const t=e.data;this.documentUpdated(t),this.removeStyleTracking(t.cssModel()),this.setupStyleTracking(t.cssModel())}documentUpdated(e){if(this.searchableViewInternal.cancelSearch(),!e.existingDocument())return void(this.isShowing()&&e.requestDocument());if(this.hasNonDefaultSelectedNode=!1,this.omitDefaultSelection)return;const t=this.selectedNodeOnReset;(async function(e,n){const i=n?n.path():null,s=i?await e.pushNodeByPathToFrontend(i):null;if(t!==this.selectedNodeOnReset)return;let o=s?e.nodeForId(s):null;if(!o){const t=e.existingDocument();o=t?t.body||t.documentElement:null}o&&(this.setDefaultSelectedNode(o),this.lastSelectedNodeSelectedForTest())}).call(this,e,this.selectedNodeOnReset||null)}lastSelectedNodeSelectedForTest(){}setDefaultSelectedNode(e){if(!e||this.hasNonDefaultSelectedNode||this.pendingNodeReveal)return;const t=Xi.forDOMModel(e.domModel());t&&(this.selectDOMNode(e),t.selectedTreeElement&&t.selectedTreeElement.expand())}onSearchClosed(){const e=this.selectedDOMNode();if(!e)return;const t=this.treeElementForNode(e);t&&t.select()}onSearchCanceled(){this.searchConfig=void 0,this.hideSearchHighlights(),this.searchableViewInternal.updateSearchMatchesCount(0),this.currentSearchResultIndex=-1,delete this.searchResults,n.DOMModel.DOMModel.cancelSearch()}performSearch(t,i,s){const o=t.query,r=o.trim();if(!r.length)return;this.searchConfig&&this.searchConfig.query===o?this.hideSearchHighlights():this.onSearchCanceled(),this.searchConfig=t;const a=e.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom").get(),l=n.TargetManager.TargetManager.instance().models(n.DOMModel.DOMModel,{scoped:!0}),d=l.map((e=>e.performSearch(r,a)));Promise.all(d).then((e=>{this.searchResults=[];for(let t=0;t<e.length;++t){const n=e[t];for(let e=0;e<n;++e)this.searchResults.push({domModel:l[t],index:e,node:void 0})}if(this.searchableViewInternal.updateSearchMatchesCount(this.searchResults.length),!this.searchResults.length)return;this.currentSearchResultIndex>=this.searchResults.length&&(this.currentSearchResultIndex=-1);let t=this.currentSearchResultIndex;i&&(t=-1===this.currentSearchResultIndex?s?-1:0:s?t-1:t+1,this.jumpToSearchResult(t))}))}domWordWrapSettingChanged(e){this.domTreeContainer.classList.toggle("elements-wrap",e.data);for(const t of this.treeOutlines)t.setWordWrap(e.data)}switchToAndFocus(e){this.searchableViewInternal.cancelSearch(),i.ViewManager.ViewManager.instance().showView("elements").then((()=>this.selectDOMNode(e,!0)))}jumpToSearchResult(e){this.searchResults&&(this.currentSearchResultIndex=(e+this.searchResults.length)%this.searchResults.length,this.highlightCurrentSearchResult())}jumpToNextSearchResult(){this.searchResults&&this.searchConfig&&this.performSearch(this.searchConfig,!0)}jumpToPreviousSearchResult(){this.searchResults&&this.searchConfig&&this.performSearch(this.searchConfig,!0,!0)}supportsCaseSensitiveSearch(){return!1}supportsRegexSearch(){return!1}highlightCurrentSearchResult(){const e=this.currentSearchResultIndex,t=this.searchResults;if(!t)return;const n=t[e];if(this.searchableViewInternal.updateCurrentMatchIndex(e),null===n.node)return;if(void 0===n.node)return void n.domModel.searchResult(n.index).then((e=>{n.node=e;this.searchConfig&&this.searchResults&&-1!==this.currentSearchResultIndex&&this.highlightCurrentSearchResult()}));const s=this.treeElementForNode(n.node);if(n.node.scrollIntoView(),s){this.searchConfig&&s.highlightSearchResults(this.searchConfig.query),s.reveal();const e=s.listItemElement.getElementsByClassName(i.UIUtils.highlightedSearchResultClassName);e.length&&e[0].scrollIntoViewIfNeeded(!1),s.select(!0)}}hideSearchHighlights(){if(!this.searchResults||!this.searchResults.length||-1===this.currentSearchResultIndex)return;const e=this.searchResults[this.currentSearchResultIndex];if(!e.node)return;const t=this.treeElementForNode(e.node);t&&t.hideSearchHighlights()}selectedDOMNode(){for(const e of this.treeOutlines)if(e.selectedDOMNode())return e.selectedDOMNode();return null}selectDOMNode(e,t){for(const n of this.treeOutlines){Xi.forDOMModel(e.domModel())===n?n.selectDOMNode(e,t):n.selectDOMNode(null)}}selectAndShowSidebarTab(e){this.sidebarPaneView&&(this.sidebarPaneView.tabbedPane().selectTab(e),this.isShowing()||i.ViewManager.ViewManager.instance().showView("elements"))}updateBreadcrumbIfNeeded(e){const t=e.data,n=this.selectedDOMNode();if(!n)return void(this.breadcrumbs.data={crumbs:[],selectedNode:null});const i=p.Helper.legacyNodeToElementsComponentsNode(n),s=[i];for(let e=n.parentNode;e;e=e.parentNode)s.push(p.Helper.legacyNodeToElementsComponentsNode(e));const o=t.map(p.Helper.legacyNodeToElementsComponentsNode),r=new Map;o.forEach((e=>r.set(e.id,e)));const a=s.map((e=>r.get(e.id)||e));this.breadcrumbs.data={crumbs:a,selectedNode:i}}crumbNodeSelected(e){this.selectDOMNode(e.legacyDomNode,!0)}treeOutlineForNode(e){return e?Xi.forDOMModel(e.domModel()):null}treeElementForNode(e){const t=this.treeOutlineForNode(e);return t?t.findTreeElement(e):null}leaveUserAgentShadowDOM(e){let t;for(;(t=e.ancestorUserAgentShadowRoot())&&t.parentNode;)e=t.parentNode;return e}async revealAndSelectNode(t,n,s){this.omitDefaultSelection=!0;const r=e.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom").get()?t:this.leaveUserAgentShadowDOM(t);s||r.highlightForTwoSeconds(),this.accessibilityTreeView&&this.accessibilityTreeView.revealAndSelectNode(t),await i.ViewManager.ViewManager.instance().showView("elements",!1,!n),this.selectDOMNode(r,n),delete this.omitDefaultSelection,this.notFirstInspectElement||(Ls.firstInspectElementNodeNameForTest=r.nodeName(),Ls.firstInspectElementCompletedForTest(),o.InspectorFrontendHost.InspectorFrontendHostInstance.inspectElementCompleted()),this.notFirstInspectElement=!0}showUAShadowDOMChanged(){for(const e of this.treeOutlines)e.update()}setupTextSelectionHack(e){const t=i.bind(this),n=e=>{0===e.buttons&&i.call(this)};function i(){this.splitWidget.element.classList.remove("disable-resizer-for-elements-hack"),e.style.removeProperty("left"),e.style.removeProperty("padding-left"),e.style.removeProperty("width"),e.style.removeProperty("position"),e.window().removeEventListener("blur",t),e.window().removeEventListener("contextmenu",t,!0),e.window().removeEventListener("dragstart",t,!0),e.window().removeEventListener("mousemove",n,!0),e.window().removeEventListener("mouseup",t,!0),e.window().removeEventListener("visibilitychange",t)}e.addEventListener("mousedown",(i=>{if(0!==i.button)return;this.splitWidget.element.classList.add("disable-resizer-for-elements-hack"),e.style.setProperty("height",`${e.offsetHeight}px`);e.style.setProperty("left","-1000000px"),e.style.setProperty("padding-left","1000000px"),e.style.setProperty("width","calc(100% + 1000000px)"),e.style.setProperty("position","fixed"),e.window().addEventListener("blur",t),e.window().addEventListener("contextmenu",t,!0),e.window().addEventListener("dragstart",t,!0),e.window().addEventListener("mousemove",n,!0),e.window().addEventListener("mouseup",t,!0),e.window().addEventListener("visibilitychange",t)}),!0)}initializeSidebarPanes(e){this.splitWidget.setVertical("Vertical"===e),this.showToolbarPane(null,null);const t=new i.Widget.VBox;t.element.classList.add("style-panes-wrapper"),t.element.setAttribute("jslog",`${s.pane("styles").track({resize:!0})}`),this.stylesWidget.show(t.element),this.setupTextSelectionHack(t.element);const n=new i.Widget.VBox;n.element.classList.add("style-panes-wrapper"),n.element.setAttribute("jslog",`${s.pane("computed").track({resize:!0})}`),this.computedStyleWidget.show(n.element);const o=new i.SplitWidget.SplitWidget(!0,!0,"elements.styles.sidebar.width",100);o.setMainWidget(t),o.hideSidebar(),o.enableShowModeSaving(),o.addEventListener("ShowModeChanged",(()=>{a()})),this.stylesWidget.addEventListener("InitialUpdateCompleted",(()=>{this.stylesWidget.appendToolbarItem(o.createShowHideSidebarButton(Is(Ms.showComputedStylesSidebar),Is(Ms.hideComputedStylesSidebar),Is(Ms.computedStylesShown),Is(Ms.computedStylesHidden),"computed-styles"))}));const r=()=>{this.metricsWidget.show(n.element,this.computedStyleWidget.element),this.metricsWidget.toggleVisibility(!0),this.stylesWidget.removeEventListener("StylesUpdateCompleted",l)},a=()=>{"Both"===o.showMode()?r():(this.metricsWidget.show(t.element),this.stylesWidget.hasMatchedStyles||this.metricsWidget.toggleVisibility(!1),this.stylesWidget.addEventListener("StylesUpdateCompleted",l))},l=e=>{this.metricsWidget.toggleVisibility(e.data.hasMatchedStyles)};this.sidebarPaneView=i.ViewManager.ViewManager.instance().createTabbedLocation((()=>i.ViewManager.ViewManager.instance().showView("elements")),"styles-pane-sidebar",!0,!0);const c=this.sidebarPaneView.tabbedPane();c.headerElement().setAttribute("jslog",`${s.toolbar("sidebar").track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`),"Vertical"!==this.splitMode&&this.splitWidget.installResizer(c.headerElement());const h=c.headerElement();i.ARIAUtils.markAsNavigation(h),i.ARIAUtils.setLabel(h,Is(Ms.sidePanelToolbar));const p=c.tabbedPaneContentElement();i.ARIAUtils.markAsComplementary(p),i.ARIAUtils.setLabel(p,Is(Ms.sidePanelContent));const u=new i.View.SimpleView(Is(Ms.styles),void 0,"styles");this.sidebarPaneView.appendView(u),u.element.classList.add("flex-auto"),o.show(u.element);const m=new i.View.SimpleView(Is(Ms.computed),void 0,"computed");m.element.classList.add("composite","fill"),c.addEventListener(i.TabbedPane.Events.TabSelected,(e=>{const{tabId:t}=e.data;"computed"===t?(n.show(m.element),r()):"styles"===t&&(o.setSidebarWidget(n),a())}),this),this.sidebarPaneView.appendView(m),this.stylesViewToReveal=u,this.sidebarPaneView.appendApplicableItems("elements-sidebar");const g=d.ExtensionServer.ExtensionServer.instance().sidebarPanes();for(let e=0;e<g.length;++e)this.addExtensionSidebarPane(g[e]);this.splitWidget.setSidebarWidget(this.sidebarPaneView.tabbedPane())}updateSidebarPosition(){if(this.sidebarPaneView&&this.sidebarPaneView.tabbedPane().shouldHideOnDetach())return;const t=e.Settings.Settings.instance().moduleSetting("sidebar-position").get();let n="Horizontal";if(("right"===t||"auto"===t&&i.InspectorView.InspectorView.instance().element.offsetWidth>680)&&(n="Vertical"),!this.sidebarPaneView)return void this.initializeSidebarPanes(n);if(n===this.splitMode)return;this.splitMode=n;const s=this.sidebarPaneView.tabbedPane();this.splitWidget.uninstallResizer(s.headerElement()),this.splitWidget.setVertical("Vertical"===this.splitMode),this.showToolbarPane(null,null),"Vertical"!==this.splitMode&&this.splitWidget.installResizer(s.headerElement())}extensionSidebarPaneAdded(e){this.addExtensionSidebarPane(e.data)}addExtensionSidebarPane(e){this.sidebarPaneView&&e.panelName()===this.name&&this.sidebarPaneView.appendView(e)}getComputedStyleWidget(){return this.computedStyleWidget}setupStyleTracking(e){const t=e.createCSSPropertyTracker(As);t.start(),this.cssStyleTrackerByCSSModel.set(e,t),t.addEventListener("TrackedCSSPropertiesUpdated",this.trackedCSSPropertiesUpdated,this)}removeStyleTracking(e){const t=this.cssStyleTrackerByCSSModel.get(e);t&&(t.stop(),this.cssStyleTrackerByCSSModel.delete(e),t.removeEventListener("TrackedCSSPropertiesUpdated",this.trackedCSSPropertiesUpdated,this))}trackedCSSPropertiesUpdated({data:e}){for(const t of e){if(!t)continue;const e=this.treeElementForNode(t);e&&e.updateStyleAdorners(),p.LayoutPane.LayoutPane.instance().render()}}showAdornerSettingsPane(){this.adornerSettingsPane||(this.adornerSettingsPane=new p.AdornerSettingsPane.AdornerSettingsPane,this.adornerSettingsPane.addEventListener("adornersettingupdated",(e=>{const{adornerName:t,isEnabledNow:n,newSettings:i}=e.data,s=this.adornersByName.get(t);if(s)for(const e of s)n?e.show():e.hide();this.adornerManager.updateSettings(i)})),this.searchableViewInternal.element.prepend(this.adornerSettingsPane));const e=this.adornerManager.getSettings();this.adornerSettingsPane.data={settings:e},this.adornerSettingsPane.show()}isAdornerEnabled(e){return this.adornerManager.isAdornerEnabled(e)}registerAdorner(e){let t=this.adornersByName.get(e.name);t||(t=new Set,this.adornersByName.set(e.name,t)),t.add(e),this.isAdornerEnabled(e.name)||e.hide()}deregisterAdorner(e){const t=this.adornersByName.get(e.name);t&&t.delete(e)}static firstInspectElementCompletedForTest=function(){};static firstInspectElementNodeNameForTest=""}globalThis.Elements=globalThis.Elements||{},globalThis.Elements.ElementsPanel=Ls;const As=[{name:"display",value:"grid"},{name:"display",value:"inline-grid"},{name:"display",value:"flex"},{name:"display",value:"inline-flex"},{name:"container-type",value:"inline-size"},{name:"container-type",value:"block-size"},{name:"container-type",value:"size"}];let ks;class Rs{static instance(e={forceNew:null}){const{forceNew:t}=e;return ks&&!t||(ks=new Rs),ks}decorate(e){const t=e.domModel().cssModel().pseudoState(e);return t?{color:"--sys-color-orange-bright",title:Is(Ms.elementStateS,{PH1:":"+t.join(", :")})}:null}}var Ds=Object.freeze({__proto__:null,ElementsPanel:Ls,ContextMenuProvider:class{appendApplicableItems(t,i,s){s instanceof n.RemoteObject.RemoteObject&&!s.isNode()||Ls.instance().element.isAncestor(t.target)||i.revealSection().appendItem(Is(Ms.revealInElementsPanel),(()=>e.Revealer.reveal(s)),{jslogContext:"elements.reveal-node"})}},DOMNodeRevealer:class{reveal(t,i){const s=Ls.instance();return s.pendingNodeReveal=!0,new Promise((function(e,o){if(t instanceof n.DOMModel.DOMNode)r(t);else if(t instanceof n.DOMModel.DeferredDOMNode)t.resolve((function(e){if(!e){const e=Is(Ms.theDeferredDomNodeCouldNotBe);return void o(new a.UserVisibleError.UserVisibleError(e))}r(e)}));else{const e=t.runtimeModel().target().model(n.DOMModel.DOMModel);if(e)e.pushObjectAsNodeToFrontend(t).then((function(e){if(!e){const e=Is(Ms.theRemoteObjectCouldNotBe);return void o(new a.UserVisibleError.UserVisibleError(e))}r(e)}));else{const e=Is(Ms.nodeCannotBeFoundInTheCurrent);o(new a.UserVisibleError.UserVisibleError(e))}}function r(r){s.pendingNodeReveal=!1;let l=r;for(;l.parentNode;)l=l.parentNode;const d=!(l instanceof n.DOMModel.DOMDocument);if(!(t instanceof n.DOMModel.DOMDocument)&&d){const e=Is(Ms.nodeCannotBeFoundInTheCurrent);return void o(new a.UserVisibleError.UserVisibleError(e))}if(r)return void s.revealAndSelectNode(r,!i).then(e);const c=Is(Ms.nodeCannotBeFoundInTheCurrent);o(new a.UserVisibleError.UserVisibleError(c))}})).catch((t=>{let n;throw n=a.UserVisibleError.isUserVisibleError(t)?t.message:Is(Ms.nodeCannotBeFoundInTheCurrent),e.Console.Console.instance().warn(n),t}))}},CSSPropertyRevealer:class{reveal(e){return Ls.instance().revealProperty(e)}},ElementsActionDelegate:class{handleAction(e,t){const s=e.flavor(n.DOMModel.DOMNode);if(!s)return!0;const o=Xi.forDOMModel(s.domModel());if(!o)return!0;switch(t){case"elements.hide-element":return o.toggleHideElement(s),!0;case"elements.edit-as-html":return o.toggleEditAsHTML(s),!0;case"elements.duplicate-element":return o.duplicateNode(s),!0;case"elements.copy-styles":return o.findTreeElement(s)?.copyStyles(),!0;case"elements.undo":return n.DOMModel.DOMModelUndoStack.instance().undo(),Ls.instance().stylesWidget.forceUpdate(),!0;case"elements.redo":return n.DOMModel.DOMModelUndoStack.instance().redo(),Ls.instance().stylesWidget.forceUpdate(),!0;case"elements.show-styles":return Ls.instance().selectAndShowSidebarTab("styles"),!0;case"elements.show-computed":return Ls.instance().selectAndShowSidebarTab("computed"),!0;case"elements.toggle-eye-dropper":{const e=i.Context.Context.instance().flavor(q);if(!e)return!1;e.toggleEyeDropper()}}return!1}},PseudoStateMarkerDecorator:Rs});let Fs;class Us{toggleSearchAction;mode;showDetailedInspectTooltipSetting;constructor(){this.toggleSearchAction=i.ActionRegistry.ActionRegistry.instance().getAction("elements.toggle-element-search"),this.mode="none",n.TargetManager.TargetManager.instance().addEventListener("SuspendStateChanged",this.suspendStateChanged,this),n.TargetManager.TargetManager.instance().addModelListener(n.OverlayModel.OverlayModel,"InspectModeExited",(()=>this.setMode("none")),void 0,{scoped:!0}),n.OverlayModel.OverlayModel.setInspectNodeHandler(this.inspectNode.bind(this)),n.TargetManager.TargetManager.instance().observeModels(n.OverlayModel.OverlayModel,this,{scoped:!0}),this.showDetailedInspectTooltipSetting=e.Settings.Settings.instance().moduleSetting("show-detailed-inspect-tooltip"),this.showDetailedInspectTooltipSetting.addChangeListener(this.showDetailedInspectTooltipChanged.bind(this)),document.addEventListener("keydown",(e=>{e.keyCode===i.KeyboardShortcut.Keys.Esc.code&&this.isInInspectElementMode()&&(this.setMode("none"),e.consume(!0),s.logKeyDown(null,e,"cancel-inspect-mode"))}),!0)}static instance({forceNew:e}={forceNew:!1}){return Fs&&!e||(Fs=new Us),Fs}modelAdded(e){"none"!==this.mode&&e.setInspectMode(this.mode,this.showDetailedInspectTooltipSetting.get())}modelRemoved(e){}isInInspectElementMode(){return"none"!==this.mode}toggleInspectMode(){let t;t=this.isInInspectElementMode()?"none":e.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom").get()?"searchForUAShadowDOM":"searchForNode",this.setMode(t)}captureScreenshotMode(){this.setMode("captureAreaScreenshot")}setMode(e){if(!n.TargetManager.TargetManager.instance().allTargetsSuspended()){this.mode=e;for(const t of n.TargetManager.TargetManager.instance().models(n.OverlayModel.OverlayModel,{scoped:!0}))t.setInspectMode(e,this.showDetailedInspectTooltipSetting.get());this.toggleSearchAction.setToggled(this.isInInspectElementMode())}}suspendStateChanged(){n.TargetManager.TargetManager.instance().allTargetsSuspended()&&(this.mode="none",this.toggleSearchAction.setToggled(!1))}inspectNode(e){Ls.instance().revealAndSelectNode(e,!0,!0)}showDetailedInspectTooltipChanged(){this.setMode(this.mode)}}var Bs=Object.freeze({__proto__:null,InspectElementModeController:Us,ToggleSearchActionDelegate:class{handleAction(e,n){return!t.Runtime.Runtime.queryParam("isSharedWorker")&&(Fs=Us.instance(),!!Fs&&("elements.toggle-element-search"===n?Fs.toggleInspectMode():"elements.capture-area-screenshot"===n&&Fs.captureScreenshotMode(),!0))}}});const _s={frameworkListeners:"`Framework` listeners",showListenersOnTheAncestors:"Show listeners on the ancestors",ancestors:"Ancestors",eventListenersCategory:"Event listeners category",all:"All",passive:"Passive",blocking:"Blocking",resolveEventListenersBoundWith:"Resolve event listeners bound with framework"},Vs=r.i18n.registerUIStrings("panels/elements/EventListenersWidget.ts",_s),Hs=r.i18n.getLocalizedString.bind(void 0,Vs);let Ws;class js extends i.ThrottledWidget.ThrottledWidget{toolbarItemsInternal;showForAncestorsSetting;dispatchFilterBySetting;showFrameworkListenersSetting;eventListenersView;lastRequestedNode;constructor(){super(),this.toolbarItemsInternal=[],this.showForAncestorsSetting=e.Settings.Settings.instance().moduleSetting("show-event-listeners-for-ancestors"),this.showForAncestorsSetting.addChangeListener(this.update.bind(this)),this.dispatchFilterBySetting=e.Settings.Settings.instance().createSetting("event-listener-dispatch-filter-type",zs.All),this.dispatchFilterBySetting.addChangeListener(this.update.bind(this)),this.showFrameworkListenersSetting=e.Settings.Settings.instance().createSetting("show-frameowkr-listeners",!0),this.showFrameworkListenersSetting.setTitle(Hs(_s.frameworkListeners)),this.showFrameworkListenersSetting.addChangeListener(this.showFrameworkListenersChanged.bind(this)),this.eventListenersView=new A.EventListenersView.EventListenersView(this.update.bind(this)),this.eventListenersView.show(this.element),this.element.setAttribute("jslog",`${s.pane("elements.event-listeners").track({resize:!0})}`),this.toolbarItemsInternal.push(i.Toolbar.Toolbar.createActionButtonForId("elements.refresh-event-listeners")),this.toolbarItemsInternal.push(new i.Toolbar.ToolbarSettingCheckbox(this.showForAncestorsSetting,Hs(_s.showListenersOnTheAncestors),Hs(_s.ancestors)));const t=new i.Toolbar.ToolbarComboBox(this.onDispatchFilterTypeChanged.bind(this),Hs(_s.eventListenersCategory));function o(e,n){const i=t.createOption(e,n);n===this.dispatchFilterBySetting.get()&&t.select(i)}t.element.setAttribute("jslog",`${s.filterDropdown().track({change:!0})}`),o.call(this,Hs(_s.all),zs.All),o.call(this,Hs(_s.passive),zs.Passive),o.call(this,Hs(_s.blocking),zs.Blocking),t.setMaxWidth(200),this.toolbarItemsInternal.push(t),this.toolbarItemsInternal.push(new i.Toolbar.ToolbarSettingCheckbox(this.showFrameworkListenersSetting,Hs(_s.resolveEventListenersBoundWith))),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.update,this),this.update()}static instance(e={forceNew:null}){const{forceNew:t}=e;return Ws&&!t||(Ws=new js),Ws}doUpdate(){this.lastRequestedNode&&(this.lastRequestedNode.domModel().runtimeModel().releaseObjectGroup($s),delete this.lastRequestedNode);const e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!e)return this.eventListenersView.reset(),this.eventListenersView.addEmptyHolderIfNeeded(),Promise.resolve();this.lastRequestedNode=e;const t=!this.showForAncestorsSetting.get(),s=[];if(s.push(e.resolveToObject($s)),!t){let t=e.parentNode;for(;t;)s.push(t.resolveToObject($s)),t=t.parentNode;s.push(this.windowObjectInNodeContext(e))}return Promise.all(s).then(this.eventListenersView.addObjects.bind(this.eventListenersView)).then(this.showFrameworkListenersChanged.bind(this))}wasShown(){i.Context.Context.instance().setFlavor(js,this),super.wasShown()}willHide(){super.willHide(),i.Context.Context.instance().setFlavor(js,null)}toolbarItems(){return this.toolbarItemsInternal}onDispatchFilterTypeChanged(e){const t=e.target;this.dispatchFilterBySetting.set(t.value)}showFrameworkListenersChanged(){const e=this.dispatchFilterBySetting.get(),t=e===zs.All||e===zs.Passive,n=e===zs.All||e===zs.Blocking;this.eventListenersView.showFrameworkListeners(this.showFrameworkListenersSetting.get(),t,n)}windowObjectInNodeContext(e){const t=e.domModel().runtimeModel().executionContexts();let n=t[0];if(e.frameId())for(let i=0;i<t.length;++i){const s=t[i];s.frameId===e.frameId()&&s.isDefault&&(n=s)}return n.evaluate({expression:"self",objectGroup:$s,includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1},!1,!1).then((e=>"object"in e?e.object:null))}eventListenersArrivedForTest(){}}const zs={All:"All",Blocking:"Blocking",Passive:"Passive"},$s="event-listeners-panel";var Ks=Object.freeze({__proto__:null,EventListenersWidget:js,DispatchFilterBy:zs,ActionDelegate:class{handleAction(e,t){return"elements.refresh-event-listeners"===t&&(js.instance().update(),!0)}}});const Gs=new CSSStyleSheet;Gs.replaceSync(".properties-widget-section{padding:2px 0 2px 5px;flex:none}.properties-widget-toolbar{border-bottom:1px solid var(--sys-color-divider);flex-shrink:0}.styles-pane-toolbar{width:100%}\n/*# sourceURL=propertiesWidget.css */\n");const Ys="properties-sidebar-pane",qs={showAll:"Show all",showAllTooltip:"When unchecked, only properties whose values are neither null nor undefined will be shown",noMatchingProperty:"No matching property"},Xs=r.i18n.registerUIStrings("panels/elements/PropertiesWidget.ts",qs),Qs=r.i18n.getLocalizedString.bind(void 0,Xs);class Js extends i.ThrottledWidget.ThrottledWidget{node;showAllPropertiesSetting;filterRegex=null;noMatchesElement;treeOutline;expandController;lastRequestedNode;constructor(t){super(!0,t),this.showAllPropertiesSetting=e.Settings.Settings.instance().createSetting("show-all-properties",!1),this.showAllPropertiesSetting.addChangeListener(this.filterList.bind(this)),n.TargetManager.TargetManager.instance().addModelListener(n.DOMModel.DOMModel,n.DOMModel.Events.AttrModified,this.onNodeChange,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.DOMModel.DOMModel,n.DOMModel.Events.AttrRemoved,this.onNodeChange,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.DOMModel.DOMModel,n.DOMModel.Events.CharacterDataModified,this.onNodeChange,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.DOMModel.DOMModel,n.DOMModel.Events.ChildNodeCountUpdated,this.onNodeChange,this,{scoped:!0}),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.setNode,this),this.node=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);const r=this.contentElement.createChild("div","hbox properties-widget-toolbar"),a=new i.Toolbar.Toolbar("styles-pane-toolbar",r),l=new i.Toolbar.ToolbarFilter(void 0,1,1,void 0,void 0,!1);l.addEventListener("TextChanged",this.onFilterChanged,this),a.appendToolbarItem(l),a.appendToolbarItem(new i.Toolbar.ToolbarSettingCheckbox(this.showAllPropertiesSetting,Qs(qs.showAllTooltip),Qs(qs.showAll))),this.contentElement.setAttribute("jslog",`${s.pane("element-properties").track({resize:!0})}`),this.noMatchesElement=this.contentElement.createChild("div","gray-info-message hidden"),this.noMatchesElement.textContent=Qs(qs.noMatchingProperty),this.treeOutline=new k.ObjectPropertiesSection.ObjectPropertiesSectionsTreeOutline({readOnly:!0}),this.treeOutline.setShowSelectionOnKeyboardFocus(!0,!1),this.expandController=new k.ObjectPropertiesSection.ObjectPropertiesSectionsTreeExpandController(this.treeOutline),this.contentElement.appendChild(this.treeOutline.element),this.treeOutline.addEventListener(i.TreeOutline.Events.ElementExpanded,(()=>{o.userMetrics.actionTaken(o.UserMetrics.Action.DOMPropertiesExpanded)})),this.update()}onFilterChanged(e){this.filterRegex=e.data?new RegExp(a.StringUtilities.escapeForRegExp(e.data),"i"):null,this.filterList()}filterList(){let e=!0;for(const t of this.treeOutline.rootElement().children()){const{property:n}=t,i=!n?.match({includeNullOrUndefinedValues:this.showAllPropertiesSetting.get(),regex:this.filterRegex});i||(e=!1),t.hidden=i}this.noMatchesElement.classList.toggle("hidden",!e)}setNode(e){this.node=e.data,this.update()}async doUpdate(){if(this.lastRequestedNode&&(this.lastRequestedNode.domModel().runtimeModel().releaseObjectGroup(Ys),delete this.lastRequestedNode),!this.node)return void this.treeOutline.removeChildren();this.lastRequestedNode=this.node;const e=await this.node.resolveToObject(Ys);if(!e)return;const t=this.treeOutline.rootElement();let{properties:i}=await n.RemoteObject.RemoteObject.loadFromObjectPerProto(e,!0);t.removeChildren(),null===i&&(i=[]),k.ObjectPropertiesSection.ObjectPropertyTreeElement.populateWithProperties(t,i,null,!0,!0,e),this.filterList()}onNodeChange(e){if(!this.node)return;const t=e.data,i=t instanceof n.DOMModel.DOMNode?t:t.node;this.node===i&&this.update()}wasShown(){super.wasShown(),this.registerCSSFiles([Gs])}}var Zs=Object.freeze({__proto__:null,PropertiesWidget:Js});const eo=new CSSStyleSheet;eo.replaceSync(".stack-trace{font-size:11px!important;font-family:Menlo,monospace}\n/*# sourceURL=nodeStackTraceWidget.css */\n");const to={noStackTraceAvailable:"No stack trace available"},no=r.i18n.registerUIStrings("panels/elements/NodeStackTraceWidget.ts",to),io=r.i18n.getLocalizedString.bind(void 0,no);class so extends i.ThrottledWidget.ThrottledWidget{noStackTraceElement;creationStackTraceElement;linkifier;constructor(){super(!0),this.noStackTraceElement=this.contentElement.createChild("div","gray-info-message"),this.noStackTraceElement.textContent=io(to.noStackTraceAvailable),this.creationStackTraceElement=this.contentElement.createChild("div","stack-trace"),this.linkifier=new f.Linkifier.Linkifier(oo)}wasShown(){super.wasShown(),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.update,this),this.registerCSSFiles([eo]),this.update()}willHide(){i.Context.Context.instance().removeFlavorChangeListener(n.DOMModel.DOMNode,this.update,this)}async doUpdate(){const e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!e)return this.noStackTraceElement.classList.remove("hidden"),void this.creationStackTraceElement.classList.add("hidden");const t=await e.creationStackTrace();if(t){this.noStackTraceElement.classList.add("hidden"),this.creationStackTraceElement.classList.remove("hidden");const n=f.JSPresentationUtils.buildStackTracePreviewContents(e.domModel().target(),this.linkifier,{stackTrace:t,tabStops:void 0});this.creationStackTraceElement.removeChildren(),this.creationStackTraceElement.appendChild(n.element)}else this.noStackTraceElement.classList.remove("hidden"),this.creationStackTraceElement.classList.add("hidden")}}const oo=40;var ro=Object.freeze({__proto__:null,NodeStackTraceWidget:so,MaxLengthForLinks:oo});const ao=new CSSStyleSheet;ao.replaceSync('.styles-element-classes-pane{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);padding:6px 2px 2px}.styles-element-classes-container{display:flex;flex-wrap:wrap;justify-content:flex-start}.styles-element-classes-pane [is="dt-checkbox"]{margin-right:15px}.styles-element-classes-pane .title-container{padding-bottom:2px}.styles-element-classes-pane .new-class-input{padding-left:3px;padding-right:3px;overflow:hidden;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;line-height:15px;margin-left:3px;width:calc(100% - 7px);background-color:var(--sys-color-cdt-base-container);cursor:text;&:hover{box-shadow:0 0 0 1px var(--ref-palette-neutral90)}}\n/*# sourceURL=classesPaneWidget.css */\n');const lo={addNewClass:"Add new class",classesSAdded:"Classes {PH1} added",classSAdded:"Class {PH1} added",elementClasses:"Element Classes"},co=r.i18n.registerUIStrings("panels/elements/ClassesPaneWidget.ts",lo),ho=r.i18n.getLocalizedString.bind(void 0,co);class po extends i.Widget.Widget{input;classesContainer;prompt;mutatingNodes;pendingNodeClasses;updateNodeThrottler;previousTarget;constructor(){super(!0),this.contentElement.className="styles-element-classes-pane",this.contentElement.setAttribute("jslog",`${s.pane("elements-classes")}`);const t=this.contentElement.createChild("div","title-container");this.input=t.createChild("div","new-class-input monospace"),this.setDefaultFocusedElement(this.input),this.classesContainer=this.contentElement.createChild("div","source-code"),this.classesContainer.classList.add("styles-element-classes-container"),this.prompt=new yo(this.nodeClasses.bind(this)),this.prompt.setAutocompletionTimeout(0),this.prompt.renderAsBlock();const o=this.prompt.attach(this.input);this.prompt.setPlaceholder(ho(lo.addNewClass)),this.prompt.addEventListener("TextChanged",this.onTextChanged,this),o.addEventListener("keydown",this.onKeyDown.bind(this),!1),n.TargetManager.TargetManager.instance().addModelListener(n.DOMModel.DOMModel,n.DOMModel.Events.DOMMutated,this.onDOMMutated,this,{scoped:!0}),this.mutatingNodes=new Set,this.pendingNodeClasses=new Map,this.updateNodeThrottler=new e.Throttler.Throttler(0),this.previousTarget=null,i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.onSelectedNodeChanged,this)}splitTextIntoClasses(e){return e.split(/[,\s]/).map((e=>e.trim())).filter((e=>e.length))}onKeyDown(e){if("Enter"!==e.key&&!a.KeyboardUtilities.isEscKey(e))return;if("Enter"===e.key&&(e.consume(),this.prompt.acceptAutoComplete()))return;const t=e.target;let s=t.textContent;a.KeyboardUtilities.isEscKey(e)&&(a.StringUtilities.isWhitespace(s)||e.consume(!0),s=""),this.prompt.clearAutocomplete(),t.textContent="";const o=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!o)return;const r=this.splitTextIntoClasses(s);if(!r.length)return void this.installNodeClasses(o);for(const e of r)this.toggleClass(o,e,!0);const l=r.join(" "),d=r.length>1?ho(lo.classesSAdded,{PH1:l}):ho(lo.classSAdded,{PH1:l});i.ARIAUtils.alert(d),this.installNodeClasses(o),this.update()}onTextChanged(){const e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);e&&this.installNodeClasses(e)}onDOMMutated(e){const t=e.data;this.mutatingNodes.has(t)||(uo.delete(t),this.update())}onSelectedNodeChanged(e){this.previousTarget&&this.prompt.text()&&(this.input.textContent="",this.installNodeClasses(this.previousTarget)),this.previousTarget=e.data,this.update()}wasShown(){super.wasShown(),this.update(),this.registerCSSFiles([ao])}update(){if(!this.isShowing())return;let e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),this.classesContainer.removeChildren(),this.input.disabled=!e,!e)return;const t=this.nodeClasses(e),s=[...t.keys()];s.sort(a.StringUtilities.caseInsensetiveComparator);for(const e of s){const n=i.UIUtils.CheckboxLabel.create(e,t.get(e),void 0,"element-class",!0);n.classList.add("monospace"),n.checkboxElement.addEventListener("click",this.onClick.bind(this,e),!1),this.classesContainer.appendChild(n)}}onClick(e,t){const s=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!s)return;const o=t.target.checked;this.toggleClass(s,e,o),this.installNodeClasses(s)}nodeClasses(e){let t=uo.get(e);if(!t){const n=(e.getAttribute("class")||"").split(/\s/);t=new Map;for(let e=0;e<n.length;++e){const i=n[e].trim();i.length&&t.set(i,!0)}uo.set(e,t)}return t}toggleClass(e,t,n){this.nodeClasses(e).set(t,n)}installNodeClasses(e){const t=this.nodeClasses(e),n=new Set;for(const e of t.keys())t.get(e)&&n.add(e);const i=this.splitTextIntoClasses(this.prompt.textWithCurrentSuggestion());for(const e of i)n.add(e);const s=[...n.values()].sort();this.pendingNodeClasses.set(e,s.join(" ")),this.updateNodeThrottler.schedule(this.flushPendingClasses.bind(this))}async flushPendingClasses(){const e=[];for(const n of this.pendingNodeClasses.keys()){this.mutatingNodes.add(n);const i=n.setAttributeValuePromise("class",this.pendingNodeClasses.get(n)).then(t.bind(this,n));e.push(i)}function t(e){this.mutatingNodes.delete(e)}this.pendingNodeClasses.clear(),await Promise.all(e)}}const uo=new WeakMap;let mo;class go{button;view;constructor(){this.button=new i.Toolbar.ToolbarToggle(ho(lo.elementClasses),""),this.button.setText(".cls"),this.button.element.classList.add("monospace"),this.button.element.setAttribute("jslog",`${s.toggleSubpane("elements-classes").track({click:!0})}`),this.button.addEventListener("Click",this.clicked,this),this.view=new po}static instance(e={forceNew:null}){const{forceNew:t}=e;return mo&&!t||(mo=new go),mo}clicked(){Ls.instance().showToolbarPane(this.view.isShowing()?null:this.view,this.button)}item(){return this.button}}class yo extends i.TextPrompt.TextPrompt{nodeClasses;selectedFrameId;classNamesPromise;constructor(e){super(),this.nodeClasses=e,this.initialize(this.buildClassNameCompletions.bind(this)," "),this.disableDefaultSuggestionForEmptyInput(),this.selectedFrameId="",this.classNamesPromise=null}async getClassNames(e){const t=[],n=new Set;this.selectedFrameId=e.frameId();const i=e.domModel().cssModel(),s=i.allStyleSheets();for(const e of s){if(e.frameId!==this.selectedFrameId)continue;const s=i.getClassNames(e.id).then((e=>{for(const t of e)n.add(t)}));t.push(s)}const o=e.ownerDocument.id,r=e.domModel().classNamesPromise(o).then((e=>{for(const t of e)n.add(t)}));return t.push(r),await Promise.all(t),[...n]}async buildClassNameCompletions(e,t,s){t&&!s||(this.classNamesPromise=null);const o=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!o||!t&&!s&&!e.trim())return[];this.classNamesPromise&&this.selectedFrameId===o.frameId()||(this.classNamesPromise=this.getClassNames(o));let r=await this.classNamesPromise;const a=this.nodeClasses(o);return r=r.filter((e=>!a.get(e))),"."===t[0]&&(r=r.map((e=>"."+e))),r.filter((e=>e.startsWith(t))).sort().map((e=>({text:e,title:void 0,subtitle:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0})))}}var fo=Object.freeze({__proto__:null,ClassesPaneWidget:po,ButtonProvider:go,ClassNamePrompt:yo});const So=new CSSStyleSheet;So.replaceSync(".styles-element-state-pane{overflow:hidden;padding-left:2px;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);margin-top:0;padding-bottom:2px}.styles-element-state-pane > .page-state-checkbox{margin-block:6px;display:flex;align-items:center;gap:2px}.styles-element-state-pane > .section-header{margin:8px 4px 6px;color:var(--color-text-secondary)}.styles-element-state-pane > table{width:100%;border-spacing:0}.styles-element-state-pane td{padding:0}\n/*# sourceURL=elementStatePaneWidget.css */\n");const Co={forceElementState:"Force element state",toggleElementState:"Toggle Element State",emulateFocusedPage:"Emulate a focused page",emulatesAFocusedPage:"Keep page focused. Commonly used for debugging disappearing elements."},Eo=r.i18n.registerUIStrings("panels/elements/ElementStatePaneWidget.ts",Co),bo=r.i18n.getLocalizedString.bind(void 0,Eo);class vo extends i.Widget.Widget{inputs;inputStates;cssModel;constructor(){super(!0),this.contentElement.className="styles-element-state-pane",this.contentElement.setAttribute("jslog",`${s.pane("element-states")}`);const t=[];this.inputs=t,this.inputStates=new WeakMap;const r=e=>{const t=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(!(t&&e.target instanceof HTMLInputElement))return;const s=this.inputStates.get(e.target);s&&t.domModel().cssModel().forcePseudoState(t,s,e.target.checked)},a=e=>{const n=document.createElement("td"),o=i.UIUtils.CheckboxLabel.create(":"+e,void 0,void 0,void 0,!0),a=o.checkboxElement;return this.inputStates.set(a,e),a.addEventListener("click",r,!1),a.setAttribute("jslog",`${s.toggle().track({click:!0}).context(e)}`),t.push(a),n.appendChild(o),n};this.contentElement.className="styles-element-state-pane";const l=(()=>{const t=document.createElement("div");t.classList.add("page-state-checkbox");const n=i.UIUtils.CheckboxLabel.create(bo(Co.emulateFocusedPage),void 0,void 0,void 0,!0);i.SettingsUI.bindCheckbox(n.checkboxElement,e.Settings.Settings.instance().moduleSetting("emulate-page-focus"),{enable:o.UserMetrics.Action.ToggleEmulateFocusedPageFromStylesPaneOn,disable:o.UserMetrics.Action.ToggleEmulateFocusedPageFromStylesPaneOff}),i.Tooltip.Tooltip.install(n.textElement,bo(Co.emulatesAFocusedPage));const s=i.XLink.XLink.create("https://goo.gle/devtools-emulate-focused-page",void 0,void 0,void 0,"learn-more");s.textContent="",s.style.setProperty("display","inline-flex");const r=new w.Icon.Icon;return r.data={iconName:"help",color:"var(--icon-default)",width:"16px",height:"16px"},s.prepend(r),t.appendChild(n),t.appendChild(s),t})();this.contentElement.appendChild(l),this.contentElement.appendChild((e=>{const t=document.createElement("div");return t.classList.add("section-header"),i.UIUtils.createTextChild(t.createChild("span"),e),t})(bo(Co.forceElementState)));const d=document.createElement("table");d.classList.add("source-code"),i.ARIAUtils.markAsPresentation(d);let c=d.createChild("tr");c.appendChild(a("active")),c.appendChild(a("hover")),c=d.createChild("tr"),c.appendChild(a("focus")),c.appendChild(a("visited")),c=d.createChild("tr"),c.appendChild(a("focus-within")),c.appendChild(a("focus-visible")),c=d.createChild("tr"),c.appendChild(a("target")),this.contentElement.appendChild(d),i.Context.Context.instance().addFlavorChangeListener(n.DOMModel.DOMNode,this.update,this)}updateModel(e){this.cssModel!==e&&(this.cssModel&&this.cssModel.removeEventListener(n.CSSModel.Events.PseudoStateForced,this.update,this),this.cssModel=e,this.cssModel&&this.cssModel.addEventListener(n.CSSModel.Events.PseudoStateForced,this.update,this))}wasShown(){super.wasShown(),this.registerCSSFiles([So]),this.update()}update(){let e=i.Context.Context.instance().flavor(n.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),this.updateModel(e?e.domModel().cssModel():null),e){const t=e.domModel().cssModel().pseudoState(e);for(const n of this.inputs){n.disabled=Boolean(e.pseudoType());const i=this.inputStates.get(n);n.checked=!(!t||void 0===i)&&t.indexOf(i)>=0}}else for(const e of this.inputs)e.disabled=!0,e.checked=!1;xo.instance().item().setToggled(this.inputs.some((e=>e.checked)))}}let wo;class xo{button;view;constructor(){this.button=new i.Toolbar.ToolbarToggle(bo(Co.toggleElementState),""),this.button.setText(r.i18n.lockedString(":hov")),this.button.setToggleWithDot(!0),this.button.addEventListener("Click",this.clicked,this),this.button.element.classList.add("monospace"),this.button.element.setAttribute("jslog",`${s.toggleSubpane("element-states").track({click:!0})}`),this.view=new vo}static instance(e={forceNew:null}){const{forceNew:t}=e;return wo&&!t||(wo=new xo),wo}clicked(){Ls.instance().showToolbarPane(this.view.isShowing()?null:this.view,null)}item(){return this.button}}var To=Object.freeze({__proto__:null,ElementStatePaneWidget:vo,ButtonProvider:xo});export{H as AccessibilityTreeUtils,z as AccessibilityTreeView,Dt as CSSRuleValidator,fo as ClassesPaneWidget,J as ColorSwatchPopoverIcon,te as ComputedStyleModel,Ii as ComputedStyleWidget,Mn as DOMLinkifier,Bi as DOMPath,To as ElementStatePaneWidget,Ds as ElementsPanel,Je as ElementsSidebarPane,Es as ElementsTreeElement,vs as ElementsTreeElementHighlighter,ns as ElementsTreeOutline,Ks as EventListenersWidget,Bs as InspectElementModeController,at as LayersWidget,cs as MarkerDecorator,Ts as MetricsSidebarPane,ro as NodeStackTraceWidget,ce as PlatformFontsWidget,Zs as PropertiesWidget,$e as PropertyMatchers,yi as PropertyRenderer,yn as StyleEditorWidget,_n as StylePropertiesSection,Hn as StylePropertyHighlighter,cn as StylePropertyTreeElement,Ut as StylePropertyUtils,ai as StylesSidebarPane,zi as TopLayerContainer,zn as WebCustomData};
