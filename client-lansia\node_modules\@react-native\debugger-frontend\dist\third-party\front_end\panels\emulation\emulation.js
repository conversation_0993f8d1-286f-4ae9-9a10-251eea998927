import*as e from"../../core/host/host.js";import*as t from"../../ui/legacy/legacy.js";import*as i from"../../ui/legacy/theme_support/theme_support.js";import*as o from"../../core/root/root.js";import*as s from"../../core/sdk/sdk.js";import*as n from"../../models/emulation/emulation.js";import*as r from"../../core/common/common.js";import*as a from"../../core/i18n/i18n.js";import*as d from"../../core/platform/platform.js";import*as l from"../../ui/visual_logging/visual_logging.js";import*as c from"../mobile_throttling/mobile_throttling.js";import*as h from"./components/components.js";import*as m from"../../models/bindings/bindings.js";var p={cssContent:".device-mode-x{margin:0 1px;font-size:16px}.device-mode-empty-toolbar-element{width:0}\n/*# sourceURL=deviceModeToolbar.css */\n"};const u={dimensions:"Dimensions",width:"Width",heightLeaveEmptyForFull:"Height (leave empty for full)",zoom:"Zoom",devicePixelRatio:"Device pixel ratio",deviceType:"Device type",experimentalWebPlatformFeature:'"`Experimental Web Platform Feature`" flag is enabled. Click to disable it.',experimentalWebPlatformFeatureFlag:'"`Experimental Web Platform Feature`" flag is disabled. Click to enable it.',moreOptions:"More options",fitToWindowF:"Fit to window ({PH1}%)",autoadjustZoom:"Auto-adjust zoom",defaultF:"Default: {PH1}",hideDeviceFrame:"Hide device frame",showDeviceFrame:"Show device frame",hideMediaQueries:"Hide media queries",showMediaQueries:"Show media queries",hideRulers:"Hide rulers",showRulers:"Show rulers",removeDevicePixelRatio:"Remove device pixel ratio",addDevicePixelRatio:"Add device pixel ratio",removeDeviceType:"Remove device type",addDeviceType:"Add device type",resetToDefaults:"Reset to defaults",closeDevtools:"Close DevTools",responsive:"Responsive",edit:"Edit…",portrait:"Portrait",landscape:"Landscape",rotate:"Rotate",none:"None",screenOrientationOptions:"Screen orientation options",toggleDualscreenMode:"Toggle dual-screen mode",devicePosture:"Device posture"},g=a.i18n.registerUIStrings("panels/emulation/DeviceModeToolbar.ts",u),v=a.i18n.getLocalizedString.bind(void 0,g);function b(e,t){e.setTitle(t),e.element.title=t}class S{model;showMediaInspectorSetting;showRulersSetting;deviceOutlineSetting;showDeviceScaleFactorSetting;showUserAgentTypeSetting;autoAdjustScaleSetting;lastMode;elementInternal;emulatedDevicesList;persistenceSetting;spanButton;postureItem;modeButton;widthInput;heightInput;deviceScaleItem;deviceSelectItem;scaleItem;uaItem;experimentalButton;cachedDeviceScale;cachedUaType;xItem;throttlingConditionsItem;cachedModelType;cachedScale;cachedModelDevice;cachedModelMode;constructor(e,i,o){this.model=e,this.showMediaInspectorSetting=i,this.showRulersSetting=o,this.deviceOutlineSetting=this.model.deviceOutlineSetting(),this.showDeviceScaleFactorSetting=r.Settings.Settings.instance().createSetting("emulation.show-device-scale-factor",!1),this.showDeviceScaleFactorSetting.addChangeListener(this.updateDeviceScaleFactorVisibility,this),this.showUserAgentTypeSetting=r.Settings.Settings.instance().createSetting("emulation.show-user-agent-type",!1),this.showUserAgentTypeSetting.addChangeListener(this.updateUserAgentTypeVisibility,this),this.autoAdjustScaleSetting=r.Settings.Settings.instance().createSetting("emulation.auto-adjust-scale",!0),this.lastMode=new Map,this.elementInternal=document.createElement("div"),this.elementInternal.classList.add("device-mode-toolbar"),this.elementInternal.setAttribute("jslog",`${l.toolbar("device-mode").track({resize:!0})}`);const s=this.elementInternal.createChild("div","device-mode-toolbar-spacer");s.createChild("div","device-mode-toolbar-spacer");const a=new t.Toolbar.Toolbar("",s);this.fillLeftToolbar(a);const d=new t.Toolbar.Toolbar("",this.elementInternal);d.makeWrappable(),this.widthInput=new h.DeviceSizeInputElement.SizeInputElement(v(u.width),{jslogContext:"width"}),this.widthInput.addEventListener("sizechanged",(({size:e})=>{this.autoAdjustScaleSetting.get()?this.model.setWidthAndScaleToFit(e):this.model.setWidth(e)})),this.heightInput=new h.DeviceSizeInputElement.SizeInputElement(v(u.heightLeaveEmptyForFull),{jslogContext:"height"}),this.heightInput.addEventListener("sizechanged",(({size:e})=>{this.autoAdjustScaleSetting.get()?this.model.setHeightAndScaleToFit(e):this.model.setHeight(e)})),this.fillMainToolbar(d);const c=this.elementInternal.createChild("div","device-mode-toolbar-spacer"),m=new t.Toolbar.Toolbar("device-mode-toolbar-fixed-size",c);m.makeWrappable(),this.fillRightToolbar(m);const p=new t.Toolbar.Toolbar("device-mode-toolbar-fixed-size",c);p.makeWrappable(),this.fillModeToolbar(p),c.createChild("div","device-mode-toolbar-spacer");const g=new t.Toolbar.Toolbar("device-mode-toolbar-options",c);function b(){const t=e.toolbarControlsEnabledSetting().get();a.setEnabled(t),d.setEnabled(t),m.setEnabled(t),p.setEnabled(t),g.setEnabled(t)}g.makeWrappable(),this.fillOptionsToolbar(g),this.emulatedDevicesList=n.EmulatedDevices.EmulatedDevicesList.instance(),this.emulatedDevicesList.addEventListener("CustomDevicesUpdated",this.deviceListChanged,this),this.emulatedDevicesList.addEventListener("StandardDevicesUpdated",this.deviceListChanged,this),this.persistenceSetting=r.Settings.Settings.instance().createSetting("emulation.device-mode-value",{device:"",orientation:"",mode:""}),this.model.toolbarControlsEnabledSetting().addChangeListener(b),b()}createEmptyToolbarElement(){const e=document.createElement("div");return e.classList.add("device-mode-empty-toolbar-element"),e}fillLeftToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.deviceSelectItem=new t.Toolbar.ToolbarMenuButton(this.appendDeviceMenuItems.bind(this),void 0,void 0,"device"),this.deviceSelectItem.turnShrinkable(),this.deviceSelectItem.setDarkText(),e.appendToolbarItem(this.deviceSelectItem)}fillMainToolbar(e){e.appendToolbarItem(new t.Toolbar.ToolbarItem(this.widthInput));const i=document.createElement("div");i.classList.add("device-mode-x"),i.textContent="×",this.xItem=this.wrapToolbarItem(i),e.appendToolbarItem(this.xItem),e.appendToolbarItem(new t.Toolbar.ToolbarItem(this.heightInput))}fillRightToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.scaleItem=new t.Toolbar.ToolbarMenuButton(this.appendScaleMenuItems.bind(this),void 0,void 0,"scale"),b(this.scaleItem,v(u.zoom)),this.scaleItem.setDarkText(),e.appendToolbarItem(this.scaleItem),e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.deviceScaleItem=new t.Toolbar.ToolbarMenuButton(this.appendDeviceScaleMenuItems.bind(this),void 0,void 0,"device-pixel-ratio"),this.deviceScaleItem.setVisible(this.showDeviceScaleFactorSetting.get()),b(this.deviceScaleItem,v(u.devicePixelRatio)),this.deviceScaleItem.setDarkText(),e.appendToolbarItem(this.deviceScaleItem),e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.uaItem=new t.Toolbar.ToolbarMenuButton(this.appendUserAgentMenuItems.bind(this),void 0,void 0,"device-type"),this.uaItem.setVisible(this.showUserAgentTypeSetting.get()),b(this.uaItem,v(u.deviceType)),this.uaItem.setDarkText(),e.appendToolbarItem(this.uaItem),this.throttlingConditionsItem=c.ThrottlingManager.throttlingManager().createMobileThrottlingButton(),e.appendToolbarItem(this.throttlingConditionsItem)}fillModeToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.modeButton=new t.Toolbar.ToolbarButton("","screen-rotation",void 0,"screen-rotation"),this.modeButton.addEventListener("Click",this.modeMenuClicked,this),e.appendToolbarItem(this.modeButton),this.spanButton=new t.Toolbar.ToolbarButton("","device-fold",void 0,"device-fold"),this.spanButton.addEventListener("Click",this.spanClicked,this),e.appendToolbarItem(this.spanButton),e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.postureItem=new t.Toolbar.ToolbarMenuButton(this.appendDevicePostureItems.bind(this),void 0,void 0,"device-posture"),this.postureItem.setDarkText(),b(this.postureItem,v(u.devicePosture)),e.appendToolbarItem(this.postureItem),this.createExperimentalButton(e)}createExperimentalButton(e){e.appendToolbarItem(new t.Toolbar.ToolbarSeparator(!0));const i=this.model.webPlatformExperimentalFeaturesEnabled()?v(u.experimentalWebPlatformFeature):v(u.experimentalWebPlatformFeatureFlag);this.experimentalButton=new t.Toolbar.ToolbarToggle(i,"experiment-check"),this.experimentalButton.setToggled(this.model.webPlatformExperimentalFeaturesEnabled()),this.experimentalButton.setEnabled(!0),this.experimentalButton.addEventListener("Click",this.experimentalClicked,this),e.appendToolbarItem(this.experimentalButton)}experimentalClicked(){e.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab("chrome://flags/#enable-experimental-web-platform-features")}fillOptionsToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement()));const i=new t.Toolbar.ToolbarMenuButton(this.appendOptionsMenuItems.bind(this),void 0,void 0,"more-options");b(i,v(u.moreOptions)),e.appendToolbarItem(i)}appendDevicePostureItems(e){for(const t of["Continuous","Folded"])e.defaultSection().appendCheckboxItem(t,this.spanClicked.bind(this),{checked:t===this.currentDevicePosture(),jslogContext:t.toLowerCase()})}currentDevicePosture(){const e=this.model.mode();return!e||e.orientation!==n.EmulatedDevices.VerticalSpanned&&e.orientation!==n.EmulatedDevices.HorizontalSpanned?"Continuous":"Folded"}appendScaleMenuItems(e){this.model.type()===n.DeviceModeModel.Type.Device&&e.footerSection().appendItem(v(u.fitToWindowF,{PH1:this.getPrettyFitZoomPercentage()}),this.onScaleMenuChanged.bind(this,this.model.fitScale()),{jslogContext:"fit-to-window"}),e.footerSection().appendCheckboxItem(v(u.autoadjustZoom),this.onAutoAdjustScaleChanged.bind(this),{checked:this.autoAdjustScaleSetting.get(),jslogContext:"auto-adjust-zoom"});const t=function(t,i){e.defaultSection().appendCheckboxItem(t,this.onScaleMenuChanged.bind(this,i),{checked:this.model.scaleSetting().get()===i,jslogContext:t})}.bind(this);t("50%",.5),t("75%",.75),t("100%",1),t("125%",1.25),t("150%",1.5),t("200%",2)}onScaleMenuChanged(e){this.model.scaleSetting().set(e)}onAutoAdjustScaleChanged(){this.autoAdjustScaleSetting.set(!this.autoAdjustScaleSetting.get())}appendDeviceScaleMenuItems(e){const t=this.model.deviceScaleFactorSetting(),i="Mobile"===this.model.uaSetting().get()||"Mobile (no touch)"===this.model.uaSetting().get()?n.DeviceModeModel.defaultMobileScaleFactor:window.devicePixelRatio;function o(e,i,o,s){e.appendCheckboxItem(i,t.set.bind(t,o),{checked:t.get()===o,jslogContext:s})}o(e.headerSection(),v(u.defaultF,{PH1:i}),0,"dpr-default"),o(e.defaultSection(),"1",1,"dpr-1"),o(e.defaultSection(),"2",2,"dpr-2"),o(e.defaultSection(),"3",3,"dpr-3")}appendUserAgentMenuItems(e){const t=this.model.uaSetting();function i(i,o){e.defaultSection().appendCheckboxItem(i,t.set.bind(t,o),{checked:t.get()===o,jslogContext:d.StringUtilities.toKebabCase(o)})}i("Mobile","Mobile"),i("Mobile (no touch)","Mobile (no touch)"),i("Desktop","Desktop"),i("Desktop (touch)","Desktop (touch)")}appendOptionsMenuItems(t){const i=this.model;function o(e,t,o,s,r,a){void 0===r&&(r=i.type()===n.DeviceModeModel.Type.None);const d=t.get(),l=`${a}-${d?"disable":"enable"}`;e.appendItem(d?o:s,t.set.bind(t,!t.get()),{disabled:r,jslogContext:l})}o(t.headerSection(),this.deviceOutlineSetting,v(u.hideDeviceFrame),v(u.showDeviceFrame),i.type()!==n.DeviceModeModel.Type.Device,"device-frame"),o(t.headerSection(),this.showMediaInspectorSetting,v(u.hideMediaQueries),v(u.showMediaQueries),void 0,"media-queries"),o(t.headerSection(),this.showRulersSetting,v(u.hideRulers),v(u.showRulers),void 0,"rulers"),o(t.defaultSection(),this.showDeviceScaleFactorSetting,v(u.removeDevicePixelRatio),v(u.addDevicePixelRatio),void 0,"device-pixel-ratio"),o(t.defaultSection(),this.showUserAgentTypeSetting,v(u.removeDeviceType),v(u.addDeviceType),void 0,"device-type"),t.appendItemsAtLocation("deviceModeMenu"),t.footerSection().appendItem(v(u.resetToDefaults),this.reset.bind(this),{jslogContext:"reset-to-defaults"}),t.footerSection().appendItem(v(u.closeDevtools),e.InspectorFrontendHost.InspectorFrontendHostInstance.closeWindow.bind(e.InspectorFrontendHost.InspectorFrontendHostInstance),{jslogContext:"close-dev-tools"})}reset(){this.deviceOutlineSetting.set(!1),this.showDeviceScaleFactorSetting.set(!1),this.showUserAgentTypeSetting.set(!1),this.showMediaInspectorSetting.set(!1),this.showRulersSetting.set(!1),this.model.reset()}wrapToolbarItem(e){const i=document.createElement("div");return t.UIUtils.createShadowRootWithCoreStyles(i,{cssFile:p,delegatesFocus:void 0}).appendChild(e),new t.Toolbar.ToolbarItem(i)}emulateDevice(e){const t=this.autoAdjustScaleSetting.get()?void 0:this.model.scaleSetting().get();this.model.emulate(n.DeviceModeModel.Type.Device,e,this.lastMode.get(e)||e.modes[0],t)}switchToResponsive(){this.model.emulate(n.DeviceModeModel.Type.Responsive,null,null)}filterDevices(e){return(e=e.filter((function(e){return e.show()}))).sort(n.EmulatedDevices.EmulatedDevice.deviceComparator),e}standardDevices(){return this.filterDevices(this.emulatedDevicesList.standard())}customDevices(){return this.filterDevices(this.emulatedDevicesList.custom())}allDevices(){return this.standardDevices().concat(this.customDevices())}appendDeviceMenuItems(e){function t(t){if(!t.length)return;const i=e.section();for(const e of t)i.appendCheckboxItem(e.title,this.emulateDevice.bind(this,e),{checked:this.model.device()===e,jslogContext:d.StringUtilities.toKebabCase(e.title)})}e.headerSection().appendCheckboxItem(v(u.responsive),this.switchToResponsive.bind(this),{checked:this.model.type()===n.DeviceModeModel.Type.Responsive,jslogContext:"responsive"}),t.call(this,this.standardDevices()),t.call(this,this.customDevices()),e.footerSection().appendItem(v(u.edit),this.emulatedDevicesList.revealCustomSetting.bind(this.emulatedDevicesList),{jslogContext:"edit"})}deviceListChanged(){const e=this.model.device();if(!e)return;const t=this.allDevices();-1===t.indexOf(e)?t.length?this.emulateDevice(t[0]):this.model.emulate(n.DeviceModeModel.Type.Responsive,null,null):this.emulateDevice(e)}updateDeviceScaleFactorVisibility(){this.deviceScaleItem&&this.deviceScaleItem.setVisible(this.showDeviceScaleFactorSetting.get())}updateUserAgentTypeVisibility(){this.uaItem&&this.uaItem.setVisible(this.showUserAgentTypeSetting.get())}spanClicked(){const e=this.model.device();if(!e||!e.isDualScreen&&!e.isFoldableScreen)return;const t=this.autoAdjustScaleSetting.get()?void 0:this.model.scaleSetting().get(),i=this.model.mode();if(!i)return;const o=e.getSpanPartner(i);o&&this.model.emulate(this.model.type(),e,o,t)}modeMenuClicked(e){const i=this.model.device(),o=this.model,s=this.autoAdjustScaleSetting;if(o.type()===n.DeviceModeModel.Type.Responsive){const e=o.appliedDeviceSize();return void(s.get()?o.setSizeAndScaleToFit(e.height,e.width):(o.setWidth(e.height),o.setHeight(e.width)))}if(!i)return;if((i.isDualScreen||i.isFoldableScreen||2===i.modes.length)&&i.modes[0].orientation!==i.modes[1].orientation){const e=s.get()?void 0:o.scaleSetting().get(),t=o.mode();if(!t)return;const n=i.getRotationPartner(t);if(!n)return;return void o.emulate(o.type(),o.device(),n,e)}if(!this.modeButton)return;const r=new t.ContextMenu.ContextMenu(e.data,{useSoftMenu:!1,x:this.modeButton.element.getBoundingClientRect().left,y:this.modeButton.element.getBoundingClientRect().top+this.modeButton.element.offsetHeight});function a(e,t){if(!i)return;const o=i.modesForOrientation(e);if(o.length)if(1===o.length)d(o[0],t);else for(let e=0;e<o.length;e++)d(o[e],t+" – "+o[e].title)}function d(e,t){r.defaultSection().appendCheckboxItem(t,l.bind(null,e),{checked:o.mode()===e,jslogContext:"device-mode"})}function l(e){const t=s.get()?void 0:o.scaleSetting().get();o.emulate(o.type(),o.device(),e,t)}a(n.EmulatedDevices.Vertical,v(u.portrait)),a(n.EmulatedDevices.Horizontal,v(u.landscape)),r.show()}getPrettyFitZoomPercentage(){return`${(100*this.model.fitScale()).toFixed(0)}`}getPrettyZoomPercentage(){return`${(100*this.model.scale()).toFixed(0)}`}element(){return this.elementInternal}update(){this.model.type()!==this.cachedModelType&&(this.cachedModelType=this.model.type(),this.widthInput.disabled=this.model.type()!==n.DeviceModeModel.Type.Responsive,this.heightInput.disabled=this.model.type()!==n.DeviceModeModel.Type.Responsive,this.deviceScaleItem.setEnabled(this.model.type()===n.DeviceModeModel.Type.Responsive),this.uaItem.setEnabled(this.model.type()===n.DeviceModeModel.Type.Responsive),this.model.type()===n.DeviceModeModel.Type.Responsive?(this.modeButton.setEnabled(!0),b(this.modeButton,v(u.rotate))):this.modeButton.setEnabled(!1));const e=this.model.appliedDeviceSize();this.widthInput.size=String(e.width),this.heightInput.size=this.model.type()===n.DeviceModeModel.Type.Responsive&&this.model.isFullHeight()?"":String(e.height),this.heightInput.placeholder=String(e.height),this.model.scale()!==this.cachedScale&&(this.scaleItem.setText(`${this.getPrettyZoomPercentage()}%`),this.cachedScale=this.model.scale());const t=this.model.appliedDeviceScaleFactor();t!==this.cachedDeviceScale&&(this.deviceScaleItem.setText(`DPR: ${t.toFixed(1)}`),this.cachedDeviceScale=t);const i=this.model.appliedUserAgentType();i!==this.cachedUaType&&(this.uaItem.setText(i),this.cachedUaType=i);let o=v(u.none);this.model.type()===n.DeviceModeModel.Type.Responsive&&(o=v(u.responsive));const s=this.model.device();if(this.model.type()===n.DeviceModeModel.Type.Device&&s&&(o=s.title),this.deviceSelectItem.setText(`${v(u.dimensions)}: ${o}`),this.model.device()!==this.cachedModelDevice){const e=this.model.device();if(e){const t=e?e.modes.length:0;this.modeButton.setEnabled(t>=2),b(this.modeButton,v(2===t?u.rotate:u.screenOrientationOptions))}this.cachedModelDevice=e}if(this.experimentalButton){const e=this.model.device();e&&(e.isDualScreen||e.isFoldableScreen)?(e.isDualScreen?(this.spanButton.setVisible(!0),this.postureItem.setVisible(!1)):e.isFoldableScreen&&(this.spanButton.setVisible(!1),this.postureItem.setVisible(!0),this.postureItem.setText(this.currentDevicePosture())),this.experimentalButton.setVisible(!0)):(this.spanButton.setVisible(!1),this.postureItem.setVisible(!1),this.experimentalButton.setVisible(!1)),b(this.spanButton,v(u.toggleDualscreenMode))}if(this.model.type()===n.DeviceModeModel.Type.Device&&this.lastMode.set(this.model.device(),this.model.mode()),this.model.mode()!==this.cachedModelMode&&this.model.type()!==n.DeviceModeModel.Type.None){this.cachedModelMode=this.model.mode();const e=this.persistenceSetting.get(),t=this.model.device();if(t){e.device=t.title;const i=this.model.mode();e.orientation=i?i.orientation:"",e.mode=i?i.title:""}else e.device="",e.orientation="",e.mode="";this.persistenceSetting.set(e)}}restore(){for(const e of this.allDevices())if(e.title===this.persistenceSetting.get().device)for(const t of e.modes)if(t.orientation===this.persistenceSetting.get().orientation&&t.title===this.persistenceSetting.get().mode)return this.lastMode.set(e,t),void this.emulateDevice(e);this.model.emulate(n.DeviceModeModel.Type.Responsive,null,null)}}var f=Object.freeze({__proto__:null,DeviceModeToolbar:S}),w={cssContent:":host{overflow:hidden;align-items:stretch;flex:auto;background-color:var(--app-color-toolbar-background)}.device-mode-toolbar{flex:none;background-color:var(--app-color-toolbar-background);border-bottom:1px solid var(--sys-color-divider);display:flex;flex-direction:row;align-items:stretch}.device-mode-toolbar .toolbar{overflow:hidden;flex:0 100000 auto;padding:0 5px}.device-mode-toolbar .toolbar.device-mode-toolbar-fixed-size{flex:0 1 auto}.device-mode-toolbar-options.toolbar{position:sticky;right:0;flex:none}.device-mode-toolbar-spacer{flex:1 1 0;display:flex;flex-direction:row;overflow:hidden}.device-mode-content-clip{overflow:hidden;flex:auto}.device-mode-media-container{flex:none;overflow:hidden;box-shadow:inset 0 -1px var(--sys-color-divider)}.device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-media-container{margin-bottom:20px}.device-mode-presets-container{flex:0 0 20px;display:flex}.device-mode-presets-container-inner{flex:auto;justify-content:center;position:relative;background-color:var(--sys-color-surface1);border-bottom:1px solid var(--sys-color-divider)}.device-mode-presets-container:hover{transition:opacity 0.1s;transition-delay:50ms;opacity:100%}.device-mode-preset-bar-outer{pointer-events:none;display:flex;justify-content:center}.device-mode-preset-bar{border-left:2px solid var(--sys-color-on-base-divider);border-right:2px solid var(--sys-color-on-base-divider);pointer-events:auto;text-align:center;flex:none;color:var(--sys-color-on-surface);display:flex;align-items:center;justify-content:center;white-space:nowrap;margin-bottom:1px}.device-mode-preset-bar:hover{transition:background-color 0.1s;transition-delay:50ms;background-color:var(--sys-color-state-hover-on-subtle)}.device-mode-preset-bar > span{visibility:hidden}.device-mode-preset-bar:hover > span{transition:visibility 0.1s;transition-delay:50ms;visibility:visible}.device-mode-content-area{flex:auto;position:relative;margin:0}.device-mode-screen-area{position:absolute;left:0;right:0;width:0;height:0;background-color:var(--sys-color-inverse-surface)}.device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area{--override-screen-area-box-shadow:hsl(240deg 3% 84%) 0 0 0 0.5px,hsl(0deg 0% 80%/40%) 0 0 20px;box-shadow:var(--override-screen-area-box-shadow)}.theme-with-dark-background .device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area,\n:host-context(.theme-with-dark-background) .device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area{--override-screen-area-box-shadow:rgb(40 40 42) 0 0 0 0.5px,rgb(51 51 51/40%) 0 0 20px}.device-mode-screen-image{position:absolute;left:0;top:0;width:100%;height:100%}.device-mode-resizer{position:absolute;display:flex;align-items:center;justify-content:center;overflow:hidden;transition:background-color 0.1s ease,opacity 0.1s ease}.device-mode-resizer:hover{background-color:var(--sys-color-state-hover-on-subtle);opacity:100%}.device-mode-resizer > div{pointer-events:none}.device-mode-right-resizer{top:0;bottom:-1px;right:-20px;width:20px}.device-mode-left-resizer{top:0;bottom:-1px;left:-20px;width:20px;opacity:0%}.device-mode-bottom-resizer{left:0;right:-1px;bottom:-20px;height:20px}.device-mode-bottom-right-resizer{left:0;top:0;right:-20px;bottom:-20px;background-color:var(--sys-color-surface1)}.device-mode-bottom-left-resizer{left:-20px;top:0;right:0;bottom:-20px;opacity:0%}.device-mode-right-resizer > div{content:var(--image-file-resizeHorizontal);width:6px;height:26px}.device-mode-left-resizer > div{content:var(--image-file-resizeHorizontal);width:6px;height:26px}.device-mode-bottom-resizer > div{content:var(--image-file-resizeVertical);margin-bottom:-2px;width:26px;height:6px}.device-mode-bottom-right-resizer > div{position:absolute;bottom:3px;right:3px;width:13px;height:13px;content:var(--image-file-resizeDiagonal)}.device-mode-bottom-left-resizer > div{position:absolute;bottom:3px;left:3px;width:13px;height:13px;content:var(--image-file-resizeDiagonal);transform:rotate(90deg)}.device-mode-page-area{position:absolute;left:0;right:0;width:0;height:0;display:flex;background-color:var(--sys-color-cdt-base-container)}.device-mode-ruler{position:absolute;overflow:visible}.device-mode-ruler-top{height:20px;right:0}.device-mode-ruler-left{width:20px;bottom:0}.device-mode-ruler-content{pointer-events:none;position:absolute;left:-20px;top:-20px}.device-mode-ruler-top .device-mode-ruler-content{border-top:1px solid transparent;right:0;bottom:20px;background-color:var(--sys-color-cdt-base-container)}.device-mode-ruler-left .device-mode-ruler-content{border-left:1px solid transparent;border-top:1px solid transparent;right:20px;bottom:0}.device-mode-content-clip.device-mode-outline-visible .device-mode-ruler-top .device-mode-ruler-content{border-top:1px solid var(--sys-color-token-subtle)}.device-mode-content-clip.device-mode-outline-visible .device-mode-ruler-left .device-mode-ruler-content{border-left:1px solid var(--sys-color-token-subtle);border-top:1px solid var(--sys-color-token-subtle)}.device-mode-ruler-inner{position:absolute}.device-mode-ruler-top .device-mode-ruler-inner{top:0;bottom:0;left:20px;right:0;border-bottom:1px solid var(--sys-color-token-subtle)}.device-mode-ruler-left .device-mode-ruler-inner{left:0;right:0;top:19px;bottom:0;border-right:1px solid var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container)}.device-mode-ruler-marker{position:absolute}.device-mode-ruler-top .device-mode-ruler-marker{width:0;height:5px;bottom:0;border-right:1px solid var(--sys-color-token-subtle);margin-right:-1px}.device-mode-ruler-top .device-mode-ruler-marker.device-mode-ruler-marker-medium{height:10px}.device-mode-ruler-top .device-mode-ruler-marker.device-mode-ruler-marker-large{height:15px}.device-mode-ruler-left .device-mode-ruler-marker{height:0;width:5px;right:0;border-bottom:1px solid var(--sys-color-token-subtle);margin-bottom:-1px}.device-mode-ruler-left .device-mode-ruler-marker.device-mode-ruler-marker-medium{width:10px}.device-mode-ruler-left .device-mode-ruler-marker.device-mode-ruler-marker-large{width:15px}.device-mode-ruler-text{color:var(--sys-color-token-subtle);position:relative;pointer-events:auto}.device-mode-ruler-text:hover{color:var(--sys-color-on-surface)}.device-mode-ruler-top .device-mode-ruler-text{left:2px;top:-2px}.device-mode-ruler-left .device-mode-ruler-text{left:-4px;top:-15px;transform:rotate(270deg)}\n/*# sourceURL=deviceModeView.css */\n"},M={cssContent:".media-inspector-view{height:50px}.media-inspector-marker-container{height:14px;margin:2px 0;position:relative}.media-inspector-bar{display:flex;flex-direction:row;align-items:stretch;pointer-events:none;position:absolute;left:0;right:0;top:0;bottom:0}.media-inspector-marker{flex:none;pointer-events:auto;margin:1px 0;white-space:nowrap;z-index:auto;position:relative}.media-inspector-marker-spacer{flex:auto}.media-inspector-marker:hover{margin:-1px 0;opacity:100%}.media-inspector-marker-min-width{flex:auto;background-color:var(--sys-color-yellow-container);border-right:2px solid var(--sys-color-yellow-bright);border-left:2px solid var(--sys-color-yellow-bright);&:hover{background-color:color-mix(in sRGB,var(--sys-color-yellow-container),var(--sys-color-yellow-bright) 30%)}}.media-inspector-marker-min-width-right{border-left:2px solid var(--sys-color-yellow-bright)}.media-inspector-marker-min-width-left{border-right:2px solid var(--sys-color-yellow-bright)}.media-inspector-marker-min-max-width{background-color:var(--sys-color-tertiary-container);border-left:2px solid var(--sys-color-tertiary);border-right:2px solid var(--sys-color-tertiary)}.media-inspector-marker-min-max-width:hover{z-index:1}.media-inspector-marker-max-width{background-color:var(--sys-color-inverse-primary);border-right:2px solid var(--sys-color-primary-bright);border-left:2px solid var(--sys-color-primary-bright)}.media-inspector-marker-inactive .media-inspector-marker-min-width:not(:hover){background-color:var(--sys-color-surface-yellow)}.media-inspector-marker-inactive .media-inspector-marker-min-max-width:not(:hover){background-color:color-mix(in sRGB,var(--sys-color-tertiary-container),var(--sys-color-cdt-base-container) 30%)}.media-inspector-marker-inactive .media-inspector-marker-max-width:not(:hover){background-color:var(--sys-color-tonal-container)}.media-inspector-marker-label-container{position:absolute;z-index:1}.media-inspector-marker:not(:hover) .media-inspector-marker-label-container{display:none}.media-inspector-marker-label-container-left{left:-2px}.media-inspector-marker-label-container-right{right:-2px}.media-inspector-marker-label{color:var(--sys-color-on-surface);position:absolute;top:1px;bottom:0;font-size:12px;pointer-events:none}.media-inspector-label-right{right:4px}.media-inspector-label-left{left:4px}\n/*# sourceURL=mediaQueryInspector.css */\n"};const x={revealInSourceCode:"Reveal in source code"},I=a.i18n.registerUIStrings("panels/emulation/MediaQueryInspector.ts",x),y=a.i18n.getLocalizedString.bind(void 0,I);class C extends t.Widget.Widget{mediaThrottler;getWidthCallback;setWidthCallback;scale;elementsToMediaQueryModel;elementsToCSSLocations;cssModel;cachedQueryModels;constructor(e,i,o){super(!0),this.registerRequiredCSS(M),this.contentElement.classList.add("media-inspector-view"),this.contentElement.setAttribute("jslog",`${l.mediaInspectorView().track({click:!0})}`),this.contentElement.addEventListener("click",this.onMediaQueryClicked.bind(this),!1),this.contentElement.addEventListener("contextmenu",this.onContextMenu.bind(this),!1),this.mediaThrottler=o,this.getWidthCallback=e,this.setWidthCallback=i,this.scale=1,this.elementsToMediaQueryModel=new WeakMap,this.elementsToCSSLocations=new WeakMap,s.TargetManager.TargetManager.instance().observeModels(s.CSSModel.CSSModel,this),t.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.renderMediaQueries.bind(this),this)}modelAdded(e){e.target()===s.TargetManager.TargetManager.instance().primaryPageTarget()&&(this.cssModel=e,this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetAdded,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetRemoved,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetChanged,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.MediaQueryResultChanged,this.scheduleMediaQueriesUpdate,this))}modelRemoved(e){e===this.cssModel&&(this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetAdded,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetRemoved,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetChanged,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.MediaQueryResultChanged,this.scheduleMediaQueriesUpdate,this),delete this.cssModel)}setAxisTransform(e){Math.abs(this.scale-e)<1e-8||(this.scale=e,this.renderMediaQueries())}onMediaQueryClicked(e){const t=e.target.enclosingNodeOrSelfWithClass("media-inspector-bar");if(!t)return;const i=this.elementsToMediaQueryModel.get(t);if(!i)return;const o=i.maxWidthExpression(),s=i.minWidthExpression();if(0===i.section())return void this.setWidthCallback(o&&o.computedLength()||0);if(2===i.section())return void this.setWidthCallback(s&&s.computedLength()||0);const n=this.getWidthCallback();s&&n!==s.computedLength()?this.setWidthCallback(s.computedLength()||0):this.setWidthCallback(o&&o.computedLength()||0)}onContextMenu(e){if(!this.cssModel||!this.cssModel.isEnabled())return;const i=e.target.enclosingNodeOrSelfWithClass("media-inspector-bar");if(!i)return;const o=this.elementsToCSSLocations.get(i)||[],s=new Map;for(let e=0;e<o.length;++e){const t=m.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(o[e]);if(!t)continue;const i="number"==typeof t.columnNumber?d.StringUtilities.sprintf("%s:%d:%d",t.uiSourceCode.url(),t.lineNumber+1,t.columnNumber+1):d.StringUtilities.sprintf("%s:%d",t.uiSourceCode.url(),t.lineNumber+1);s.set(i,t)}const n=[...s.keys()].sort(),r=new t.ContextMenu.ContextMenu(e),a=r.defaultSection().appendSubMenuItem(y(x.revealInSourceCode),void 0,"reveal-in-source-list");for(let e=0;e<n.length;++e){const t=n[e];a.defaultSection().appendItem(t,this.revealSourceLocation.bind(this,s.get(t)),{jslogContext:"reveal-in-source"})}r.show()}revealSourceLocation(e){r.Revealer.reveal(e)}scheduleMediaQueriesUpdate(){this.isShowing()&&this.mediaThrottler.schedule(this.refetchMediaQueries.bind(this))}refetchMediaQueries(){return this.isShowing()&&this.cssModel?this.cssModel.getMediaQueries().then(this.rebuildMediaQueries.bind(this)):Promise.resolve()}squashAdjacentEqual(e){const t=[];for(let i=0;i<e.length;++i){const o=t[t.length-1];o&&o.equals(e[i])||t.push(e[i])}return t}rebuildMediaQueries(e){let t=[];for(let i=0;i<e.length;++i){const o=e[i];if(o.mediaList)for(let e=0;e<o.mediaList.length;++e){const i=o.mediaList[e],s=k.createFromMediaQuery(o,i);s&&t.push(s)}}t.sort((function(e,t){return e.compareTo(t)})),t=this.squashAdjacentEqual(t);let i=this.cachedQueryModels&&this.cachedQueryModels.length===t.length;for(let e=0;i&&e<t.length;++e)i=i&&this.cachedQueryModels&&this.cachedQueryModels[e].equals(t[e]);i||(this.cachedQueryModels=t,this.renderMediaQueries())}renderMediaQueries(){if(!this.cachedQueryModels||!this.isShowing())return;const e=[];let t=null;for(const i of this.cachedQueryModels){t&&t.model.dimensionsEqual(i)?t.active=t.active||i.active():(t={active:i.active(),model:i,locations:[]},e.push(t));const o=i.rawLocation();o&&t.locations.push(o)}this.contentElement.removeChildren();let i=null;for(let t=0;t<e.length;++t){t&&e[t].model.section()===e[t-1].model.section()||(i=this.contentElement.createChild("div","media-inspector-marker-container"));const o=e[t],s=this.createElementFromMediaQueryModel(o.model);if(this.elementsToMediaQueryModel.set(s,o.model),this.elementsToCSSLocations.set(s,o.locations),s.classList.toggle("media-inspector-marker-inactive",!o.active),!i)throw new Error("Could not find container to render media queries into.");i.appendChild(s)}}zoomFactor(){return t.ZoomManager.ZoomManager.instance().zoomFactor()/this.scale}wasShown(){super.wasShown(),this.scheduleMediaQueriesUpdate()}createElementFromMediaQueryModel(e){const i=this.zoomFactor(),o=e.minWidthExpression(),s=e.maxWidthExpression(),n=o?(o.computedLength()||0)/i:0,r=s?(s.computedLength()||0)/i:0,a=document.createElement("div");if(a.classList.add("media-inspector-bar"),0===e.section()){a.createChild("div","media-inspector-marker-spacer");const i=a.createChild("div","media-inspector-marker media-inspector-marker-max-width");i.style.width=r+"px",t.Tooltip.Tooltip.install(i,e.mediaText()),d(i,e.maxWidthExpression(),!1,!1),d(i,e.maxWidthExpression(),!0,!0),a.createChild("div","media-inspector-marker-spacer")}if(1===e.section()){a.createChild("div","media-inspector-marker-spacer");const i=a.createChild("div","media-inspector-marker media-inspector-marker-min-max-width");i.style.width=.5*(r-n)+"px",t.Tooltip.Tooltip.install(i,e.mediaText()),d(i,e.maxWidthExpression(),!0,!1),d(i,e.minWidthExpression(),!1,!0),a.createChild("div","media-inspector-marker-spacer").style.flex="0 0 "+n+"px";const o=a.createChild("div","media-inspector-marker media-inspector-marker-min-max-width");o.style.width=.5*(r-n)+"px",t.Tooltip.Tooltip.install(o,e.mediaText()),d(o,e.minWidthExpression(),!0,!1),d(o,e.maxWidthExpression(),!1,!0),a.createChild("div","media-inspector-marker-spacer")}if(2===e.section()){const i=a.createChild("div","media-inspector-marker media-inspector-marker-min-width media-inspector-marker-min-width-left");t.Tooltip.Tooltip.install(i,e.mediaText()),d(i,e.minWidthExpression(),!1,!1),a.createChild("div","media-inspector-marker-spacer").style.flex="0 0 "+n+"px";const o=a.createChild("div","media-inspector-marker media-inspector-marker-min-width media-inspector-marker-min-width-right");t.Tooltip.Tooltip.install(o,e.mediaText()),d(o,e.minWidthExpression(),!0,!0)}function d(e,t,i,o){t&&(e.createChild("div","media-inspector-marker-label-container "+(i?"media-inspector-marker-label-container-left":"media-inspector-marker-label-container-right")).createChild("span","media-inspector-marker-label "+(o?"media-inspector-label-left":"media-inspector-label-right")).textContent=t.value()+t.unit())}return a}}class k{cssMedia;minWidthExpressionInternal;maxWidthExpressionInternal;activeInternal;sectionInternal;rawLocationInternal;constructor(e,t,i,o){this.cssMedia=e,this.minWidthExpressionInternal=t,this.maxWidthExpressionInternal=i,this.activeInternal=o,this.sectionInternal=i&&!t?0:t&&i?1:2}static createFromMediaQuery(e,t){let i=null,o=Number.MAX_VALUE,s=null,n=Number.MIN_VALUE;const r=t.expressions();if(!r)return null;for(let e=0;e<r.length;++e){const t=r[e],a=t.feature();if(-1===a.indexOf("width"))continue;const d=t.computedLength();a.startsWith("max-")&&d&&d<o?(i=t,o=d):a.startsWith("min-")&&d&&d>n&&(s=t,n=d)}return n>o||!i&&!s?null:new k(e,s,i,t.active())}equals(e){return 0===this.compareTo(e)}dimensionsEqual(e){const t=this.minWidthExpression(),i=e.minWidthExpression(),o=this.maxWidthExpression(),s=e.maxWidthExpression(),n=this.section()===e.section(),r=!t||t.computedLength()===i?.computedLength(),a=!o||o.computedLength()===s?.computedLength();return n&&r&&a}compareTo(e){if(this.section()!==e.section())return this.section()-e.section();if(this.dimensionsEqual(e)){const t=this.rawLocation(),i=e.rawLocation();return t||i?t&&!i?1:!t&&i?-1:this.active()!==e.active()?this.active()?-1:1:t&&i?d.StringUtilities.compare(t.url,i.url)||t.lineNumber-i.lineNumber||t.columnNumber-i.columnNumber:0:d.StringUtilities.compare(this.mediaText(),e.mediaText())}const t=this.maxWidthExpression(),i=e.maxWidthExpression(),o=t&&t.computedLength()||0,s=i&&i.computedLength()||0,n=this.minWidthExpression(),r=e.minWidthExpression(),a=n&&n.computedLength()||0,l=r&&r.computedLength()||0;return 0===this.section()?s-o:2===this.section()?a-l:a-l||s-o}section(){return this.sectionInternal}mediaText(){return this.cssMedia.text||""}rawLocation(){return this.rawLocationInternal||(this.rawLocationInternal=this.cssMedia.rawLocation()),this.rawLocationInternal}minWidthExpression(){return this.minWidthExpressionInternal}maxWidthExpression(){return this.maxWidthExpressionInternal}active(){return this.activeInternal}}var T=Object.freeze({__proto__:null,MediaQueryInspector:C,MediaQueryUIModel:k});const E={doubleclickForFullHeight:"Double-click for full height",mobileS:"Mobile S",mobileM:"Mobile M",mobileL:"Mobile L",tablet:"Tablet",laptop:"Laptop",laptopL:"Laptop L"},D=a.i18n.registerUIStrings("panels/emulation/DeviceModeView.ts",E),R=a.i18n.getLocalizedString.bind(void 0,D);class z extends t.Widget.VBox{wrapperInstance;blockElementToWidth;model;mediaInspector;showMediaInspectorSetting;showRulersSetting;topRuler;leftRuler;presetBlocks;responsivePresetsContainer;screenArea;pageArea;outlineImage;contentClip;contentArea;rightResizerElement;leftResizerElement;bottomResizerElement;bottomRightResizerElement;bottomLeftResizerElement;cachedResizable;mediaInspectorContainer;screenImage;toolbar;slowPositionStart;resizeStart;cachedCssScreenRect;cachedCssVisiblePageRect;cachedOutlineRect;cachedMediaInspectorVisible;cachedShowRulers;cachedScale;handleWidth;handleHeight;constructor(){super(!0),this.blockElementToWidth=new WeakMap,this.setMinimumSize(150,150),this.element.classList.add("device-mode-view"),this.registerRequiredCSS(w),this.model=n.DeviceModeModel.DeviceModeModel.instance(),this.model.addEventListener("Updated",this.updateUI,this),this.mediaInspector=new C((()=>this.model.appliedDeviceSize().width),this.model.setWidth.bind(this.model),new r.Throttler.Throttler(0)),this.showMediaInspectorSetting=r.Settings.Settings.instance().moduleSetting("show-media-query-inspector"),this.showMediaInspectorSetting.addChangeListener(this.updateUI,this),this.showRulersSetting=r.Settings.Settings.instance().moduleSetting("emulation.show-rulers"),this.showRulersSetting.addChangeListener(this.updateUI,this),this.topRuler=new L(!0,this.model.setWidthAndScaleToFit.bind(this.model)),this.topRuler.element.classList.add("device-mode-ruler-top"),this.leftRuler=new L(!1,this.model.setHeightAndScaleToFit.bind(this.model)),this.leftRuler.element.classList.add("device-mode-ruler-left"),this.createUI(),t.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.zoomChanged,this)}createUI(){this.toolbar=new S(this.model,this.showMediaInspectorSetting,this.showRulersSetting),this.contentElement.appendChild(this.toolbar.element()),this.contentClip=this.contentElement.createChild("div","device-mode-content-clip vbox"),this.responsivePresetsContainer=this.contentClip.createChild("div","device-mode-presets-container"),this.responsivePresetsContainer.setAttribute("jslog",`${l.responsivePresets()}`),this.populatePresetsContainer(),this.mediaInspectorContainer=this.contentClip.createChild("div","device-mode-media-container"),this.contentArea=this.contentClip.createChild("div","device-mode-content-area"),this.outlineImage=this.contentArea.createChild("img","device-mode-outline-image hidden fill"),this.outlineImage.addEventListener("load",this.onImageLoaded.bind(this,this.outlineImage,!0),!1),this.outlineImage.addEventListener("error",this.onImageLoaded.bind(this,this.outlineImage,!1),!1),this.screenArea=this.contentArea.createChild("div","device-mode-screen-area"),this.screenImage=this.screenArea.createChild("img","device-mode-screen-image hidden"),this.screenImage.addEventListener("load",this.onImageLoaded.bind(this,this.screenImage,!0),!1),this.screenImage.addEventListener("error",this.onImageLoaded.bind(this,this.screenImage,!1),!1),this.bottomRightResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-right-resizer"),this.bottomRightResizerElement.createChild("div",""),this.createResizer(this.bottomRightResizerElement,2,1),this.bottomLeftResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-left-resizer"),this.bottomLeftResizerElement.createChild("div",""),this.createResizer(this.bottomLeftResizerElement,-2,1),this.rightResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-right-resizer"),this.rightResizerElement.createChild("div",""),this.createResizer(this.rightResizerElement,2,0),this.leftResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-left-resizer"),this.leftResizerElement.createChild("div",""),this.createResizer(this.leftResizerElement,-2,0),this.bottomResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-resizer"),this.bottomResizerElement.createChild("div",""),this.createResizer(this.bottomResizerElement,0,1),this.bottomResizerElement.addEventListener("dblclick",this.model.setHeight.bind(this.model,0),!1),t.Tooltip.Tooltip.install(this.bottomResizerElement,R(E.doubleclickForFullHeight)),this.pageArea=this.screenArea.createChild("div","device-mode-page-area"),this.pageArea.createChild("slot")}populatePresetsContainer(){const e=[320,375,425,768,1024,1440,2560],t=[R(E.mobileS),R(E.mobileM),R(E.mobileL),R(E.tablet),R(E.laptop),R(E.laptopL),"4K"];this.presetBlocks=[];const i=this.responsivePresetsContainer.createChild("div","device-mode-presets-container-inner");for(let s=e.length-1;s>=0;--s){const n=i.createChild("div","fill device-mode-preset-bar-outer").createChild("div","device-mode-preset-bar");n.createChild("span").textContent=t[s]+" – "+e[s]+"px",n.setAttribute("jslog",`${l.action().track({click:!0}).context(`device-mode-preset-${e[s]}px`)}`),n.addEventListener("click",o.bind(this,e[s]),!1),this.blockElementToWidth.set(n,e[s]),this.presetBlocks.push(n)}function o(e,t){this.model.emulate(n.DeviceModeModel.Type.Responsive,null,null),this.model.setWidthAndScaleToFit(e),t.consume()}}createResizer(e,i,o){const s=new t.ResizerWidget.ResizerWidget;e.setAttribute("jslog",`${l.slider("device-mode-resizer").track({drag:!0})}`),s.addElement(e);let n=i?"ew-resize":"ns-resize";return i*o>0&&(n="nwse-resize"),i*o<0&&(n="nesw-resize"),s.setCursor(n),s.addEventListener("ResizeStart",this.onResizeStart,this),s.addEventListener("ResizeUpdateXY",this.onResizeUpdate.bind(this,i,o)),s.addEventListener("ResizeEnd",this.onResizeEnd,this),s}onResizeStart(){this.slowPositionStart=null;const e=this.model.screenRect();this.resizeStart=new t.Geometry.Size(e.width,e.height)}onResizeUpdate(e,i,o){o.data.shiftKey!==Boolean(this.slowPositionStart)&&(this.slowPositionStart=o.data.shiftKey?{x:o.data.currentX,y:o.data.currentY}:null);let s=o.data.currentX-o.data.startX,r=o.data.currentY-o.data.startY;if(this.slowPositionStart&&(s=(o.data.currentX-this.slowPositionStart.x)/10+this.slowPositionStart.x-o.data.startX,r=(o.data.currentY-this.slowPositionStart.y)/10+this.slowPositionStart.y-o.data.startY),e&&this.resizeStart){const i=s*t.ZoomManager.ZoomManager.instance().zoomFactor();let o=this.resizeStart.width+i*e;o=Math.round(o/this.model.scale()),o>=n.DeviceModeModel.MinDeviceSize&&o<=n.DeviceModeModel.MaxDeviceSize&&this.model.setWidth(o)}if(i&&this.resizeStart){const e=r*t.ZoomManager.ZoomManager.instance().zoomFactor();let o=this.resizeStart.height+e*i;o=Math.round(o/this.model.scale()),o>=n.DeviceModeModel.MinDeviceSize&&o<=n.DeviceModeModel.MaxDeviceSize&&this.model.setHeight(o)}}exitHingeMode(){this.model&&this.model.exitHingeMode()}onResizeEnd(){delete this.resizeStart,e.userMetrics.actionTaken(e.UserMetrics.Action.ResizedViewInResponsiveMode)}updateUI(){function e(e,t){e.style.left=t.left+"px",e.style.top=t.top+"px",e.style.width=t.width+"px",e.style.height=t.height+"px"}if(!this.isShowing())return;const i=t.ZoomManager.ZoomManager.instance().zoomFactor();let o=!1;const s=this.showRulersSetting.get()&&this.model.type()!==n.DeviceModeModel.Type.None;let r=!1,a=!1;const d=this.model.screenRect().scale(1/i);this.cachedCssScreenRect&&d.isEqual(this.cachedCssScreenRect)||(e(this.screenArea,d),a=!0,o=!0,this.cachedCssScreenRect=d);const l=this.model.visiblePageRect().scale(1/i);this.cachedCssVisiblePageRect&&l.isEqual(this.cachedCssVisiblePageRect)||(e(this.pageArea,l),o=!0,this.cachedCssVisiblePageRect=l);const c=this.model.outlineRect();if(c){const t=c.scale(1/i);this.cachedOutlineRect&&t.isEqual(this.cachedOutlineRect)||(e(this.outlineImage,t),o=!0,this.cachedOutlineRect=t)}this.contentClip.classList.toggle("device-mode-outline-visible",Boolean(this.model.outlineImage()));const h=this.model.type()===n.DeviceModeModel.Type.Responsive;h!==this.cachedResizable&&(this.rightResizerElement.classList.toggle("hidden",!h),this.leftResizerElement.classList.toggle("hidden",!h),this.bottomResizerElement.classList.toggle("hidden",!h),this.bottomRightResizerElement.classList.toggle("hidden",!h),this.bottomLeftResizerElement.classList.toggle("hidden",!h),this.cachedResizable=h);const m=this.showMediaInspectorSetting.get()&&this.model.type()!==n.DeviceModeModel.Type.None;if(m!==this.cachedMediaInspectorVisible&&(m?this.mediaInspector.show(this.mediaInspectorContainer):this.mediaInspector.detach(),r=!0,o=!0,this.cachedMediaInspectorVisible=m),s!==this.cachedShowRulers&&(this.contentClip.classList.toggle("device-mode-rulers-visible",s),s?(this.topRuler.show(this.contentArea),this.leftRuler.show(this.contentArea)):(this.topRuler.detach(),this.leftRuler.detach()),r=!0,o=!0,this.cachedShowRulers=s),this.model.scale()!==this.cachedScale){a=!0,o=!0;for(const e of this.presetBlocks){const t=this.blockElementToWidth.get(e);if(!t)throw new Error("Could not get width for block.");e.style.width=t*this.model.scale()+"px"}this.cachedScale=this.model.scale()}this.toolbar.update(),this.loadImage(this.screenImage,this.model.screenImage()),this.loadImage(this.outlineImage,this.model.outlineImage()),this.mediaInspector.setAxisTransform(this.model.scale()),o&&this.doResize(),a&&(this.topRuler.render(this.model.scale()),this.leftRuler.render(this.model.scale()),this.topRuler.element.positionAt(this.cachedCssScreenRect?this.cachedCssScreenRect.left:0,this.cachedCssScreenRect?this.cachedCssScreenRect.top:0),this.leftRuler.element.positionAt(this.cachedCssScreenRect?this.cachedCssScreenRect.left:0,this.cachedCssScreenRect?this.cachedCssScreenRect.top:0)),r&&this.contentAreaResized()}loadImage(e,t){e.getAttribute("srcset")!==t&&(e.setAttribute("srcset",t),t||e.classList.toggle("hidden",!0))}onImageLoaded(e,t){e.classList.toggle("hidden",!t)}setNonEmulatedAvailableSize(e){if(this.model.type()!==n.DeviceModeModel.Type.None)return;const i=t.ZoomManager.ZoomManager.instance().zoomFactor(),o=e.getBoundingClientRect(),s=new t.Geometry.Size(Math.max(o.width*i,1),Math.max(o.height*i,1));this.model.setAvailableSize(s,s)}contentAreaResized(){const e=t.ZoomManager.ZoomManager.instance().zoomFactor(),i=this.contentArea.getBoundingClientRect(),o=new t.Geometry.Size(Math.max(i.width*e,1),Math.max(i.height*e,1)),s=new t.Geometry.Size(Math.max((i.width-2*(this.handleWidth||0))*e,1),Math.max((i.height-(this.handleHeight||0))*e,1));this.model.setAvailableSize(o,s)}measureHandles(){const e=this.rightResizerElement.classList.contains("hidden");this.rightResizerElement.classList.toggle("hidden",!1),this.bottomResizerElement.classList.toggle("hidden",!1),this.handleWidth=this.rightResizerElement.offsetWidth,this.handleHeight=this.bottomResizerElement.offsetHeight,this.rightResizerElement.classList.toggle("hidden",e),this.bottomResizerElement.classList.toggle("hidden",e)}zoomChanged(){delete this.handleWidth,delete this.handleHeight,this.isShowing()&&(this.measureHandles(),this.contentAreaResized())}onResize(){this.isShowing()&&this.contentAreaResized()}wasShown(){this.measureHandles(),this.toolbar.restore()}willHide(){this.model.emulate(n.DeviceModeModel.Type.None,null,null)}async captureScreenshot(){const e=await this.model.captureScreenshot(!1);if(null===e)return;const t=new Image;t.src="data:image/png;base64,"+e,t.onload=async()=>{const e=t.naturalWidth/this.model.screenRect().width,i=this.model.outlineRect();if(!i)throw new Error("Unable to take screenshot: no outlineRect available.");const o=i.scale(e),s=this.model.screenRect().scale(e),n=this.model.visiblePageRect().scale(e),r=s.left+n.left-o.left,a=s.top+n.top-o.top,d=document.createElement("canvas");d.width=Math.floor(o.width),d.height=Math.min(16384,Math.floor(o.height));const l=d.getContext("2d");if(!l)throw new Error("Could not get 2d context from canvas.");l.imageSmoothingEnabled=!1,this.model.outlineImage()&&await this.paintImage(l,this.model.outlineImage(),o.relativeTo(o)),this.model.screenImage()&&await this.paintImage(l,this.model.screenImage(),s.relativeTo(o)),l.drawImage(t,Math.floor(r),Math.floor(a)),this.saveScreenshot(d)}}async captureFullSizeScreenshot(){const e=await this.model.captureScreenshot(!0);if(null!==e)return this.saveScreenshotBase64(e)}async captureAreaScreenshot(e){const t=await this.model.captureScreenshot(!1,e);if(null!==t)return this.saveScreenshotBase64(t)}saveScreenshotBase64(e){const t=new Image;t.src="data:image/png;base64,"+e,t.onload=()=>{const e=document.createElement("canvas");e.width=t.naturalWidth,e.height=Math.min(16384,Math.floor(t.naturalHeight));const i=e.getContext("2d");if(!i)throw new Error("Could not get 2d context for base64 screenshot.");i.imageSmoothingEnabled=!1,i.drawImage(t,0,0),this.saveScreenshot(e)}}paintImage(e,t,i){return new Promise((o=>{const s=new Image;s.crossOrigin="Anonymous",s.srcset=t,s.onerror=()=>o(),s.onload=()=>{e.drawImage(s,i.left,i.top,i.width,i.height),o()}}))}saveScreenshot(e){const t=this.model.inspectedURL();let i="";if(t){const e=d.StringUtilities.removeURLFragment(t);i=d.StringUtilities.trimURL(e)}const o=this.model.device();o&&this.model.type()===n.DeviceModeModel.Type.Device&&(i+=`(${o.title})`);const s=document.createElement("a");s.download=i+".png",e.toBlob((e=>{null!==e&&(s.href=URL.createObjectURL(e),s.click())}))}}class L extends t.Widget.VBox{contentElementInternal;horizontal;scale;count;throttler;applyCallback;renderedScale;renderedZoomFactor;constructor(e,t){super(),this.element.classList.add("device-mode-ruler"),this.element.setAttribute("jslog",`${l.deviceModeRuler().track({click:!0})}`),this.contentElementInternal=this.element.createChild("div","device-mode-ruler-content").createChild("div","device-mode-ruler-inner"),this.horizontal=e,this.scale=1,this.count=0,this.throttler=new r.Throttler.Throttler(0),this.applyCallback=t}render(e){this.scale=e,this.throttler.schedule(this.update.bind(this))}onResize(){this.throttler.schedule(this.update.bind(this))}update(){const e=t.ZoomManager.ZoomManager.instance().zoomFactor(),i=this.horizontal?this.contentElementInternal.offsetWidth:this.contentElementInternal.offsetHeight;this.scale===this.renderedScale&&e===this.renderedZoomFactor||(this.contentElementInternal.removeChildren(),this.count=0,this.renderedScale=this.scale,this.renderedZoomFactor=e);const o=i*e/this.scale,s=Math.ceil(o/5);let n=1;this.scale<.8&&(n=2),this.scale<.6&&(n=4),this.scale<.4&&(n=8),this.scale<.2&&(n=16),this.scale<.1&&(n=32);for(let e=s;e<this.count;e++)if(!(e%n)){const e=this.contentElementInternal.lastChild;e&&e.remove()}for(let t=this.count;t<s;t++){if(t%n)continue;const i=this.contentElementInternal.createChild("div","device-mode-ruler-marker");if(t&&(this.horizontal?i.style.left=5*t*this.scale/e+"px":i.style.top=5*t*this.scale/e+"px",!(t%20))){const e=i.createChild("div","device-mode-ruler-text");e.textContent=String(5*t),e.addEventListener("click",this.onMarkerClick.bind(this,5*t),!1)}t%10?t%5||i.classList.add("device-mode-ruler-marker-medium"):i.classList.add("device-mode-ruler-marker-large")}return this.count=s,Promise.resolve()}onMarkerClick(e){this.applyCallback.call(null,e)}}var W=Object.freeze({__proto__:null,DeviceModeView:z,Ruler:L});let P;class A extends t.Widget.VBox{inspectedPagePlaceholder;deviceModeView;toggleDeviceModeAction;showDeviceModeSetting;constructor(e){super(),this.inspectedPagePlaceholder=e,this.deviceModeView=null,this.toggleDeviceModeAction=t.ActionRegistry.ActionRegistry.instance().getAction("emulation.toggle-device-mode");const i=n.DeviceModeModel.DeviceModeModel.instance();this.showDeviceModeSetting=i.enabledSetting(),this.showDeviceModeSetting.setRequiresUserAction(Boolean(o.Runtime.Runtime.queryParam("hasOtherClients"))),this.showDeviceModeSetting.addChangeListener(this.update.bind(this,!1)),s.TargetManager.TargetManager.instance().addModelListener(s.OverlayModel.OverlayModel,"ScreenshotRequested",this.screenshotRequestedFromOverlay,this),this.update(!0)}static instance(e={forceNew:null,inspectedPagePlaceholder:null}){const{forceNew:t,inspectedPagePlaceholder:i}=e;if(!P||t){if(!i)throw new Error(`Unable to create DeviceModeWrapper: inspectedPagePlaceholder must be provided: ${(new Error).stack}`);P=new A(i)}return P}toggleDeviceMode(){this.showDeviceModeSetting.set(!this.showDeviceModeSetting.get())}isDeviceModeOn(){return this.showDeviceModeSetting.get()}captureScreenshot(e,t){return this.deviceModeView||(this.deviceModeView=new z),this.deviceModeView.setNonEmulatedAvailableSize(this.inspectedPagePlaceholder.element),e?this.deviceModeView.captureFullSizeScreenshot():t?this.deviceModeView.captureAreaScreenshot(t):this.deviceModeView.captureScreenshot(),!0}screenshotRequestedFromOverlay(e){const t=e.data;this.captureScreenshot(!1,t)}update(e){if(this.toggleDeviceModeAction.setToggled(this.showDeviceModeSetting.get()),!e){const e=this.deviceModeView&&this.deviceModeView.isShowing();if(this.showDeviceModeSetting.get()===e)return}this.showDeviceModeSetting.get()?(this.deviceModeView||(this.deviceModeView=new z),this.deviceModeView.show(this.element),this.inspectedPagePlaceholder.clearMinimumSize(),this.inspectedPagePlaceholder.show(this.deviceModeView.element)):(this.deviceModeView&&(this.deviceModeView.exitHingeMode(),this.deviceModeView.detach()),this.inspectedPagePlaceholder.restoreMinimumSize(),this.inspectedPagePlaceholder.show(this.element))}}var F=Object.freeze({__proto__:null,DeviceModeWrapper:A,ActionDelegate:class{handleAction(e,t){switch(t){case"emulation.capture-screenshot":return A.instance().captureScreenshot();case"emulation.capture-node-screenshot":{const i=e.flavor(s.DOMModel.DOMNode);if(!i)return!0;async function o(){if(!i)return;const e=await i.resolveToObject();if(!e)return;const t=await e.callFunction((function(){const e=this.getBoundingClientRect(),t=this.ownerDocument.documentElement.getBoundingClientRect();return JSON.stringify({x:e.left-t.left,y:e.top-t.top,width:e.width,height:e.height,scale:1})}));if(!t.object)throw new Error("Clipping error: could not get object data.");const o=JSON.parse(t.object.value),s=await i.domModel().target().pageAgent().invoke_getLayoutMetrics(),n=!s.getError()&&s.visualViewport.zoom||1;o.x*=n,o.y*=n,o.width*=n,o.height*=n,A.instance().captureScreenshot(!1,o)}return o(),!0}case"emulation.capture-full-height-screenshot":return A.instance().captureScreenshot(!0);case"emulation.toggle-device-mode":return A.instance().toggleDeviceMode(),!0}return!1}}}),V={cssContent:":host{background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=inspectedPagePlaceholder.css */\n"};let U;class B extends(r.ObjectWrapper.eventMixin(t.Widget.Widget)){updateId;constructor(){super(!0),this.registerRequiredCSS(V),t.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.onResize,this),this.restoreMinimumSize()}static instance(e={forceNew:null}){const{forceNew:t}=e;return U&&!t||(U=new B),U}onResize(){this.updateId&&this.element.window().cancelAnimationFrame(this.updateId),this.updateId=this.element.window().requestAnimationFrame(this.update.bind(this,!1))}restoreMinimumSize(){this.setMinimumSize(150,150)}clearMinimumSize(){this.setMinimumSize(1,1)}dipPageRect(){const e=t.ZoomManager.ZoomManager.instance().zoomFactor(),i=this.element.getBoundingClientRect(),o=this.element.ownerDocument.body.getBoundingClientRect(),s=Math.max(i.left*e,o.left*e),n=Math.max(i.top*e,o.top*e),r=Math.min(i.bottom*e,o.bottom*e);return{x:s,y:n,width:Math.min(i.right*e,o.right*e)-s,height:r-n}}update(e){delete this.updateId;const t=this.dipPageRect(),i={x:Math.round(t.x),y:Math.round(t.y),height:Math.max(1,Math.round(t.height)),width:Math.max(1,Math.round(t.width))};e&&(--i.height,this.dispatchEventToListeners("Update",i),++i.height),this.dispatchEventToListeners("Update",i)}}var j=Object.freeze({__proto__:null,InspectedPagePlaceholder:B});let Q,H;class O{rootSplitWidget;deviceModeView;inspectedPagePlaceholder;toolboxWindow;toolboxRootView;changingDockSide;toolboxDocument;constructor(){t.DockController.DockController.instance().addEventListener("BeforeDockSideChanged",this.openToolboxWindow,this)}static instance(){return Q||(Q=new O),Q}presentUI(e){const i=new t.RootView.RootView;this.rootSplitWidget=new t.SplitWidget.SplitWidget(!1,!0,"inspector-view.split-view-state",555,300,!0),this.rootSplitWidget.show(i.element),this.rootSplitWidget.setSidebarWidget(t.InspectorView.InspectorView.instance()),this.rootSplitWidget.setDefaultFocusedChild(t.InspectorView.InspectorView.instance()),t.InspectorView.InspectorView.instance().setOwnerSplit(this.rootSplitWidget),this.inspectedPagePlaceholder=B.instance(),this.inspectedPagePlaceholder.addEventListener("Update",this.onSetInspectedPageBounds.bind(this),this),this.deviceModeView=A.instance({inspectedPagePlaceholder:this.inspectedPagePlaceholder,forceNew:!1}),t.DockController.DockController.instance().addEventListener("BeforeDockSideChanged",this.onBeforeDockSideChange,this),t.DockController.DockController.instance().addEventListener("DockSideChanged",this.onDockSideChange,this),t.DockController.DockController.instance().addEventListener("AfterDockSideChanged",this.onAfterDockSideChange,this),this.onDockSideChange(),console.timeStamp("AdvancedApp.attachToBody"),i.attachToDocument(e),i.focus(),this.inspectedPagePlaceholder.update()}openToolboxWindow(e){if("undocked"!==e.data.to)return;if(this.toolboxWindow)return;const t=window.location.href.replace("devtools_app.html","device_mode_emulation_frame.html");this.toolboxWindow=window.open(t,void 0)}deviceModeEmulationFrameLoaded(e){i.ThemeSupport.instance().addDocumentToTheme(e),t.UIUtils.initializeUIUtils(e),t.UIUtils.installComponentRootStyles(e.body),t.ContextMenu.ContextMenu.installHandler(e),this.toolboxRootView=new t.RootView.RootView,this.toolboxRootView.attachToDocument(e),this.toolboxDocument=e,this.updateDeviceModeView()}updateDeviceModeView(){this.isDocked()?this.rootSplitWidget.setMainWidget(this.deviceModeView):this.toolboxRootView&&this.deviceModeView.show(this.toolboxRootView.element)}onBeforeDockSideChange(e){"undocked"===e.data.to&&this.toolboxRootView&&(this.rootSplitWidget.hideSidebar(),this.inspectedPagePlaceholder.update()),this.changingDockSide=!0}onDockSideChange(e){this.updateDeviceModeView();const i=e?e.data.to:t.DockController.DockController.instance().dockSide();if(void 0===i)throw new Error("Got onDockSideChange event with unexpected undefined for dockSide()");"undocked"===i?this.updateForUndocked():this.toolboxRootView&&e&&"undocked"===e.data.from?this.rootSplitWidget.hideSidebar():this.updateForDocked(i)}onAfterDockSideChange(e){this.changingDockSide&&(e.data.from&&"undocked"===e.data.from&&this.updateForDocked(e.data.to),this.changingDockSide=!1,this.inspectedPagePlaceholder.update())}updateForDocked(e){this.rootSplitWidget.resizerElement().style.transform="right"===e?"translateX(2px)":"left"===e?"translateX(-2px)":"",this.rootSplitWidget.setVertical("right"===e||"left"===e),this.rootSplitWidget.setSecondIsSidebar("right"===e||"bottom"===e),this.rootSplitWidget.toggleResizer(this.rootSplitWidget.resizerElement(),!0),this.rootSplitWidget.toggleResizer(t.InspectorView.InspectorView.instance().topResizerElement(),"bottom"===e),this.rootSplitWidget.showBoth()}updateForUndocked(){this.rootSplitWidget.toggleResizer(this.rootSplitWidget.resizerElement(),!1),this.rootSplitWidget.toggleResizer(t.InspectorView.InspectorView.instance().topResizerElement(),!1),this.rootSplitWidget.hideMain()}isDocked(){return"undocked"!==t.DockController.DockController.instance().dockSide()}onSetInspectedPageBounds(t){if(this.changingDockSide)return;const i=this.inspectedPagePlaceholder.element.window();if(!i.innerWidth||!i.innerHeight)return;if(!this.inspectedPagePlaceholder.isShowing())return;const o=t.data;console.timeStamp("AdvancedApp.setInspectedPageBounds"),e.InspectorFrontendHost.InspectorFrontendHostInstance.setInspectedPageBounds(o)}}globalThis.Emulation=globalThis.Emulation||{},globalThis.Emulation.AdvancedApp=O;class _{static instance(e={forceNew:null}){const{forceNew:t}=e;return H&&!t||(H=new _),H}createApp(){return O.instance()}}var Z=Object.freeze({__proto__:null,AdvancedApp:O,AdvancedAppProvider:_});export{Z as AdvancedApp,f as DeviceModeToolbar,W as DeviceModeView,F as DeviceModeWrapper,j as InspectedPagePlaceholder,T as MediaQueryInspector};
