{"version": 3, "names": ["encodeSvg", "getBoundingClientRect", "WebShape", "Circle", "tag", "<PERSON><PERSON><PERSON><PERSON>", "Defs", "Ellipse", "FeBlend", "FeColorMatrix", "FeComponentTransfer", "FeComposite", "FeConvolveMatrix", "FeDiffuseLighting", "FeDisplacementMap", "FeDistantLight", "FeDropShadow", "FeFlood", "FeFuncA", "FeFuncB", "FeFuncG", "FeFuncR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FeImage", "FeMerge", "FeMergeNode", "FeMorphology", "FeOffset", "FePointLight", "FeSpecularLighting", "FeSpotLight", "FeTile", "FeTurbulence", "Filter", "ForeignObject", "G", "prepareProps", "props", "x", "y", "rest", "translate", "Image", "Line", "LinearGradient", "<PERSON><PERSON>", "Mask", "Path", "Pattern", "Polygon", "Polyline", "RadialGrad<PERSON>", "Rect", "Stop", "Svg", "toDataURL", "callback", "options", "ref", "elementRef", "current", "rect", "width", "Number", "height", "svg", "document", "createElementNS", "setAttribute", "String", "append<PERSON><PERSON><PERSON>", "cloneNode", "img", "window", "onload", "canvas", "createElement", "context", "getContext", "drawImage", "replace", "src", "XMLSerializer", "serializeToString", "Symbol", "TSpan", "Text", "TextPath", "Use"], "sourceRoot": "../../src", "sources": ["elements.web.ts"], "mappings": "AAoDA,SAASA,SAAS,EAAEC,qBAAqB,QAAQ,aAAa;AAC9D,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,OAAO,MAAMC,MAAM,SAASD,QAAQ,CAA0B;EAC5DE,GAAG,GAAG,QAAQ;AAChB;AAEA,OAAO,MAAMC,QAAQ,SAASH,QAAQ,CAA4B;EAChEE,GAAG,GAAG,UAAU;AAClB;AAEA,OAAO,MAAME,IAAI,SAASJ,QAAQ,CAAC;EACjCE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAMG,OAAO,SAASL,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMI,OAAO,SAASN,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMK,aAAa,SAASP,QAAQ,CAAiC;EAC1EE,GAAG,GAAG,eAAe;AACvB;AAEA,OAAO,MAAMM,mBAAmB,SAASR,QAAQ,CAE/C;EACAE,GAAG,GAAG,qBAAqB;AAC7B;AAEA,OAAO,MAAMO,WAAW,SAAST,QAAQ,CAA+B;EACtEE,GAAG,GAAG,aAAa;AACrB;AAEA,OAAO,MAAMQ,gBAAgB,SAASV,QAAQ,CAE5C;EACAE,GAAG,GAAG,kBAAkB;AAC1B;AAEA,OAAO,MAAMS,iBAAiB,SAASX,QAAQ,CAE7C;EACAE,GAAG,GAAG,mBAAmB;AAC3B;AAEA,OAAO,MAAMU,iBAAiB,SAASZ,QAAQ,CAE7C;EACAE,GAAG,GAAG,mBAAmB;AAC3B;AAEA,OAAO,MAAMW,cAAc,SAASb,QAAQ,CAAkC;EAC5EE,GAAG,GAAG,gBAAgB;AACxB;AAEA,OAAO,MAAMY,YAAY,SAASd,QAAQ,CAAgC;EACxEE,GAAG,GAAG,cAAc;AACtB;AAEA,OAAO,MAAMa,OAAO,SAASf,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMc,OAAO,SAAShB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMe,OAAO,SAASjB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMgB,OAAO,SAASlB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMiB,OAAO,SAASnB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMkB,cAAc,SAASpB,QAAQ,CAAkC;EAC5EE,GAAG,GAAG,gBAAgB;AACxB;AAEA,OAAO,MAAMmB,OAAO,SAASrB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMoB,OAAO,SAAStB,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAMqB,WAAW,SAASvB,QAAQ,CAA+B;EACtEE,GAAG,GAAG,aAAa;AACrB;AAEA,OAAO,MAAMsB,YAAY,SAASxB,QAAQ,CAAgC;EACxEE,GAAG,GAAG,cAAc;AACtB;AAEA,OAAO,MAAMuB,QAAQ,SAASzB,QAAQ,CAA4B;EAChEE,GAAG,GAAG,UAAU;AAClB;AAEA,OAAO,MAAMwB,YAAY,SAAS1B,QAAQ,CAAgC;EACxEE,GAAG,GAAG,cAAc;AACtB;AAEA,OAAO,MAAMyB,kBAAkB,SAAS3B,QAAQ,CAE9C;EACAE,GAAG,GAAG,oBAAoB;AAC5B;AAEA,OAAO,MAAM0B,WAAW,SAAS5B,QAAQ,CAA+B;EACtEE,GAAG,GAAG,aAAa;AACrB;AAEA,OAAO,MAAM2B,MAAM,SAAS7B,QAAQ,CAA0B;EAC5DE,GAAG,GAAG,QAAQ;AAChB;AAEA,OAAO,MAAM4B,YAAY,SAAS9B,QAAQ,CAAgC;EACxEE,GAAG,GAAG,cAAc;AACtB;AAEA,OAAO,MAAM6B,MAAM,SAAS/B,QAAQ,CAA0B;EAC5DE,GAAG,GAAG,QAAQ;AAChB;AAEA,OAAO,MAAM8B,aAAa,SAAShC,QAAQ,CAAiC;EAC1EE,GAAG,GAAG,eAAe;AACvB;AAEA,OAAO,MAAM+B,CAAC,SAASjC,QAAQ,CAAqB;EAClDE,GAAG,GAAG,GAAG;EACTgC,YAAYA,CAACC,KAAyB,EAAE;IACtC,MAAM;MAAEC,CAAC;MAAEC,CAAC;MAAE,GAAGC;IAAK,CAAC,GAAGH,KAAK;IAE/B,IAAI,CAACC,CAAC,IAAIC,CAAC,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE;MAC/BD,IAAI,CAACC,SAAS,GAAG,GAAGH,CAAC,IAAI,CAAC,KAAKC,CAAC,IAAI,CAAC,EAAE;IACzC;IAEA,OAAOC,IAAI;EACb;AACF;AAEA,OAAO,MAAME,KAAK,SAASxC,QAAQ,CAAyB;EAC1DE,GAAG,GAAG,OAAO;AACf;AAEA,OAAO,MAAMuC,IAAI,SAASzC,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAMwC,cAAc,SAAS1C,QAAQ,CAAkC;EAC5EE,GAAG,GAAG,gBAAgB;AACxB;AAEA,OAAO,MAAMyC,MAAM,SAAS3C,QAAQ,CAA0B;EAC5DE,GAAG,GAAG,QAAQ;AAChB;AAEA,OAAO,MAAM0C,IAAI,SAAS5C,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAM2C,IAAI,SAAS7C,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAM4C,OAAO,SAAS9C,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAM6C,OAAO,SAAS/C,QAAQ,CAA2B;EAC9DE,GAAG,GAAG,SAAS;AACjB;AAEA,OAAO,MAAM8C,QAAQ,SAAShD,QAAQ,CAA4B;EAChEE,GAAG,GAAG,UAAU;AAClB;AAEA,OAAO,MAAM+C,cAAc,SAASjD,QAAQ,CAAkC;EAC5EE,GAAG,GAAG,gBAAgB;AACxB;AAEA,OAAO,MAAMgD,IAAI,SAASlD,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAMiD,IAAI,SAASnD,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAMkD,GAAG,SAASpD,QAAQ,CAAuB;EACtDE,GAAG,GAAG,KAAK;EACXmD,SAASA,CACPC,QAAgC,EAChCC,OAA4C,GAAG,CAAC,CAAC,EACjD;IACA,MAAMC,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,OAAO;IAEnC,IAAIF,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,MAAMG,IAAI,GAAG5D,qBAAqB,CAACyD,GAAG,CAAC;IAEvC,MAAMI,KAAK,GAAGC,MAAM,CAACN,OAAO,CAACK,KAAK,CAAC,IAAID,IAAI,CAACC,KAAK;IACjD,MAAME,MAAM,GAAGD,MAAM,CAACN,OAAO,CAACO,MAAM,CAAC,IAAIH,IAAI,CAACG,MAAM;IAEpD,MAAMC,GAAG,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;IACzEF,GAAG,CAACG,YAAY,CAAC,SAAS,EAAE,OAAOP,IAAI,CAACC,KAAK,IAAID,IAAI,CAACG,MAAM,EAAE,CAAC;IAC/DC,GAAG,CAACG,YAAY,CAAC,OAAO,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;IACxCG,GAAG,CAACG,YAAY,CAAC,QAAQ,EAAEC,MAAM,CAACL,MAAM,CAAC,CAAC;IAC1CC,GAAG,CAACK,WAAW,CAACZ,GAAG,CAACa,SAAS,CAAC,IAAI,CAAC,CAAC;IAEpC,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAAC/B,KAAK,CAAC,CAAC;IAC9B8B,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB,MAAMC,MAAM,GAAGT,QAAQ,CAACU,aAAa,CAAC,QAAQ,CAAC;MAC/CD,MAAM,CAACb,KAAK,GAAGA,KAAK;MACpBa,MAAM,CAACX,MAAM,GAAGA,MAAM;MACtB,MAAMa,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;MACvCD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,SAAS,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7BhB,QAAQ,CAACmB,MAAM,CAACpB,SAAS,CAAC,CAAC,CAACyB,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;IACpE,CAAC;IAEDR,GAAG,CAACS,GAAG,GAAG,2BAA2BjF,SAAS,CAC5C,IAAIyE,MAAM,CAACS,aAAa,CAAC,CAAC,CAACC,iBAAiB,CAAClB,GAAG,CAClD,CAAC,EAAE;EACL;AACF;AAEA,OAAO,MAAMmB,MAAM,SAASlF,QAAQ,CAA0B;EAC5DE,GAAG,GAAG,QAAQ;AAChB;AAEA,OAAO,MAAMiF,KAAK,SAASnF,QAAQ,CAAyB;EAC1DE,GAAG,GAAG,OAAO;AACf;AAEA,OAAO,MAAMkF,IAAI,SAASpF,QAAQ,CAAwB;EACxDE,GAAG,GAAG,MAAM;AACd;AAEA,OAAO,MAAMmF,QAAQ,SAASrF,QAAQ,CAA4B;EAChEE,GAAG,GAAG,UAAU;AAClB;AAEA,OAAO,MAAMoF,GAAG,SAAStF,QAAQ,CAAuB;EACtDE,GAAG,GAAG,KAAK;AACb;AAEA,eAAekD,GAAG", "ignoreList": []}