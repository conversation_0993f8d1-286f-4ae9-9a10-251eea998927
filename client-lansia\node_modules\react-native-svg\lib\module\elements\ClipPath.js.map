{"version": 3, "names": ["React", "extract", "<PERSON><PERSON><PERSON>", "RNSVGClipPath", "<PERSON><PERSON><PERSON><PERSON>", "displayName", "render", "props", "createElement", "_extends", "ref", "refMethod", "children"], "sourceRoot": "../../../src", "sources": ["elements/ClipPath.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AACrD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,mCAAmC;AAO7D,eAAe,MAAMC,QAAQ,SAASF,KAAK,CAAgB;EACzD,OAAOG,WAAW,GAAG,UAAU;EAE/BC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,oBACEP,KAAA,CAAAQ,aAAA,CAACL,aAAa,EAAAM,QAAA;MAACC,GAAG,EAAE,IAAI,CAACC;IAAU,GAAKV,OAAO,CAAC,IAAI,EAAEM,KAAK,CAAC,GACzDA,KAAK,CAACK,QACM,CAAC;EAEpB;AACF", "ignoreList": []}