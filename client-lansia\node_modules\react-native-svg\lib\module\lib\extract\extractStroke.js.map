{"version": 3, "names": ["extractBrush", "extractOpacity", "extractLengthList", "caps", "butt", "square", "round", "joins", "miter", "bevel", "vectorEffects", "none", "default", "nonScalingStroke", "inherit", "uri", "extractStroke", "o", "props", "inherited", "stroke", "strokeOpacity", "strokeLinecap", "strokeLinejoin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeWidth", "strokeDashoffset", "strokeMiterlimit", "vectorEffect", "push", "strokeDash", "length", "concat", "parseFloat"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractStroke.ts"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD,MAAMC,IAAI,GAAG;EACXC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRH,KAAK,EAAE;AACT,CAAC;AAED,MAAMI,aAAa,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVC,gBAAgB,EAAE,CAAC;EACnB,oBAAoB,EAAE,CAAC;EACvBC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE;AACP,CAAC;AAED,eAAe,SAASC,aAAaA,CACnCC,CAAiB,EACjBC,KAAkB,EAClBC,SAAmB,EACnB;EACA,MAAM;IACJC,MAAM;IACNC,aAAa;IACbC,aAAa;IACbC,cAAc;IACdC,eAAe;IACfC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC;EACF,CAAC,GAAGV,KAAK;EAET,IAAIE,MAAM,IAAI,IAAI,EAAE;IAClBD,SAAS,CAACU,IAAI,CAAC,QAAQ,CAAC;IACxBZ,CAAC,CAACG,MAAM,GAAGpB,YAAY,CAACoB,MAAM,CAAC;EACjC;EACA,IAAIK,WAAW,IAAI,IAAI,EAAE;IACvBN,SAAS,CAACU,IAAI,CAAC,aAAa,CAAC;IAC7BZ,CAAC,CAACQ,WAAW,GAAGA,WAAW;EAC7B;EACA,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzBF,SAAS,CAACU,IAAI,CAAC,eAAe,CAAC;IAC/BZ,CAAC,CAACI,aAAa,GAAGpB,cAAc,CAACoB,aAAa,CAAC;EACjD;EACA,IAAIG,eAAe,IAAI,IAAI,EAAE;IAC3BL,SAAS,CAACU,IAAI,CAAC,iBAAiB,CAAC;IACjC,MAAMC,UAAU,GACd,CAACN,eAAe,IAAIA,eAAe,KAAK,MAAM,GAC1C,IAAI,GACJtB,iBAAiB,CAACsB,eAAe,CAAC;IACxCP,CAAC,CAACO,eAAe,GACfM,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,KAAK,CAAC,GACrCD,UAAU,CAACE,MAAM,CAACF,UAAU,CAAC,GAC7BA,UAAU;EAClB;EACA,IAAIJ,gBAAgB,IAAI,IAAI,EAAE;IAC5BP,SAAS,CAACU,IAAI,CAAC,kBAAkB,CAAC;IAClCZ,CAAC,CAACS,gBAAgB,GAChBF,eAAe,IAAIE,gBAAgB,GAAG,CAACA,gBAAgB,IAAI,CAAC,GAAG,IAAI;EACvE;EACA,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzBH,SAAS,CAACU,IAAI,CAAC,eAAe,CAAC;IAC/BZ,CAAC,CAACK,aAAa,GAAIA,aAAa,IAAInB,IAAI,CAACmB,aAAa,CAAC,IAAK,CAAC;EAC/D;EACA,IAAIC,cAAc,IAAI,IAAI,EAAE;IAC1BJ,SAAS,CAACU,IAAI,CAAC,gBAAgB,CAAC;IAChCZ,CAAC,CAACM,cAAc,GAAIA,cAAc,IAAIhB,KAAK,CAACgB,cAAc,CAAC,IAAK,CAAC;EACnE;EACA,IAAII,gBAAgB,IAAI,IAAI,EAAE;IAC5BR,SAAS,CAACU,IAAI,CAAC,kBAAkB,CAAC;IAClCZ,CAAC,CAACU,gBAAgB,GAChB,CAACA,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,GACrDM,UAAU,CAACN,gBAAgB,CAAC,GAC5BA,gBAAgB,KAAK,CAAC;EAC9B;EACA,IAAIC,YAAY,IAAI,IAAI,EAAE;IACxBX,CAAC,CAACW,YAAY,GAAIA,YAAY,IAAIlB,aAAa,CAACkB,YAAY,CAAC,IAAK,CAAC;EACrE;AACF", "ignoreList": []}