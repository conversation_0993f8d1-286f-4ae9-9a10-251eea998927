{"version": 3, "names": ["React", "withoutXY", "units", "<PERSON><PERSON><PERSON>", "RNSVGMask", "maskType", "Mask", "displayName", "defaultProps", "x", "y", "width", "height", "render", "props", "maskUnits", "maskContentUnits", "children", "style", "maskProps", "undefined", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Mask.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA6B;AAOvD,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,+BAA+B;AAErD,SAASC,QAAQ,QAAQ,iBAAiB;AAiB1C,eAAe,MAAMC,IAAI,SAASH,KAAK,CAAY;EACjD,OAAOI,WAAW,GAAG,MAAM;EAE3B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,IAAI;IACPC,CAAC,EAAE,IAAI;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MACJL,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNG,SAAS;MACTC,gBAAgB;MAChBC,QAAQ;MACRC;IACF,CAAC,GAAGJ,KAAK;IACT,MAAMK,SAAS,GAAG;MAChBV,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNG,SAAS,EAAEA,SAAS,KAAKK,SAAS,GAAGlB,KAAK,CAACa,SAAS,CAAC,GAAG,CAAC;MACzDC,gBAAgB,EACdA,gBAAgB,KAAKI,SAAS,GAAGlB,KAAK,CAACc,gBAAgB,CAAC,GAAG,CAAC;MAC9DX,QAAQ,EAAEA,QAAQ,CAAC,CAAAS,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAET,QAAQ,MAAIa,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEb,QAAQ,KAAI,WAAW;IACtE,CAAC;IACD,oBACEL,KAAA,CAAAqB,aAAA,CAACjB,SAAS,EAAAkB,QAAA;MACRC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DtB,SAAS,CAAC,IAAI,EAAEa,KAAK,CAAC,EACtBK,SAAS,GACZF,QACQ,CAAC;EAEhB;AACF", "ignoreList": []}